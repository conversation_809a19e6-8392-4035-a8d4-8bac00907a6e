import { defineConfig } from "vite";
import laravel from "laravel-vite-plugin";
import viteCompression from "vite-plugin-compression";
import { chunkSplitPlugin } from "vite-plugin-chunk-split";
import { ViteMinifyPlugin } from "vite-plugin-minify";
import * as glob from "glob";
import * as fs from "fs";

const jsFiles = glob.sync("resources/js/**/*.js");

function getModuleAssets() {
    const base = 'Modules';
    const input = [];

    fs.readdirSync(base).forEach(module => {
      const jsPath = `${base}/${module}/resources/assets/js`;
      if (fs.existsSync(jsPath)) {
        fs.readdirSync(jsPath).forEach(file => {
          if (file.endsWith('.js')) {
            input.push(`${jsPath}/${file}`);
          }
        });
      }
    });

    return input;
}

function getModuleScssAssets() {
    const base = 'Modules';
    const input = [];

    fs.readdirSync(base).forEach(module => {
      const scssPath = `${base}/${module}/resources/assets/sass`;
      if (fs.existsSync(scssPath)) {
        fs.readdirSync(scssPath).forEach(file => {
          if (file.endsWith('.scss')) {
            input.push(`${scssPath}/${file}`);
          }
        });
      }
    });

    return input;
}

export default defineConfig({
    server: {
        host: 'localhost', // Allow access from any IP address
        port: 8001,
    },
    plugins: [
        ViteMinifyPlugin({}),
        chunkSplitPlugin(),
        viteCompression(),
        laravel({
            input: [
                "resources/sass/app.scss",
                "resources/sass/components/mobile/_filter-type.scss",
                "resources/sass/components/minigame/index.scss",
                "resources/sass/components/notification/index.scss",
                "resources/js/app.js",
                "resources/js/game-filters.js",
                "resources/js/account-filter.js",
                "resources/js/mb-side-nav.js",
                "resources/js/validation.js",
                "resources/js/layout.js",
                "resources/js/datetime.js",
                ...jsFiles,
                ...getModuleAssets(),
                ...getModuleScssAssets(),
            ],
            refresh: true,
        }),
    ],
});
