/** @type {import('tailwindcss').Config} */
import accounts from './accounts.config.js';
export default {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
        "./packages/**/*.blade.php",
        "./packages/**/*.js",
        "./packages/**/config/*.php",
        "./Modules/**/*.blade.php",
        "./Modules/**/*.js",
        "./Modules/**/config/*.php",
    ],
    theme: {
        fontFamily: {
            sans: "Inter, sans-serif",
        },
        container: {
            // you can configure the container to be centered
            center: true,
            padding: {
                DEFAULT: "1rem",
                sm: "1rem",
                lg: "1rem",
                xl: "1rem",
                "2xl": "1rem",
            },
            screens: {
                sm: "100%",
                md: "100%",
                lg: "100%",
                xl: "1200px",
            },
        },
        backgroundImage: {
            "account-left-to-right-gradient":
                "linear-gradient(90deg, #FDF9F3 25.06%, #E1C081 110.58%)",
        },

        extend: {
            colors: {
                error: "#EA4D4D",
                success: "#0D8E01",
                warning: "#EE7539",
                grey: {
                    DEFAULT: "#9E9E9E",
                    50: "#FAFAFA",
                    100: "#F5F5F5",
                    200: "#EEEEEE",
                    300: "#E0E0E0",
                    400: "#BDBDBD",
                    500: "#9E9E9E",
                    600: "#757575",
                    700: "#616161",
                    800: "#424242",
                    900: "#212121",
                },
                marron: {
                    300: "#BC385C",
                    DEFAULT: "#B01E46",
                },
                neutral: {
                    white: "#FFFFFF",
                    DEFAULT: "#FFFFFF",
                    50: "#F8FAFB", // this color not use
                    100: "#D2DAD6",
                    150: "#F1F3F9", // this color not use
                    180: "#E4E9F0", // this color not use
                    200: "#AFBBB7",
                    300: "#8C9D98",
                    350: "#BFD0D7",
                    400: "#697E79",
                    450: "#63687E", // this color not use
                    500: "#465F59",
                    600: "#23413A",
                    650: "#12312B",
                    700: "#00221B",
                    750: "#001D16",
                    800: "#00120C",
                    850: "#092A21",
                    900: "#001111",
                    1000: "#002217",
                },
                primary: {
                    DEFAULT: "#6CFE00",
                    100: "#008C5B",
                    200: "#7DFF6B",
                    300: "#80D59C",
                    400: "#6CFE00",
                    500: "#02BC7B",
                    600: "#428970",
                    700: "#88A199",
                    800: "#33443D",
                    900: "#1A231F",
                    1000: "#072C20",
                },
                secondary: {
                    DEFAULT: "#F5E25C",
                    100: "#FFFBDE",
                    200: "#FCF3B3",
                    300: "#F9EB88",
                    400: "#F5E25C",
                    600: "#FFDE58",
                    700: "#E4AF21",
                    800: "#E09810",
                    900: "#DB8200"
                },
                functional: {
                    warning: "#FFF948",
                    error: "#F03535",
                    success: "#2CFFBF",
                    color: "#288DD7",
                    side: "#0AB59C",
                    yellow: "#FFE100",
                    blue: "#00BBFF",
                    yellow100: "#E5FF00"
                },
                black: {
                    DEFAULT: "#000000",
                    650: "#494C55",
                    700: "#3B3D44",
                    800: "#27272A",
                },
                alert: {
                    failed: "#FF6347",
                    complete: "#2CFFBF",
                    wait: "#FFF948",
                    loading: "#208DF2"
                },
                ...accounts.colors,
            },
            boxShadow: {
                top: '0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)',
                'inset-custom': '0px 8px 20px 0px rgba(0, 0, 0, 0.25) inset',
                'menu-shadow': '0px 4px 4px 0px #********',
            },
            screens: {
                xs: "389px",
                sms: "480px",
                xl: "1200px",
                "2xl": "1400px",
                "3xl": "1508px",
                "4xl": "1768px",
            },
            backgroundImage: {
                "card-amount-hover": "linear-gradient(0deg, #fff 100%, #fff 100%)",
                "card-amount":
                    "linear-gradient(114.48deg, #FDFEFE 9.88%, #F8FAFB 101.25%)",
                "card-amount-active":
                    "linear-gradient(100.72deg, #FFFCF6 20.5%, #FFF6E3 81.98%)",
                "secondary-gradient":
                    "linear-gradient(180deg, #0A2A1C 0%, #1A4D1D 100%)",
                "secondary-gradient-hover":
                    "linear-gradient(180deg, #0B5836 0%, #258F2B 100%)",
                "site-menu-active":
                    "linear-gradient(90deg, rgba(108, 254, 0, 0.15) 0%, rgba(108, 254, 0, 0.02) 100%)",
                "site-menu-active2":
                    "linear-gradient(0deg, #072C20, #072C20),linear-gradient(90deg, rgba(108, 254, 0, 0.15) 0%, rgba(108, 254, 0, 0.02) 100%)",
                "gradient-hot":
                    "linear-gradient(263.15deg, #FFA369 3.1%, #FFD64E 96.9%)",
                "gradient-new":
                    "linear-gradient(263.15deg, #9CE871 3.1%, #29EE89 96.9%)",
                "gradient-live":
                    "linear-gradient(263.15deg, #FF3838 3.1%, #DB0505 96.9%)",
                "gradient-hotmatch-header":
                    "linear-gradient(270deg, rgba(108, 254, 0, 0.22) 0%, rgba(108, 254, 0, 0.5) 52%, rgba(108, 254, 0, 0.22) 100%)",
                "gradient-auth-stroke":
                    "linear-gradient(118.04deg, #465F59 0.88%, #23413A 50%, #465F59 99.12%)",
                "gradient-btn-primary": "linear-gradient(93.83deg, #6CFE00 14.34%, #64FD54 51.8%, #00E33D 85.66%);",
                'radial-gradient': "radial-gradient(50% 79.81% at 50% 50%, rgba(0, 168, 36, 0.2) 0%, rgba(0, 168, 36, 0) 100%);",
                'gradient-tab-support': "url('/public/asset/images/support-tab-active.png')",
                'support-sidebar-active': "url('/public/asset/images/support-sidebar-active.png')",
                ...accounts.backgroundImage,
            },
            backgroundSize: {
                full: '100% 100%',
            },
        },
    },
    plugins: [],
    safelist: [
        'text-black-800',
        'text-neutral-600',
        'text-functional-error',
    ],
};
