import sharp from 'sharp'
import fs from 'fs-extra'
import path from 'path'

const imageInput =
    process.argv?.length && process.argv?.length > 2
        ? `/${process.argv[process.argv.length - 1]}`
        : ''


// Directory where your images are located
const inputDir = `./public/asset/images${imageInput}`
// Base directory where converted images will be saved
const outputDir = `./public/asset/images${imageInput}`

// Ensure the output directory exists
fs.ensureDirSync(outputDir)

// Function to convert and compress image to AVIF and WebP
const convertAndCompressImage = async (inputFilePath, outputFilePath) => {
    if (!path.extname(inputFilePath)?.trim()) {
        return
    }
    if (!path.extname(inputFilePath)?.match(/\.(jpg|jpeg|png|gif|svg)$/i)) {
        return
    }

    console.log(`Converted and compressed ${inputFilePath} to AVIF and WebP.`)
    const fileName = path.basename(inputFilePath, path.extname(inputFilePath))

    if (!path.extname(inputFilePath)?.match(/\.(svg)$/i)) {
        // Convert to AVIF with compression
        await sharp(inputFilePath)
            .avif() // Adjust quality as needed
            .toFile(path.join(outputFilePath, `${fileName}.avif`))

        // Convert to WebP with compression
        // await sharp(inputFilePath)
        //     .webp() // Adjust quality as needed
        //     .toFile(path.join(outputFilePath, `${fileName}.webp`))
    }

    await fs.copyFile(
        inputFilePath,
        `${outputFilePath}/${fileName}${path.extname(inputFilePath)}`
    )
}

const parseArgs = (argv) => {
    const args = {}
    argv.forEach((arg) => {
        const [key, value] = arg.split('=')
        if (key && value) {
            args[key] = value
        }
    })
    return args
}

// Function to process directory recursively
const processDirectory = async (dir, baseOutputDir) => {
    let items = await fs.readdir(dir)
    const params = parseArgs(process.argv)
    const since = params.since || ''
    // if (since) {
    //     const commits = await gitlog({
    //         repo: '.',
    //         number: 1000,
    //         since,
    //     })
    //     items = commits.reduce((acc, commit) => {
    //         const files = commit.files.filter((file) =>
    //             file.match(/\.(jpg|jpeg|png|gif|svg)$/i)
    //         )
    //         return [...acc, ...files]
    //     }, [])
    // }
    for (const item of items) {
        let inputPath = path.join(dir, item)

        if (since) {
            const fileName = path.basename(item)
            const fileDir = path.dirname(item)
            inputPath = path.join(fileDir, fileName)
        }
        try {
            const stats = await fs.stat(inputPath)

            if (stats.isDirectory()) {
                const newBaseOutputDir = path.join(baseOutputDir, item)
                await fs.ensureDir(newBaseOutputDir)
                await processDirectory(inputPath, newBaseOutputDir)
            } else if (stats.isFile()) {
                await convertAndCompressImage(inputPath, baseOutputDir)
            }
        } catch (e) {}
    }
}

// Run the conversion and compression process
processDirectory(inputDir, outputDir)
    .then(() => console.log('All images converted and compressed.'))
    .catch((error) =>
        console.error('Error during conversion and compression:', error)
    )

// To run the script, execute the following command:
// node convert-and-compress-images.js since=2021-01-30