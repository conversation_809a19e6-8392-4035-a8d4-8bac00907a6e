<?php
use Illuminate\Support\Facades\Route;
use S2\Accounts\Http\Controllers\AccountController;
use S2\Accounts\Http\Controllers\DepositController;
use S2\Accounts\Http\Controllers\WithdrawController;
use S2\Accounts\Http\Middleware\AuthProtectMiddleware;
use App\Http\Controllers\CacheController;

Route::group(['middleware' => ['web']], function () {
    Route::prefix('account')->middleware(AuthProtectMiddleware::class)->group(function () {
        Route::get('/', [AccountController::class, 'index'])->name('account');
        Route::get('/profile', [AccountController::class, 'profile'])->name('account.profile');
        Route::get('/overview', [AccountController::class, 'overview'])->name('account.overview');

        Route::post('/deposit/codepay', [DepositController::class, 'createCodepayDeposit'])->name('deposit.codepay');
        Route::post('/deposit/codepay/cancel', [DepositController::class, 'cancelCodepayDeposit'])->name('deposit.codepay.cancel');
        Route::get('/deposit/codepay/success', [DepositController::class, 'handleCodepaySuccess'])->name('deposit.codepay.success');
        Route::get('/deposit/codepay/info', [DepositController::class, 'getCodepayInfo'])->name('deposit.codepayInfo');
        Route::get('/deposit/cryptopay/{network}', [DepositController::class, 'getCryptoAddress'])->name('deposit.cryptopay-address');
        Route::get('/deposit/{tab?}', [DepositController::class, 'deposit'])->name('deposit.index');
        Route::post('/deposit/card', [DepositController::class, 'createPhonecardDeposit'])->name('deposit.card-create');

        Route::get('/bank-account', [AccountController::class, 'bankAccount'])->name('account.bank-account');
        Route::get('/bet-history', [AccountController::class, 'betHistory'])->name('account.bet-history');
        Route::get('/password', [AccountController::class, 'password'])->name('account.password');
        Route::get('/promotion', [AccountController::class, 'promotion'])->name('account.promotions');
        Route::get('/referral', [AccountController::class, 'referral'])->name('account.referral');

        Route::get('/history', [AccountController::class, 'history'])->name('history.index');

        Route::get('/withdraw/{tab?}', [WithdrawController::class, 'withdraw'])->name('withdraw.index');
        Route::post('/withdraw/cryptopay', [WithdrawController::class, 'withdrawCrypto'])->name('withdraw.cryptopay');
        Route::post('/withdraw/card', [WithdrawController::class, 'withdrawCard'])->name('withdraw.card');
        Route::post('/createBank', [WithdrawController::class, 'createBank'])->name('withdraw.createBank');
        Route::post('/withdraw/banking', [WithdrawController::class, 'withdrawbank'])->name('withdraw.banking');
        Route::post('/cancelPromotion', [AccountController::class, 'cancelPromotion'])->name('cancelPromotion');
        Route::post('/updatePassword', [AccountController::class, 'changePassword'])->name('changePassword');
        Route::get('/suggest-deposit', [DepositController::class, 'getSuggestDeposit'])->name('suggest-deposit');
    });
    Route::get('/get-nicepay-data', [CacheController::class, 'getNicepayData'])->name('get.nicepay.data');
    Route::get('/clear-nicepay-data', [CacheController::class, 'clearNicepayData'])->name('clear.nicepay.data');
});
