<?php


namespace S2\Accounts\Services;

use App\Services\GatewayApi;
use App\Services\BaseService;
use S2\Accounts\Enums\AccountEndpoint;
use App\Helpers\DetectDeviceHelper;
use Symfony\Component\HttpFoundation\Response;
use App\Enums\ApiEndpointType;
use Carbon\Carbon;

class AccountService extends BaseService
{
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    public function getListTransaction($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::LIST_TRANSACTION->value,
                cookies: ['lang' => $request->lang],
                queryparams: $params,
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getListBet($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::LIST_BET->value,
                cookies: ['lang' => $request->lang],
                queryparams: $params,
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function mapPaymentMethod(string $method): string
    {
        $methodMapping = [
            'nicepay' => 'codepay',
        ];

        return $methodMapping[$method] ?? $method;
    }

    /**
     * Lấy giao dịch nạp tiền gần nhất
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function getLastDeposit($request): string
    {
        $response = $this->getListTransaction($request, [
            'limit' => 10,
            'action' => 'DEPOSIT'
        ]);
        if (empty($response->data)) {
            return '';
        }
        $transactions = collect($response->data)
            ->filter(function ($transaction) {
                return $transaction->type === 'PAYMENT';
            })
            ->take(1)->values();

        $method = !empty($transactions->first()->method) ? $transactions->first()->method : '';
        return $this->mapPaymentMethod($method);
    }

    public function getLastDepositSuccess($request): array
    {
        $response = $this->getListTransaction($request, [
            'limit' => 20,
            'action' => 'DEPOSIT',
            'status' => 'FINISHED',
        ]);

        if (empty($response->data)) {
            return [];
        }
        $methodMapping = config('account.historyMethodMapping');

        return collect($response->data)
            ->filter(function ($transaction) {
                return $transaction->type === 'PAYMENT';
            })
            ->map(function ($transaction) use ($methodMapping) {
                $transaction->method = $methodMapping[$transaction->method] ?? $transaction->method;
                return $transaction;
            })
            ->take(1)
            ->values()
            ->toArray();
    }

    /**
     * Lấy giao dịch rút tiền gần nhất
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function getLastWithdrawSuccess($request)
    {
        $response = $this->getListTransaction($request, [
            'limit' => 20,
            'action' => 'WITHDRAW',
            'status' => 'FINISHED'
        ]);
        if (empty($response->data)) {
            return [];
        }

        return collect($response->data)
            ->filter(function ($transaction) {
                return $transaction->type === 'PAYMENT';
            })
            ->take(1)
            ->values()
            ->toArray();
    }

    public function getUserPromotionInfo($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::ACCOUNT_INFO->value,
                cookies: ['lang' => $request->lang],
            );
            if (isset($response) && !empty((array)$response->data) && self::isSuccessResponse($response)) {
                return $response;
            }
            return null;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getUserCommission($request)
    {
        $response = $this->gatewayApi->get(
            endpoint: AccountEndpoint::ACCOUNT_COMMISSION->value,
            cookies: ['lang' => $request->lang],
        );
        if (isset($response) && self::isSuccessResponse($response)) {
            return $response;
        }
        return null;
    }

    public function getDataDisplayUserPromotion($request)
    {
        $promotions = [];
        $responsePromotion = $this->getUserPromotionInfo($request);
        if (isset($responsePromotion->data) && !empty((array) $responsePromotion->data)) {
            $promotionData = (array) $responsePromotion->data;
            $packageResource = config('package-promotion.package_resource');
            if (isset($packageResource[$promotionData['package_id']])) {
                $promotionData = array_merge($promotionData, $packageResource[$promotionData['package_id']]);
            }
            array_push($promotions, $promotionData);
        }
        return $promotions;
    }

    public function getDataDisplayUserCommission($request)
    {

        $commission = [];
        $commissionResponse = $this->getUserCommission($request);
        $commissionResponse = (array) $commissionResponse;
        if (!empty($commissionResponse['data']) && count($commissionResponse['data']) > 0) {
            $commission = collect($commissionResponse['data'])
                ->map(function ($item) {
                    $item = (array) $item;
                    $date = date('d/m', strtotime($item['date']));
                    $itemStake = floatval(str_replace(',', '', (string)$item['stake']));
                    $itemCommissionEstimate = floatval(str_replace(',', '', (string)$item['commission_estimate']));
                    $itemRolling = floatval(str_replace(',', '', (string)$item['rolling']));

                    return [
                        'date' => $date,
                        'day' => $date,
                        'bet' => $itemStake,
                        'return' => $itemCommissionEstimate,
                        'bet_txt' => formatFloatNumber($itemStake),
                        'return_txt' => formatFloatNumber($itemCommissionEstimate),
                        'rolling' => $itemRolling,
                        'total' => $itemStake + $itemCommissionEstimate,
                        'betHeight' => 0,
                        'returnHeight' => 0
                    ];
                })
                ->pipe(function ($collection) {
                    $maxValue = $collection->max('total');

                    if ($maxValue <= 0) {
                        return $collection;
                    }

                    return $collection->map(function ($item) use ($maxValue) {
                        $item['betHeight'] = round(($item['bet'] / $maxValue) * 100, 1);
                        $item['returnHeight'] = round(($item['return'] / $maxValue) * 100, 1);
                        unset($item['total']);
                        return $item;
                    });
                })
                ->toArray();
        } else {
            $commission = [];
            for ($i = 0; $i < 7; $i++) {
                $date = date('Y-m-d', strtotime("-$i days"));
                $commission[] = [
                    'date' => date('d/m', strtotime($date)),
                    'day' => date('d/m', strtotime($date)),
                    'bet' => 0,
                    'return' => 0,
                    'rolling' => 0,
                    'betHeight' => 0,
                    'returnHeight' => 0,
                ];
            }
        }
        return $commission;
    }

    public function getSlotInfo($request)
    {
        try {
            $slotsResource = config('package-promotion.package_resource')['3'];
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::SLOT_INFO->value,
                cookies: ['lang' => $request->lang],
                type: ApiEndpointType::PROMOTION,
            );
            if (self::isSuccessResponse($response) && isset($response->data)) {
                return [
                    ...$slotsResource,
                    ...(array)$response->data
                ];
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getCasinoInfo($request)
    {
        try {
            $casinoResource = config('package-promotion.package_resource')['4'];
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::CASINO_INFO->value,
                cookies: ['lang' => $request->lang],
                type: ApiEndpointType::PROMOTION,
            );
            if (self::isSuccessResponse($response) && isset($response->data)) {
                return [
                    ...$casinoResource,
                    ...(array)$response->data
                ];
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function cancelPromotion($request)
    {
        $response = $this->gatewayApi->post(
            endpoint: AccountEndpoint::CANCEL_PROMOTION->value,
            cookies: ['lang' => $request->lang],
        );
        return $response;
    }

    public function changePassword($request)
    {
        $dataRq = [
            'password' => $request->password,
            'newPassword' => $request->newPassword,
            'confirmPassword' => $request->confirmNewPassword,
        ];
        $response = $this->gatewayApi->post(
            endpoint: AccountEndpoint::CHANGE_PASSWORD->value,
            data: $dataRq,
            cookies: ['lang' => $request->lang],
        );
        return $response;
    }

    public function getAccountVerificationStatus($request)
    {
        $verificationStatusDefault = (object)[
            'deposit' => false,
            'bank' => false,
            'is_show_freespin' => false,
            'isVerified' => false,
            'isShowSection' => true,
        ];
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::ACCOUNT_VERIFICATION_STATUS->value,
                cookies: ['lang' => $request->lang],
                type: ApiEndpointType::PROMOTION,
            );
            if (self::isSuccessResponse($response)) {
                $dataResponse = $response->data;
                return (object)[
                    ...(array)$dataResponse,
                    'isVerified' => ($dataResponse->bank ?? false) && ($dataResponse->deposit ?? false),
                    'isShowSection' => !isset($dataResponse->expried_date) || Carbon::now()->lt(Carbon::parse($dataResponse->expried_date)),
                ];
            }
            return $verificationStatusDefault;
        } catch (\Exception $e) {
            return $verificationStatusDefault;
        }
    }
}
