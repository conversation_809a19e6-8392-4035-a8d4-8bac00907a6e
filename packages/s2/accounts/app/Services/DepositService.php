<?php

namespace S2\Accounts\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use App\Services\GatewayApi;
use S2\Accounts\Enums\AccountEndpoint;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use S2\Accounts\Services\AccountService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class DepositService
{
    protected $gatewayApi;
    const MOMO = 'MoMo';
    const VIETTELMONEY = 'Viettel Money';
    protected $accountService;

    public function __construct(GatewayApi $gatewayApi, AccountService $accountService)
    {
        $this->gatewayApi = $gatewayApi;
        $this->accountService = $accountService;
    }

    private function processP2Plink(String $p2pLink) {
        try {
            $url = $p2pLink ?? '';
            $parsedUrl = parse_url($url);
            parse_str($parsedUrl['query'] ?? '', $queryParams);
            $currentDomain = request()->getSchemeAndHttpHost();
            $queryParams['url_bank_empty'] = $currentDomain . '/account/bank-account';
            $queryParams['url_cskh'] = config('constants.live_chat_url');
            $parsedUrl['query'] = http_build_query($queryParams);

            $scheme = $parsedUrl['scheme'] ?? 'https';
            $host = $parsedUrl['host'] ?? '';
            $path = $parsedUrl['path'] ?? '';
            $query = $parsedUrl['query'] ?? '';

            $url = $scheme . '://' . $host . $path . ($query ? '?' . $query : '');
            return $url;
        } catch (\Exception $e) {
            return '';
        }
    }

    public function getInitialData()
    {
        $packagesResource = config('package-promotion.package_resource');
        $passHeaders = [];
        $headersAll = request()->headers->all();
        // Either Loop through the $headersAll array
        foreach ($headersAll as $key => $value) {
            // Since $value is an array, we take the first element
            // Assuming we want the first value for each header
            $passHeaders[$key] = $value[0];
        }

        $userCookie = request()->cookie('user');

        $response = $this->gatewayApi->get(endpoint: AccountEndpoint::DEPOSIT_INDEX->value, headers: $passHeaders, cookies: ['user' => $userCookie]);
        $responseData = $response->data ?? [];
        $cryptoList = $this->getCryptoCurrencyList();
        $phonecardData = $this->getPhonecardList();
        $isDepositMaintenance = [
            'p2p' => $this->isP2pMaintenance($responseData),
            'codepay' => $this->isCodepayMaintenance($responseData),
            'cryptopay' => $this->isCryptoMaintenance($cryptoList),
            'ewallet' => $this->isEwalletMaintenance($responseData),
            'phone_card' => $this->isPhonecardMaintenance($phonecardData)
        ];

        return [
            'ewallets' => [
                'momo' => [
                    'key' => 'momo',
                    'name' => self::MOMO,
                    'data' => $response->data->momos ?? [],
                    'isMaintenance' => count($response->data->momos ?? []) <= 0,
                ],
                'viettelmoney' => [
                    'key' => 'viettelmoney',
                    'name' => self::VIETTELMONEY,
                    'data' => $response->data->viettelPays ?? [],
                    'isMaintenance' => count($response->data->viettelPays ?? []) <= 0,
                ],
            ],
            'recommendDeposit' => [], // This field isn't in the response
            'packages' => array_map(function ($package) use ($packagesResource) {
                $package->percent = isset($package->type) && $package->type === 'WELCOME' ? 100 : 0;
                if (isset($packagesResource[$package->id])) {
                    $package->mbName = $packagesResource[$package->id]['mbName'];
                    $package->mbDescription = $packagesResource[$package->id]['mbDescription'];
                }
                return $package;
            }, $response->data->packages ?? []),
            'nicepayInfo' => $response->data->nicepayInfo ?? [],
            'p2pLink' => $this->processP2Plink($response->data->p2pLink ?? ''),
            'networks' => (array) $phonecardData['cardlist'] ?? [],
            'cryptoCurrencyList' => $cryptoList,
            'isDepositMaintenance' => $isDepositMaintenance ?? [],
            'withdrawBanks' => $response->data->withdrawBanks ?? [],
            'lastDeposit' => $this->getLastDeposit(request())
        ];
    }

    public function createCodepayDeposit(Request $request): array
    {
        try {
            $dataRq = $request->all();
            $headersAll = $request->headers->all();
            foreach ($headersAll as $key => $value) {
                $passHeaders[$key] = $value[0];
            }
            $userCookie = $request->cookie('user');
            if (isset($dataRq['amount'])) {
                $dataRq['amount'] = (int) str_replace([',', '.'], '', $dataRq['amount']) * 1000;
            }

            $response = $this->gatewayApi->post(
                endpoint: AccountEndpoint::DEPOSIT_CODEPPAY_CREATE->value,
                data: $dataRq,
                headers: $passHeaders,
                cookies: ['lang' => $request->lang, 'user' => $userCookie],
            );
            if (isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK]) {
                $data = $response->data[0];
                $data->expired_countdown_time = now()->addSeconds(60);
                Cache::forget('createdNicepayTime' . Auth::user()->getAuthIdentifier());
                Cache::remember('createdNicepayTime' . Auth::user()->getAuthIdentifier(), 300, function () {
                    return now()->addSeconds(60);
                });

                Cache::remember('nicepayData' . Auth::user()->getAuthIdentifier(), 60 * 30, function () use ($data) {
                    return $data;
                });
                return [
                    'status' => Response::$statusTexts[Response::HTTP_OK],
                    'data' => $data
                ];
            }

            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $response->message ?? 'Tạo phiếu nạp thất bại'
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getCodepayInfo()
    {
        try {
            $response = $this->gatewayApi->get(endpoint: AccountEndpoint::DEPOSIT_CODEPPAY_INFO->value);
            return isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data[0]) ? $response->data[0] : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function getCryptoCurrencyList(): array
    {
        try {
            $response = $this->gatewayApi->get(endpoint: AccountEndpoint::DEPOSIT_CRYPTO_LIST->value);
            return isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data) ? $response->data : [];
        } catch (\Exception $e) {
            return [];
        }
    }

    public function getCryptoAddress($network)
    {
        try {
            $response = $this->gatewayApi->get(endpoint: AccountEndpoint::DEPOSIT_CRYPTO_ADDRESS->value, queryparams: ['network' => $network]);
            return isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data[0]) ? $response->data[0] : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function getCryptoDeposit(): array
    {
        return Cache::remember('crypto_deposits' . Auth::user()->getAuthIdentifier(), 3600, function () {
            return Http::get(config('payment.crypto.deposit_endpoint'))->json()['data'];
        });
    }
    public function getEwalletCode()
    {
        try {
            $response = $this->gatewayApi->get(endpoint: AccountEndpoint::DEPOSIT_EWALLET_CODE->value);
            return isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data) ? $response->data : null;
        } catch (\Exception $e) {
            return null;
        }
    }
    public function getPhonecardList(): array
    {
        try {
            $response = $this->gatewayApi->get(endpoint: AccountEndpoint::DEPOSIT_CARD_NETWORKS->value);
            return [
                'status' => $response->status,
                'cardlist' => $response->status == 1 ? collect($response->cardlist)
                    ->sort(function($a, $b) {
                        return $b->status <=> $a->status;
                    })
                    ->toArray() : []
            ];
        } catch (\Exception $e) {
            return [
                'status' => 0,
                'cardlist' => []
            ];
        }
    }

    public function createPhonecardDeposit(Request $request)
    {
        try {
            $dataRq = $request->all();
            if (isset($dataRq['card_amount'])) {
                $dataRq['card_amount'] = (int) str_replace([',', '.'], '', $dataRq['card_amount']);
            }

            $response = $this->gatewayApi->post(endpoint: AccountEndpoint::DEPOSIT_CARD_CREATE->value, data: $dataRq);

            if (isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK]) {
                return [
                    'status' => Response::$statusTexts[Response::HTTP_OK],
                    'data' => $response->data
                ];
            } else {
                return [
                    'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                    'message' => $response->message ?? 'Không thể tạo giao dịch'
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    private function isP2pMaintenance($dataDeposit): bool
    {
        return empty($dataDeposit->p2pLink ?? '');
    }

    private function isCodepayMaintenance($dataDeposit): bool
    {
        return empty($dataDeposit->nicepayInfo) || !($dataDeposit->nicepayInfo->status ?? false);
    }

    private function isCryptoMaintenance($dataDeposit): bool
    {
        return empty($dataDeposit) || count($dataDeposit) <= 0;
    }

    private function isEwalletMaintenance($dataDeposit): bool
    {
        $momoData = count($dataDeposit->momos ?? []);
        $viettelData = count($dataDeposit->viettelPays ?? []);

        return $momoData <= 0 && $viettelData <= 0;
    }

    private function isPhonecardMaintenance($phonecardData): bool
    {
        if (empty($phonecardData)) {
            return true;
        }

        $mainStatus = $phonecardData['status'] ?? 0;
        if ($mainStatus === 0) {
            return true;
        }

        $cardlist = $phonecardData['cardlist'] ?? [];
        if (empty($cardlist)) {
            return true;
        }

        return collect($cardlist)->every(fn($item) => $item->status === 0);
    }

    public function mapPaymentMethod(string $method): string
    {
        $methodMapping = [
            'nicepay' => 'codepay',
            'phone_card' => 'card',
        ];

        return $methodMapping[$method] ?? $method;
    }

    public function mapPaymentMethodText(string $method): string
    {
        $methodMapping = [
            'nicepay' => 'Codepay',
            'phone_card' => 'Thẻ cào',
            'ibanking' => 'Ngân hàng',
            'bank_account' => 'Chuyển khoản',
            'daily_cashback_slot' => 'Hoàn trả Slots',
            'cryptopay' => 'Crypto',
        ];

        return $methodMapping[$method] ?? $method;
    }

    private function roundAmountPhoneCard($amount): float
    {
        if (!is_numeric($amount) || $amount <= 0) {
            return 0;
        }

        $amount = $amount / 1000;
        $roundingLevels = [10, 20, 30, 50, 100, 200, 300, 400, 500, 1000];

        if ($amount < min($roundingLevels)) {
            return min($roundingLevels);
        }

        if ($amount > max($roundingLevels)) {
            return max($roundingLevels);
        }

        $closest = null;
        foreach ($roundingLevels as $level) {
            if ($closest === null || abs($amount - $level) < abs($amount - $closest)) {
                $closest = $level;
            }
        }

        return $closest;
    }

    public function getLastDeposit($request): array
    {
        $response = $this->accountService->getListTransaction($request, [
            'limit' => 30,
            'action' => 'DEPOSIT',
            'status' => 'FINISHED,PHONE_CARD_FINISHED',
        ]);
        if (empty($response->data)) {
            return [];
        }
        $allowedMethods = ['nicepay', 'phone_card', 'ibanking', 'bank_account'];

        return collect($response->data)
            ->filter(function ($transaction) {
                return $transaction->type === 'PAYMENT';
            })
            ->filter(function ($transaction) use ($allowedMethods) {
                return in_array($transaction->method ?? '', $allowedMethods);
            })
            ->unique(function ($item) {
                $roundedAmount = $item->method === 'phone_card' ? $this->roundAmountPhoneCard($item->amount ?? 0) : $item->amount;
                return $roundedAmount . '_' . $item->method;
            })
            ->values()
            ->take(3)
            ->map(function ($transaction) {
                $method = $transaction->method ?? '';
                $baseData = [
                    'id' => $transaction->id ?? '',
                    'method' => $this->mapPaymentMethod($method),
                    'methodText' => $this->mapPaymentMethodText($method),
                    'to_bank_code' => $transaction->to_bank_code ?? '',
                    'created_time' => $transaction->created_time ?? '',
                    'invoice_id' => $transaction->invoice_id ?? '',
                ];

                if ($method === 'phone_card') {
                    $roundedAmount = $this->roundAmountPhoneCard($transaction->amount ?? 0);
                    return array_merge($baseData, [
                        'amount' => (int)($roundedAmount * 1000),
                        'amountText' => formatNumberWithSymbol($roundedAmount) ?? 0,
                    ]);
                }

                return array_merge($baseData, [
                    'amount' => (int)($transaction->amount ?? 0),
                    'amountText' => addSymbolToNumber($transaction->amount_txt) ?? 0,
                ]);
            })
            ->toArray();
    }
}
