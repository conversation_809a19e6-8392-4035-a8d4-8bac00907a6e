<?php

namespace S2\Accounts\Services;

use App\Services\GatewayApi;
use S2\Accounts\Enums\AccountEndpoint;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Http\Request;
use S2\Accounts\Services\DepositService;
use S2\Accounts\Helpers\BankNameHelper;

class WithdrawService
{
    protected $gatewayApi;
    protected $depositService;

    public function __construct(GatewayApi $gatewayApi, DepositService $depositService)
    {
        $this->gatewayApi = $gatewayApi;
        $this->depositService = $depositService;
    }

    public function getInitialData()
    {
        $cryptoResponse = $this->getWithdrawCryptoData();
        $p2pLink = $this->depositService->getInitialData()['p2pLink'];
        return [
            'cryptoCurrencyList' => $cryptoResponse,
            'p2pLink' => $p2pLink,
        ];
    }

    public function getPaymentList()
    {
        try {
            $userCookie = request()->cookie('user');

            $response = $this->gatewayApi->get(endpoint: AccountEndpoint::DEPOSIT_INDEX->value, cookies: ['user' => $userCookie]);

            return $response -> data;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getUserBankList()
    {
        try {
            $userCookie = request()->cookie('user');

            $response = $this->gatewayApi->get(endpoint: AccountEndpoint::ACCOUNT_USERBANK->value, cookies: ['user' => $userCookie]);

            $convertedData = array_map(function ($item) {
                $item->bank_name = BankNameHelper::convertBankName($item->bank_name);
                return $item;
            }, $response->data);
            return $convertedData;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getBankList()
    {
        try {
            $userCookie = request()->cookie('user');

            $response = $this->gatewayApi->get(endpoint: AccountEndpoint::DEPOSIT_INDEX->value, cookies: ['user' => $userCookie]);

            return $response -> data;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getWithdrawCryptoData()
    {
        $response = $this->gatewayApi->get(endpoint: AccountEndpoint::WITHDRAW_CRYPTO->value);
        if(isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data)) {
            return $response->data;
        }
        return [];
    }

    public function withdrawCrypto(Request $request)
    {
        try {
            $data = $request->all();
            $data['address'] = $request['wallet_address'];
            if (isset($data['amount_withdraw'])) {
                $data['amount_withdraw'] = (int) str_replace([',', '.'], '', $data['amount_withdraw']);
                $data['amount'] = strval(intval($data['amount_withdraw']/1000));
            }
            $response = $this->gatewayApi->post(endpoint: AccountEndpoint::WITHDRAW_CRYPTO_CREATE->value, data: $data);
            return [
                'status' => $response->status,
                'message' => isset($response->message) ? $response->message : '',
                'data' => $response->data
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function withdrawCard(Request $request)
    {
        try {
            $data = $request->all();
            $data['card_status'] = 1;
            $response = $this->gatewayApi->post(endpoint: AccountEndpoint::WITHDRAW_CARD_CREATE->value, data: $data);
            return $response;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function createBank(array $data)
    {
        try {
            $response = $this->gatewayApi->post(endpoint: AccountEndpoint::ACCOUNT_CREATE_BANK->value, data: $data);
            return $response;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function withdrawbank(Request $request)
    {
        try {
            $data = $request->all();
            $response = $this->gatewayApi->post(endpoint: AccountEndpoint::WITHDRAW_BANK->value, data: $data);
            return $response;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }
}
