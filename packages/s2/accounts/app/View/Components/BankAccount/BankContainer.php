<?php

namespace S2\Accounts\View\Components\BankAccount;

use S2\Accounts\View\Components\BladeComponent;

class BankContainer extends BladeComponent
{
    public $listBanks;
    public $withdrawData;
    public $userBanks;
    public function __construct($listBanks = [], $withdrawData = [], $userBanks = [])
    {
        $this->listBanks = $listBanks;
        $this->withdrawData = $withdrawData;
        $this->userBanks = $userBanks;
    }

    public function render()
    {
        return view('accounts::components.bank-account.bank-container');
    }
}
