<?php

namespace S2\Accounts\View\Components\BankAccount;

use S2\Accounts\View\Components\BladeComponent;

class FormAddBank extends BladeComponent
{
    public $listBanks;
    public $userBanks;
    public $bankAccountName = '';
    public function __construct($listBanks = [], $userBanks = [])
    {
        $this->listBanks = $listBanks;
        $this->userBanks = $userBanks;
        if (!empty($userBanks) && count($userBanks) >= 0) {
            $this->bankAccountName = $userBanks[0]->bank_account_name;
        }
    }

    public function render()
    {
        return view('accounts::components.bank-account.form-add-bank');
    }
}
