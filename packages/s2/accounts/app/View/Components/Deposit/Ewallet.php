<?php
namespace S2\Accounts\View\Components\Deposit;

use S2\Accounts\View\Components\BladeComponent;
class Ewallet extends BladeComponent
{
    public $depositData;
    public $ewallets;
    public $ewalletData;
    public $dropdownOptions;
    public $currentEwallet;
    public $activeEwallet;
    public $defaultQrcode;
    public $currentEwalletItem;

    public function __construct($depositData)
    {
        $this->depositData = $depositData;
        $this->getEwalletData($depositData);

    }

    private function getEwalletData($depositData)
    {
        $this->ewallets = $depositData['ewallets'] ?? [];
        $this->currentEwallet = '';

        $this->activeEwallet = array_filter($this->ewallets, fn($ewallet) => !$ewallet['isMaintenance']);
        if (!empty($this->activeEwallet)) {
            $this->currentEwallet = array_key_first($this->activeEwallet);
        }

        $this->dropdownOptions = array_map(fn($item) => [
            'label' => "{$item->account_name} - {$item->account_no}",
            'value' => $item->account_no,
            'account_name' => $item->account_name,
            'account_no' => $item->account_no,
            'qr_code' => $item->qr_code,
        ], $this->activeEwallet[$this->currentEwallet]['data'] ?? []);

        $this->currentEwalletItem = $this->activeEwallet[$this->currentEwallet]['data'][0] ?? (object)[
            'label' => '',
            'value' => '',
            'account_name' => '',
            'account_no' => '',
            'qr_code' => '',
        ];
    }

    public function render()
    {
        return view('accounts::components.deposit.ewallet', [
            'depositData' => $this->depositData,
            'ewallets' => $this->ewallets,
            'dropdownOptions' => $this->dropdownOptions,
            'currentEwallet' => $this->currentEwallet,
            'activeEwallet' => $this->activeEwallet,
            'defaultQrcode' => $this->defaultQrcode,
            'currentEwalletItem' => $this->currentEwalletItem,
        ]);
    }
}
