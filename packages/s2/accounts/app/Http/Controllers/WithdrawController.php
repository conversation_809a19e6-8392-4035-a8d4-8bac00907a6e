<?php

namespace S2\Accounts\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use S2\Accounts\Services\WithdrawService;
use S2\Accounts\Services\DepositService;
use Illuminate\Support\Facades\App;
use S2\Accounts\Services\AccountService;
use S2\Accounts\Helpers\BankNameHelper;

class WithdrawController extends Controller
{
    protected $withdrawService;
    protected $depositService;
    protected $accountService;
    public $withdrawData;
    public const COIN12 = 'coin12';
    public const P2P = 'p2p';
    public const CARD = 'card';
    public const BANK = 'banking';
    public const TYPE_ADD_BANK = 'add-bank';

    public function __construct(WithdrawService $withdrawService, DepositService $depositService, AccountService $accountService)
    {
        $this->withdrawService = $withdrawService;
        $this->depositService = $depositService;
        $this->accountService = $accountService;
    }

    private function getWithdrawData($tab)
    {
        try {
                $this->withdrawData = $this->withdrawService->getInitialData();
                switch ($tab) {
                    case self::CARD:
                    $this->withdrawData['phonecardList'] = $this->depositService->getPhonecardList();
                    break;
                case self::BANK:
                    $formatList = function ($item)
                    {
                        $item -> label = BankNameHelper::convertBankName($item->bank_name);
                        $item -> value = $item -> bank_code;
                        $item -> image = asset('/vendor/accounts/images/banks/' . mb_strtolower($item -> bank_code) . '.svg');
                        $item -> icon = 'icon-bank';

                        return $item;
                    };

                    $paymentList = $this->withdrawService->getPaymentList();
                    $bankListResponse = isset($paymentList->withdrawBanks) ? $paymentList->withdrawBanks : [];
                    $userBankListResponse =  $this->withdrawService->getUserBankList();

                    $selectedBanks = array_map(
                        function ($item){ return $item -> bank_code;},
                        $userBankListResponse
                    );

                    $formatBankList = array_map($formatList, $bankListResponse);
                    $validBankList = array_filter(
                        $formatBankList,
                        function ($item) use ($selectedBanks) {
                            return !in_array($item -> value, $selectedBanks);
                        }
                    );

                    $userBankList = array_map($formatList, $userBankListResponse);
                    // filter bank status verified and verifying
                    $userBankList = array_filter($userBankList, function($item) {
                        if (isset($item->bank_status)) {
                            return in_array(
                                $item->bank_status,
                                [config('constants.bank_status.verified'), config('constants.bank_status.verifying')]
                            );
                        }
                        return false;
                    });
                    // sort bank
                    $userBankList = array_reverse($userBankList);
                    // usort($userBankList, function($a, $b) {
                    //     if (isset($a->bank_status) && isset($b->bank_status)) {
                    //         return $b->bank_status <=> $a->bank_status;
                    //     }
                    //     return 0;
                    // });
                    $userBankInfo = isset($userBankList[0]) ? $userBankList[0] : null;
                    $this->withdrawData['userBankList'] = $userBankList;
                    $this->withdrawData['userBankInfo'] = $userBankInfo;
                    $this->withdrawData['bankList'] = $validBankList;
                    break;
                default:
                    break;
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    public function withdraw(Request $request, $tab = self::COIN12)
    {
        $recentWithdrawTab = '';
        $lastWithdrawSuccess = $this->accountService->getLastWithdrawSuccess($request);
        if (!empty($lastWithdrawSuccess) && count($lastWithdrawSuccess) > 0) {
            $tabName = config('account.withdraw_method_tab_mapping')[$lastWithdrawSuccess[0]->method];
            $recentWithdrawTab = isset($tabName) ? $tabName : '';
        }
        if (!$request->tab) {
            $defaultTab = $recentWithdrawTab ? $recentWithdrawTab : 'banking';
            return redirect('/account/withdraw/'.$defaultTab);
        }
        $type = $request->get('type');
        $this->getWithdrawData($tab);

        $seo = App::get('seo');
        $setDefaultBank = isset($type) && $type === self::TYPE_ADD_BANK ? true : false;
        return view(
            'accounts::pages.withdraw.index',
            [
                'withdrawData' => $this->withdrawData,
                'setDefaultBank' => $setDefaultBank,
                'recentWithdrawTab' => $recentWithdrawTab
            ]
        );
    }

    public function withdrawCrypto(Request $request)
    {
        $response = $this->withdrawService->withdrawCrypto($request);
        return response()->json([
            'status' => $response['status'],
            'message' => $response['message'] ?? '',
            'redirect' => '/account/history?tab=transaction'
        ]);
    }

    public function withdrawCard(Request $request)
    {
        try {
            $response = $this->withdrawService->withdrawCard($request);
            return $response;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function createBank(Request $request)
    {
        $payload = [
            'bank_code' => $request->bank_code,
            'bank_account_no' => $request->bank_account_no,
            'bank_account_name' => $request->bank_account_name
        ];
        $response = $this->withdrawService->createBank($payload);
        return $response;
    }

    public function withdrawbank(Request $request)
    {
        try {
            if ($request->has('to_bank_no') && strpos($request->to_bank_no, '*') === false) {
                $payload = [
                    'bank_code' => $request->to_bank_code,
                    'bank_account_no' => $request->to_bank_no,
                    'bank_account_name' => $request->to_bank_name
                ];

                $bankResponse = $this->withdrawService->createBank($payload);
                if (isset($bankResponse->status) && $bankResponse->status !== 'OK') {
                    return response()->json([
                        'code' => $bankResponse->code,
                        'status' => 'ERROR',
                        'message' => $bankResponse->message
                    ]);
                }
            }
            $response = $this->withdrawService->withdrawbank($request);
            if (isset($response)) {
                return response()->json($response);
            }
            return response()->json([
                'code' => 404,
                'status' => 'ERROR',
                'message' => 'Đã có lỗi trong quá trình giao dịch. Vui lòng thử lại.'
            ]);

        } catch (\Throwable $th) {
            return response()->json([
                'code' => 404,
                'status' => 'ERROR',
                'message' => 'Đã có lỗi trong quá trình giao dịch. Vui lòng thử lại.'
            ]);
        }
    }
}
