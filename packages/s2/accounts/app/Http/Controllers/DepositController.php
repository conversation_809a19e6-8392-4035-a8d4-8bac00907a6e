<?php

namespace S2\Accounts\Http\Controllers;

use App\Http\Controllers\Controller;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use S2\Accounts\Services\DepositService;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;
use S2\Accounts\Services\AccountService;

class DepositController extends Controller
{
    protected $depositService;
    protected $accountService;
    public $depositData;
    public const CODEPAY = 'codepay';
    public const CRYPTO = 'cryptopay';
    public const EWALLET = 'ewallet';
    public const PHONECARD = 'phonecard';

    public function __construct(DepositService $depositService, AccountService $accountService)
    {
        $this->depositService = $depositService;
        $this->accountService = $accountService;
    }

    private function getDepositData($tab)
    {
        $this->depositData = $this->getInitialData();
        switch ($tab) {
            case self::CRYPTO:
                if (!empty($this->depositData['cryptoCurrencyList'])) {
                    $firstCurrency = $this->depositData['cryptoCurrencyList'][0] ?? null;
                    if ($firstCurrency && !empty($firstCurrency->network)) {
                        $this->depositData['cryptoInfo'] = $this->depositService->getCryptoAddress($firstCurrency->network[0]);
                    }
                }
                break;
            case self::EWALLET:
                $this->depositData['ewalletCode'] = $this->depositService->getEwalletCode();
                break;
            case self::CODEPAY:
            default:
                $userId = Auth::check() ? Auth::user()->getAuthIdentifier() : null;
                $nicepayData = $userId ? Cache::get('nicepayData' . $userId) : null;
                if ($nicepayData && isset($nicepayData->expired_at_utc) && now()->utc()->isAfter($nicepayData->expired_at_utc)) {
                    if ($userId) {
                        Cache::forget('nicepayData' . $userId);
                    }
                    $nicepayData = null;
                }
                $this->depositData['nicepayData'] = $nicepayData;

                break;
        }
    }

    public function deposit(Request $request, $tab = self::CODEPAY)
    {
        $methodMapping = config('account.historyMethodMapping');
        $maintenance = $this->depositService->getInitialData()['isDepositMaintenance'];
        $lastDepositSuccess = $this->accountService->getLastDepositSuccess($request);

        if (!empty($lastDepositSuccess) && !$request->tab) {
            $lastMethod = $lastDepositSuccess[0]->method;
            $mappedMethod = $methodMapping[$lastMethod] ?? $lastMethod;

            if (!array_key_exists($lastMethod, $maintenance) || $maintenance[$lastMethod] === false) {
                return redirect()->route('deposit.index', ['tab' => $mappedMethod]);
            }
        }
        $this->getDepositData($tab);

        return view(
            'accounts::pages.deposit.index',
            [
                'depositData' => $this->depositData,
                'lastDeposit' => $this->accountService->getLastDeposit($request),
                'lastDepositSuccess' => $this->accountService->getLastDepositSuccess($request),
            ]
        );
    }

    public function getInitialData()
    {
        return $this->depositService->getInitialData();
    }
    public function createCodepayDeposit(Request $request)
    {
        if (Auth::check()) {
            Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());
        }

        try {
            if ($request->has('packageId')) {
                $request->merge(['packageId' => (int)$request->packageId]);
            }

            $result = $this->depositService->createCodepayDeposit($request);

            if ($result['status'] !== Response::$statusTexts[Response::HTTP_OK]) {
                if (Auth::check()) {
                    Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());
                }
                return response()->json([
                    'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                    'message' => $result['message']
                ], Response::HTTP_BAD_REQUEST);
            }

            if (!is_string($result['data']->qrcode) || empty($result['data']->qrcode)) {
                if (Auth::check()) {
                    Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());
                }
                return response()->json([
                    'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                    'message' => $result['message']
                ], Response::HTTP_BAD_REQUEST);
            }

            return response()->json([
                'status' => Response::$statusTexts[Response::HTTP_OK],
                'data' => $result['data']
            ], Response::HTTP_OK);

        } catch (\Exception $e) {
            return response()->json([
                'status' => Response::$statusTexts[Response::HTTP_INTERNAL_SERVER_ERROR],
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function cancelCodepayDeposit(Request $request): JsonResponse
    {
        try {
            if (Auth::check()) {
                Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());
            }

            return response()->json([
                'status' => Response::$statusTexts[Response::HTTP_OK],
                'redirect' => route('deposit.index', ['tab' => self::CODEPAY])
            ], Response::HTTP_OK);

        } catch (\Exception $e) {
            return response()->json([
                'status' => Response::$statusTexts[Response::HTTP_INTERNAL_SERVER_ERROR],
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function handleCodepaySuccess(Request $request)
    {
        if (Auth::check()) {
            Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());
        }
        return response()->json(['status' => Response::$statusTexts[Response::HTTP_OK]], Response::HTTP_OK);
    }

    public function getCodepayInfo()
    {
        return response()->json([
            'status' => Response::$statusTexts[Response::HTTP_OK],
            'data' => $this->depositService->getCodepayInfo()
        ], Response::HTTP_OK);
    }

    public function getCryptoAddress($network): JsonResponse
    {
        if (!$network) {
            return response()->json(['status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST]], Response::HTTP_BAD_REQUEST);
        }
        $result = $this->depositService->getCryptoAddress($network);
        return response()->json(['status' => Response::$statusTexts[Response::HTTP_OK], 'data' => [$result]], Response::HTTP_OK);
    }
    public function getEwalletCode(): JsonResponse
    {
        $result = $this->depositService->getEwalletCode();
        return response()->json(['status' => Response::$statusTexts[Response::HTTP_OK], 'data' => $result], Response::HTTP_OK);
    }

    public function getPhonecardList(): JsonResponse
    {
        $result = $this->depositService->getPhonecardList();
        return response()->json($result);
    }

    public function createPhonecardDeposit(Request $request)
    {
        try {
            $result = $this->depositService->createPhonecardDeposit($request);
            if ($result['status'] === Response::$statusTexts[Response::HTTP_OK]) {
                return response()->json([
                    'status' => Response::$statusTexts[Response::HTTP_OK],
                    'message' => formatAmount($request->card_amount),
                    'redirect' => '/account/history?tab=transaction'
                ], Response::HTTP_OK);
            }

            return response()->json([
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $result['message'] ?? 'Có lỗi xảy ra khi nạp thẻ'
            ], Response::HTTP_BAD_REQUEST);

        } catch (\Exception $e) {
            return response()->json([
                'status' => Response::$statusTexts[Response::HTTP_INTERNAL_SERVER_ERROR],
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function checkCacheWorking()
    {
        $testKey = 'test_cache_' . time();
        $testValue = 'test_value';

        // Thử set cache
        Cache::put($testKey, $testValue, 60);

        // Thử đọc cache
        $result = Cache::get($testKey);

        // Xóa test cache
        Cache::forget($testKey);

        return [
            'cache_working' => $result === $testValue,
            'driver' => config('cache.default'),
            'store' => config('cache.stores.' . config('cache.default'))
        ];
    }

    public function filterMethod($method)
        {
            $originalMethod = $method;
            $method = strtolower($method);
            $filters = config('account.history.suggestMethodDeposit');

            $found = false;
            foreach ($filters as $methods) {
                if (in_array($method, $methods)) {
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                return $originalMethod;
            }

            foreach ($filters as $newMethod => $oldMethods) {
                if (in_array($method, $oldMethods)) {
                    return $newMethod;
                }
            }

            return $originalMethod;
        }

    public function getSuggestDeposit(Request $request)
    {
        $response = $this->depositService->getLastDeposit($request);
        $response = collect($response)->map(function ($item) {
            $item['filterMethod'] = $this->filterMethod($item['method']);
            return $item;
        });
        return $response;
    }
}
