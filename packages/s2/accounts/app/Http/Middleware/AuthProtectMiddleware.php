<?php

namespace S2\Accounts\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class AuthProtectMiddleware
{
    protected $ProtectedPathPattern = 'account*';

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            $userCookie = $request->cookie('user');
            if (!$userCookie || !Auth::check()) {
                return $this->logoutAndRedirect($request, $next);
            }
            return $next($request);
        } catch (Exception $ex) {
            return $this->logoutAndRedirect($request, $next);
        }
    }

    protected function logoutAndRedirect(Request $request, Closure $next)
    {
        $request->session()->flush();
        if (
            str_contains($request->path(), 'account')
        ) {
            $request->session()->flash('showLogin', 'true');
            $request->session()->flash('link', $request->path());
        }
        Auth::logout();

        if (Str::is($this->ProtectedPathPattern, $request->path())) {
            return redirect('/');
        }

        return $next($request);
    }
}
