<?php
$brandName = config('app.brand_name');
return [
    'historyMethodMapping' => [
        'momo' => 'ewallet',
        'viettelpay' => 'ewallet',
        'ibanking' => 'codepay',
        'bank_account' => 'codepay',
        'nicepay' => 'codepay',
        'phone_card' => 'card'
    ],
    'navbar' => [
        [
            'id' => 'overview',
            'label' => '',
            'items' => [
                [
                    'id' => 'overview',
                    'label' => 'Tổng quan',
                    'icon' => 'icon-overview',
                    'urlRegex' => '/^.*overview.*/',
                    'href' => '/account/overview',
                ],
            ]
        ],

        [
            'id' => 'transactions',
            'label' => 'Giao dịch',
            'type' => 'group',
            'items' => [
                [
                    'id' => 'deposit',
                    'label' => 'Nạp tiền',
                    'icon' => 'icon-deposit',
                    'urlRegex' => '/^.*deposit.*/',
                    'href' => '/account/deposit',
                ],
                [
                    'id' => 'withdraw',
                    'label' => 'Rút tiền',
                    'icon' => 'icon-withdraw',
                    'urlRegex' => '/^.*withdraw.*/',
                    'href' => '/account/withdraw',
                ],
                [
                    'id' => 'bankAccount',
                    'label' => 'Tài khoản ngân hàng',
                    'icon' => 'icon-bank-account',
                    'urlRegex' => '/^.*bank-account.*/',
                    'href' => '/account/bank-account',
                ],
                [
                    'id' => 'betHistory',
                    'label' => 'Lịch Sử Cược/Giao Dịch',
                    'icon' => 'icon-transaction-history',
                    'urlRegex' => '/^.*history.*/',
                    'href' => '/account/history',
                ],
            ],
        ],
        [
            'id' => 'information',
            'label' => 'Thông tin',
            'type' => 'group',
            'items' => [
                [
                    'id' => 'profile',
                    'label' => 'Tài khoản',
                    'icon' => 'icon-profile',
                    'urlRegex' => '/^.*profile.*/',
                    'href' => '/account/profile',
                ],
                [
                    'id' => 'promotion',
                    'label' => 'Khuyến mãi',
                    'icon' => 'icon-gift',
                    'urlRegex' => '/^.*promotion.*/',
                    'href' => '/account/promotion',
                ],
            ],
        ],
    ],
    'deposit_tabs' => [
        [
            'id' => 'p2p',
            'value' => 'p2p',
            'title' => 'giao dịch P2P',
            'icon' => 'p2p',
            'is_active' => true,
            'badge' => 'new',
        ],
        [
            'id' => 'codepay',
            'value' => 'codepay',
            'title' => 'Codepay',
            'icon' => 'codepay',
            'badge' => '',
        ],
        [
            'id' => 'cryptopay',
            'value' => 'cryptopay',
            'title' => 'Tiền ảo',
            'icon' => 'cryptopay',
            'is_active' => true,
            'badge' => '',
        ],
        [
            'id' => 'ewallet',
            'value' => 'ewallet',
            'title' => 'Ví điện tử',
            'icon' => 'ewallet',
            'badge' => '',
        ],
        [
            'id' => 'card',
            'value' => 'phone_card',
            'title' => 'Thẻ cào',
            'icon' => 'card',
            'badge' => '',
        ],
    ],
    'withdraw_tabs' => [
        // [
        //     'id' => 'coin12',
        //     'value' => 'coin12',
        //     'title' => 'Coin12',
        //     'icon' => '',
        //     'is_active' => true,
        //     'badge' => 'new',
        // ],
        [
            'id' => 'p2p',
            'value' => 'p2p',
            'title' => 'Giao dịch P2P',
            'icon' => 'p2p',
            'is_active' => true,
            'badge' => 'new',
        ],
        [
            'id' => 'banking',
            'value' => 'codepay',
            'title' => 'Ngân Hàng',
            'icon' => 'bank',
            'is_active' => true,
            'badge' => '',
        ],
        [
            'id' => 'cryptopay',
            'value' => 'cryptopay',
            'title' => 'Tiền Ảo',
            'icon' => 'cryptopay',
            'is_active' => true,
            'badge' => '',
        ],
        [
            'id' => 'card',
            'value' => 'phone_card',
            'title' => 'Thẻ Cào',
            'icon' => 'card',
            'is_active' => true,
            'badge' => '',
        ],
    ],
    'overview' => [
        'id' => 'overview',
        'href' => '/account',
        'icon' => 'vendor/accounts/images/menu/dashboard.svg',
        'name' => 'account.menus.overview_title',
    ],
    'transaction' => [
        [
            'id' => 'deposit',
            'href' => '/account/deposit',
            'icon' => 'vendor/accounts/images/menu/deposit.svg',
            'name' => 'account.menus.deposit',
            'name_mb' => 'account.menus.deposit',
        ],
        [
            'id' => 'withdraw',
            'href' => '/account/withdraw',
            'icon' => 'vendor/accounts/images/menu/withdraw.svg',
            'name' => 'account.menus.withdraw',
            'name_mb' => 'account.menus.withdraw',
        ],
        [
            'id' => 'bank-management',
            'href' => '/account/bank-account',
            'icon' => 'vendor/accounts/images/menu/bank.svg',
            'name' => 'account.menus.bank_management',
            'name_mb' => 'account.menus.bank_management_mb',
        ],
        [
            'id' => 'history',
            'href' => '/account/history',
            'icon' => 'vendor/accounts/images/menu/history.svg',
            'name' => 'account.menus.history',
            'name_mb' => 'account.menus.history_mb',
        ],
    ],
    'more' => [
        [
            'id' => 'promotion',
            'href' => '/account/promotion',
            'icon' => 'vendor/accounts/images/menu/promotion.svg',
            'name' => 'account.menus.promotion',
        ],
    ],
    'information' => [
        [
            'id' => 'personal_information',
            'href' => '/account/information',
            'icon' => 'vendor/accounts/images/menu/profile.svg',
            'name' => 'account.menus.personal_information',
        ],
    ],
    'deposit_codepay_guide' => [
        'title' => 'Hướng dẫn chuyển tiền',
        'sections' => [
            [
                'heading' => 'Lưu ý',
                'items' => [
                    'Nạp/Rút bằng tài khoản chính chủ.',
                    'Mã QR chỉ có thể sử dụng một lần.',
                    'Hỗ trợ chuyển tiền liên ngân hàng.',
                    'Lưu lại biên lai để đối chiếu khi cần.',
                    'Nạp tối thiểu 50.000, tối đa 500 triệu trên 1 giao dịch. '
                ]
            ],
            [
                'heading' => 'Mã QR',
                'items' => [
                    'Mã chỉ có thể sử dụng một lần, vui lòng nhập nội dung chuyển tiền chính xác, nếu sai sẽ không nhận được tiền.'
                ]
            ],
            [
                'heading' => 'Tài khoản',
                'items' => [
                    'Các tài khoản ngân hàng sẽ luôn được cập nhật thay đổi liên tục, vui lòng không lưu lại thông tin này khi giao dịch.'
                ]
            ]
        ]
    ],
    'deposit_cryptopay_guide' => [
        'title' => 'Hướng dẫn chuyển tiền',
        'sections' => [
            [
                'heading' => '',
                'items' => [
                    'Bước 1: Chọn loại tiền ảo muốn nạp',
                    'Bước 2: Chuyển tiền vào địa chỉ ví tương ứng ở bước 1. [Loại tiền] - [Network].',
                    'Bước 3: Hệ thống sẽ tự động cập nhật số dư tài khoản sau 5 phút.',
                ]
            ],
        ]
    ],
    'deposit_card_guide' => [
        'title' => 'Hướng dẫn chuyển tiền',
        'sections' => [
            [
                'heading' => '',
                'items' => [
                    'Vui lòng chọn đúng mệnh giá và nhà mạng. Nếu bạn chọn sai chúng tôi sẽ không chịu trách nhiệm.',
                    'Phí gạch thẻ rẻ nhất thị trường.',
                    'Nạp qua thẻ cào chỉ áp dụng các khuyến mãi HOÀN TRẢ.',
                ]
            ],
        ]
    ],
    'deposit_ewallet_guide' => [
        'title' => 'Hướng dẫn chuyển tiền',
        'sections' => [
            [
                'heading' => '',
                'items' => [
                    'Bước 1: Đăng nhập vào [ví điện tử: Momo, Zalopay,…]',
                    'Bước 2: Quét mã QR hoặc nhập thông tin tài khoản nhận tiền.',
                    'Bước 3: Thực hiện chuyển tiền với nội dung đúng với hệ thống cung cấp.',
                ]
            ],
        ]
    ],
    'deposit_ewallet_note' => [
        'title' => 'Lưu ý',
        'sections' => [
            [
                'heading' => '',
                'items' => [
                    'Quý khách có thể quét mã QR code để giao dịch nhanh chóng hơn.',
                    'Khuyến mãi <strong>100%</strong> không áp dụng khi nạp qua Momo.',
                    'Sau khi chuyển khoản hãy chờ khoảng <strong>30 GIÂY</strong> tiền sẽ được nạp vào tài khoản của bạn.',
                    'Nếu chuyển khoản không điền hoặc điền sai nội dung sẽ không nhận tiền được ngay.',
                    'Sau 3 phút nếu chưa nhận được tiền hãy nhờ hỗ trợ <strong>SUPPORT 24/24</strong>.',
                    'Nạp tối thiểu 50.000, tối đa 5 triệu trên 1 giao dịch.'
                ]
            ],
        ]
    ],
    'withdraw_cryptopay_guide' => [
        'title' => 'Hướng dẫn rút tiền',
        'sections' => [
            [
                'heading' => '',
                'items' => [
                    'Vui lòng chọn đúng loại tiền ảo, số tiền và địa chỉ ví tương ứng, '.$brandName.' không chịu trách nhiệm nếu nhập sai.',
                    'Rút tối thiểu 400,000 VND cho USDT.',
                    'Rút tối thiểu 100,000 VND cho KDG.'
                ]
            ],
        ]
    ],
    'history' => [
        'tabList' => [
            [
                'id' => 'bet',
                'title' => "Lịch sử cược",
                'value' => "bet",
                'icon'=> "bet",
            ],
            [
                'id' => 'transaction',
                'title' => "Lịch sử giao dịch",
                'value' => "transaction",
                'icon' => "transaction",
            ],
        ],
        'betHeader' => ['Mã giao dịch', 'Thời gian', 'Game', 'Số tiền', 'Thắng/Thua', 'Turnover', 'Trạng thái'],
        'transactionHeader' => ['Thời gian', 'Giao dịch', 'Ngân hàng', 'Phương thức', 'Số tiền', 'Mã giao dịch', 'Trạng thái'],
        'statusBet' => [
            'WIN' => 'Thắng',
            'LOSE' => 'Thua',
            'WON' => 'Thắng',
            'LOST' => 'Thua',
            "HALF WON" => "Thắng 1/2",
            "HALF WIN" => "Thắng 1/2",
            "BONUS" => "Bonus",
            "HALF LOST" => "Thua 1/2",
            "HALF LOSE" => "Thua 1/2",
            'CANCEL' => 'Huỷ',
            'RUNING' => 'Đang chờ',
            'RUNNING' => 'Đang chờ',
            "PENDING" => "Đang chờ",
            "OPEN" => "Đang chờ",
            "OPENED" => "Đang chờ",
            "WAIT" => "Đang chờ",
            "WAITING" => "Đang chờ",
            "BET" => "Đang chờ",
            'DRAW' => 'Hòa',
            "TIP" => "Tip",
            "TIPS" => "Tip",
            "REFUND" => "Hoàn trả"
        ],
        'typeTransaction' => [
            'WITHDRAW' => 'Rút tiền',
            'DEPOSIT' => 'Nạp tiền',
            'PROMOTION' => 'Khuyến mãi',
            'PROMOTION_CANCEL' => 'Huỷ khuyến mãi',
            'COMMISSION' => 'Khuyến mãi',
        ],
        'methodTransaction' => [
            'ibanking' => 'Ngân hàng',
            'bank_account' => 'Chuyển khoản',
            'nicepay' => 'nicepay',
            'phone_card' => 'Thẻ cào',
            'daily_cashback_slot' => 'Hoàn trả Slots',
        ],
        'statusTransaction' => [
            'CANCEL' => 'Thất bại',
            'DRAFT' => 'Đang xử lý',
            'FINISHED' => 'Hoàn thành',
            'DELETE' => 'Thất bại',
            'PENDING' => 'Đang xử lý',
            'PHONE_CARD_PROCESSING' => 'Đang xử lý',
            'PHONE_CARD_PENDING' => 'Đang xử lý',
            'PHONE_CARD_FINISHED' => 'Hoàn thành',
            'PHONE_CARD_CANCEL' => 'Thất bại',
            'PHONE_CARD_DRAFT' => 'Đang xử lý',
            'SMART_PAY_PROCESSING' => 'Đang xử lý',
            'MOMO_PROCESSING' => 'Đang xử lý',
            'PROCESSING' => 'Đang xử lý',
            'CC_PENDING' => 'Đang xử lý',
            'CC_PROCESSING' => 'Đang xử lý',
            'APPROVED' => 'Đang xử lý',
        ],
        'suggestMethodDeposit' => [
            'ewallet' => ['momo', 'viettelpay'],
            'codepay' => ['ibanking', 'bank_account', 'nicepay']
        ]
    ],
    'withdraw_card_guide' => [
        'title' => 'Hướng dẫn rút tiền',
        'sections' => [
            [
                'items' => [
                    'Vui lòng chọn đúng mệnh giá và nhà mạng, nếu chọn sai chúng tôi không chịu trách nhiệm.',
                    'Sau khi rút thẻ cào, Quý Khách vui lòng vào mục <a href="/account/history?tab=transaction" class="text-primary underline transition-all xl:hover:text-neutral">lịch sử giao dịch</a> để lưu lại thông tin thẻ cào, lịch sử giao dịch của Quý Khách sẽ bị xoá sau 7 ngày.'
                ]
            ],
        ]
    ],
    'withdraw_banking_guide' => [
        'title' => 'Hướng dẫn rút tiền',
        'sections' => [
            [
                'heading' => 'Lưu ý',
                'items' => [
                    'Quý khách vui lòng kiểm tra và nhập chính xác thông tin tài khoản nhận tiền, chúng tôi không chịu trách nhiệm nếu quý khách nhập sai.',
                    'Mọi thắc mắc vui lòng liên hệ <span onclick="openLiveChat()" class="text-[#6CFE00] cursor-pointer transition-all xl:hover:text-primary-500 underline">Live chat</span>.'
                ]
            ]
        ]
    ],
    'profile' => [
        'personal_information' => 'Thông tin cá nhân',
        'username' => 'Tên đăng nhập',
        'show_username' => 'Tên hiển thị',
        'phone_number' => 'Số điện thoại',
        'email' => 'Email',
        'password' => 'Mật khẩu',
        'update' => 'Cập nhật',
        'verify' => 'Xác minh',
        'old_password' => 'Mật khẩu cũ',
        'new_password' => 'Mật khẩu mới',
        'confirm_password' => 'Xác nhận mật khẩu',
        'forgot_password' => 'Quên mật khẩu',
        'input_old_password' => 'Nhập mật khẩu cũ',
        'input_new_password' => 'Nhập mật khẩu mới',
        'input_confirm_password' => 'Nhập lại mật khẩu mới',
        'wrapper_class' => '!gap-[40px] rounded-[24px] bg-account-surface-primary',
    ],
    'deposit_crypto_guide' => [
        'title' => 'Hướng dẫn mua USDT',
    ],
    'overview_categories' => [
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/saba-sports.jpg',
            'name' => 'sABA - Sports',
            'to' => '/ca-cuoc-the-thao/saba-sports',
            'apiUrl' => '/athena/sportUrl?loginPath=asports?login=true',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/e-sports.jpg',
            'name' => 'E - Sports',
            'to' => '/ca-cuoc-the-thao/im-sports',
            'apiUrl' => '/gameUrl?p=im&gId=sport',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/song-bai.jpg',
            'name' => 'Sòng bài',
            'url' => '/live-casino',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/k-sports.jpg',
            'name' => 'K - Sports',
            'to' => '/ca-cuoc-the-thao/ksports',
            'apiUrl' => '/tp/ksportUrl?loginPath=ksports&login=true',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/no-hu.jpg',
            'name' => 'Nổ hũ',
            'url' => '/cong-game/no-hu',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/bingo.jpg',
            'name' => 'Bingo',
            'url' => '/cong-game/xo-so',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/keno.jpg',
            'name' => 'Keno',
            'url' => '/cong-game/keno',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/game-bai.jpg',
            'name' => 'Game bài',
            'url' => '/cong-game/game-bai',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/quay-so.jpg',
            'name' => 'Quay số',
            'url' => '/cong-game/quay-so-number-games',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/ban-ca.jpg',
            'name' => 'bắn cá',
            'url' => '/cong-game/ban-ca',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/taixiu.jpg',
            'name' => 'Tài xỉu',
            'url' => '/live-casino/tai-xiu',
        ],
        [
            'thumbnail' => '/vendor/accounts/images/overview/category/game-nhanh.jpg',
            'name' => 'Games nhanh',
            'url' => '/cong-game/game-nhanh',
        ],
    ],
    'personal_information_menu' => [
        [
            'icon' => '/vendor/accounts/images/icons/icon-deposit.svg',
            'title' => 'Nạp tiền',
            'url' => '/account/deposit',
        ],
        [
            'icon' => '/vendor/accounts/images/icons/icon-withdraw.svg',
            'title' => 'Rút tiền',
            'url' => '/account/withdraw',
        ],
        [
            'icon' => '/vendor/accounts/images/icons/icon-profile.svg',
            'title' => 'Tài khoản',
            'url' => '/account/profile',
        ],
        [
            'icon' => '/vendor/accounts/images/overview/icon-overview-history.png',
            'title' => 'Lịch sử',
            'url' => '/account/history?tab=transaction',
        ],
    ],
    'withdraw_method_tab_mapping' => [
        'phone_card' => 'card',
        'bank_account' => 'banking',
        'cryptopay' => 'cryptopay',
        'coin12' => 'coin12',
        'p2p' => 'p2p',
    ],
    'freeSpins' => (object)[
        'partner_game_id' => 'kts9968',
        'name' => 'Cung Hỷ Phát Tài',
        'api_url' => '/gameUrl?p=vingame&gId=kts9968',
        'partner' => 'vingame',
    ],
];
