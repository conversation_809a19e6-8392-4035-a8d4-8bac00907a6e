<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_12832_94571)">
<rect opacity="0.8" width="20" height="20" rx="10" fill="#0D8E01"/>
<g filter="url(#filter0_biii_12832_94571)">
<rect width="20" height="20" rx="10" fill="#00B268" fill-opacity="0.5"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.6834 6.97693C15.1055 7.39019 15.1055 8.0602 14.6834 8.47345L9.54822 13.4999C9.12604 13.9132 8.44153 13.9132 8.01934 13.4999L5.31664 10.8544C4.89445 10.4412 4.89445 9.77114 5.31664 9.35788C5.73883 8.94463 6.42333 8.94463 6.84552 9.35788L8.78378 11.2551L13.1545 6.97693C13.5767 6.56368 14.2612 6.56368 14.6834 6.97693Z" fill="white"/>
</g>
<defs>
<filter id="filter0_biii_12832_94571" x="-10.5" y="-10.5" width="41" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="5.25"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_12832_94571"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_12832_94571" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.25"/>
<feGaussianBlur stdDeviation="1.125"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_12832_94571"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_12832_94571" result="effect3_innerShadow_12832_94571"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.75"/>
<feGaussianBlur stdDeviation="0.375"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_12832_94571" result="effect4_innerShadow_12832_94571"/>
</filter>
<clipPath id="clip0_12832_94571">
<rect width="20" height="20" rx="10" fill="white"/>
</clipPath>
</defs>
</svg>
