<svg width="93" height="44" viewBox="0 0 93 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1072_16)">
<rect width="92" height="44" transform="translate(0.5)" fill="url(#paint0_linear_1072_16)"/>
<g filter="url(#filter0_f_1072_16)">
<path d="M92.5 43.0485L49.5746 39.8182L46.5 32L43.4255 39.8182L0.500013 43.0485L0.5 44L92.5 44V43.0485Z" fill="#00FFA6"/>
</g>
</g>
<defs>
<filter id="filter0_f_1072_16" x="-22.1" y="9.4" width="137.2" height="57.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="11.3" result="effect1_foregroundBlur_1072_16"/>
</filter>
<linearGradient id="paint0_linear_1072_16" x1="46" y1="0" x2="46" y2="44" gradientUnits="userSpaceOnUse">
<stop stop-color="#183B2F" stop-opacity="0"/>
<stop offset="1" stop-color="#183B2F"/>
</linearGradient>
<clipPath id="clip0_1072_16">
<rect width="92" height="44" fill="white" transform="translate(0.5)"/>
</clipPath>
</defs>
</svg>
