@section('account-header')
    <x-accounts::header-navigation backUrl="/account" title="Tài k<PERSON>ản" />
@endsection
<div class="!xl:pl-[20px] xl:h-full rounded-2xl xl:rounded-[24px] bg-account-surface-primary p-3 xl:px-0 xl:py-6">
    <div class="mb-5 flex items-center gap-2 xl:mb-6">
        <img src="{{ asset('vendor/accounts/images/icons/icon-pass.svg') }}" alt="password">
        <label class="block text-[14px] font-medium capitalize text-account-text-primary">
            {{ __('account.password') }}
        </label>
    </div>
    <form id="change-password-form" class="flex flex-col gap-4 xl:gap-5" autocomplete="off">
        <x-kit.input type="password" label="{{ __('account.current_password') }}"
            id="change-password-input-currentPassword" name="currentPassword" maxlength="32"
            placeholder="Nhập mật khẩu cũ" noEmoji />

        <x-kit.input type="password" label="{{ __('account.new_password') }}" id="change-password-input-newPassword"
            name="newPassword" maxlength="32" placeholder="Nhập mật khẩu mới" noEmoji />

        <x-kit.input type="password" label="{{ __('account.confirm_new_password') }}"
            id="change-password-input-confirmNewPassword" name="confirmNewPassword" maxlength="32"
            placeholder="Nhập lại mật khẩu mới" noEmoji />
        <x-kit.button disabled id="submit-change-password-btn" type="submit"
            class="btn btn-primary button-submit mx-auto mt-1 h-[40px] w-full min-w-[215px] bg-account-text-brand-primary">
            {{ __('account.update') }}
        </x-kit.button>
    </form>
</div>
@pushOnce('scripts')
    <script>
        window.addEventListener("load", async (event) => {
            const form = $("#change-password-form");
            const buttonSubmit = $("#submit-change-password-btn");
            const inputFields = form.find(".input-field");

            inputFields.each((index, element) => {
                $(element).on("change input", function() {
                    checkShowSubmit(form, buttonSubmit);
                });
            });

            $("#change-password-form").validate({
                rules: {
                    currentPassword: {
                        required: true,
                        minlength: 6,
                        maxlength: 32,
                        passwordNoSpace: true
                    },
                    newPassword: {
                        required: true,
                        notEqualTo: "#change-password-input-currentPassword",
                        minlength: 6,
                        maxlength: 32,
                        passwordNoSpace: true,
                    },
                    confirmNewPassword: {
                        required: true,
                        equalTo: "#change-password-input-newPassword",
                        minlength: 6,
                        maxlength: 32,
                        passwordNoSpace: true,
                    }
                },
                messages: {
                    currentPassword: {
                        required: validationMessages.current_password.required,
                        minlength: validationMessages.password.min,
                        maxlength: validationMessages.password.max,
                        passwordNoSpace: validationMessages.password.space,
                    },
                    newPassword: {
                        required: validationMessages.new_password.required,
                        minlength: validationMessages.password.min,
                        maxlength: validationMessages.password.max,
                        passwordNoSpace: validationMessages.password.space,
                        notEqualTo: validationMessages.new_password.not_equal,
                    },
                    confirmNewPassword: {
                        required: validationMessages.confirm_password.required,
                        minlength: validationMessages.password.min,
                        maxlength: validationMessages.password.max,
                        passwordNoSpace: validationMessages.password.space,
                        equalTo: validationMessages.confirm_password.not_match,
                    }
                },
                errorClass: 'form-input-error',
                errorPlacement: function(error, element) {
                    element?.closest('.base-input')?.append(error);
                },
                errorElement: 'span'
            })

            $("#change-password-input-currentPassword, #change-password-input-newPassword, #change-password-input-confirmNewPassword")
                .on("change input", function() {
                    if ($('#change-password-input-currentPassword').val() && $('#change-password-input-newPassword').val()) {
                        $('#change-password-input-newPassword').valid();
                    }
                    if ($('#change-password-input-newPassword').val() && $('#change-password-input-currentPassword').val()) {
                        $('#change-password-input-currentPassword').valid();
                    }
                    if ($('#change-password-input-confirmNewPassword').val() && $('#change-password-input-newPassword').val()) {
                        $('#change-password-input-newPassword').valid();
                    }
                    if ($('#change-password-input-confirmNewPassword').val() && $('#change-password-input-newPassword').val()) {
                        $('#change-password-input-confirmNewPassword').valid();
                    }
                });

            $("#change-password-form").submit(async function(e) {
                e.preventDefault()
                buttonSubmit.addClass('is-loading');
                if ($(this).valid()) {
                    const password = $("#change-password-input-currentPassword").val();
                    const newPassword = $("#change-password-input-newPassword").val();
                    const confirmNewPassword = $("#change-password-input-confirmNewPassword").val();

                    const res = await submitData((url =
                        '/account/updatePassword'
                    ), {
                        password,
                        newPassword,
                        confirmNewPassword
                    });
                    const type = res?.status !== 'OK' ? 'error' : 'success';
                    const title = res?.status !== 'OK' ? 'THẤT BẠI' : 'Đổi mật khẩu thành công';
                    const text = res?.message ??
                        'Bạn đã đổi mật khẩu thành công, vui lòng đăng nhập lại để tiếp tục.';
                    if (res?.status === 'OK') {
                        window.Toast({
                            title,
                            text,
                            type
                        });
                        setTimeout(async () => {
                            await submitData('/logout')
                            window.location.href = '/?modal=login'
                        }, 3000);
                        return;
                    }
                    window.dispatchEvent(
                        new CustomEvent("swal:confirm", {
                            detail: {
                                title: 'Đổi mật khẩu thất bại',
                                html: res?.message ??
                                    'Có lỗi trong quá trình đổi mật khẩu. Vui lòng thử lại.',
                                icon: 'success',
                                customClass: {
                                    icon: 'update-password-error'
                                },
                                confirmButtonText: 'Đóng',
                                cancelButtonText: 'Thử Lại'
                            }
                        })
                    );
                }
                buttonSubmit.removeClass('is-loading');
            });

            $.validator.addMethod("notEqualTo", function(value, element, param) {
                return this.optional(element) || value !== $(param).val();
            }, "Mật khẩu mới không được trùng với mật khẩu hiện tại");
        })
    </script>
@endPushOnce
