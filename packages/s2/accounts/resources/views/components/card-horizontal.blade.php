@props([
    'name',
    'image',
    'id',
    'partner',
    'partner_txt',
    'is_favorite',
    'partner_provider' => '',
    'isShowGameName' => true,
    'lobbyType' => 'game',
    'isInlineInfo' => false,
    'isHiddenGameName' => true,
    'imageClass' => '',
    'tags' => '',
    'favoriteOnTop' => false,
    'category' => '',
    'deny_info' => false,
])

@php
    $providerLogo = null;
@endphp
<div
    class="card-game-horizontal relative flex cursor-pointer items-center gap-3 overflow-hidden rounded-2xl bg-[#12312B] p-2 pl-3 transition-all duration-300 xl:p-3 xl:hover:bg-[#12312B] xl:gap-3"
    data-name="{{ $name }}"
    data-image="{{ $image }}"
    data-partner-provider="{{ $partner_provider }}"
    data-id="{{ $id }}"
    data-partner="{{ $partner }}"
    data-lobby-type="{{ $lobbyType }}"
    data-deny-info="{{ $deny_info }}"
    onclick="window.handleGetGameUrl(this, event)">
    <div class="w-[70px] xl:w-[112px] shrink-0">
        <img @class([
            'aspect-[70/92] xl:aspect-[112/146] w-full game-card__thumb min-w-full rounded-lg object-cover',
            $imageClass,
        ]) src="{{ $image }}" alt="{{ $name }} {{ $id }}"
            loading="lazy" quality="50" format="webp" sizes="(max-width: 600px) 100vw, 50vw"
            onerror="this.src ='/asset/images/game-default.png'" />
    </div>
    <div class="grow space-y-1 xl:py-2">
        <p class="line-clamp-1 break-all text-[10px] font-bold uppercase leading-[12px] text-[#6CFE00]">
            {{ $category }}</p>
        <p class="line-clamp-1 break-all text-sm font-semibold capitalize text-white xl:text-[14px] xl:leading-[18px]">
            {{ mb_strtolower($name) }}</p>
        <p class="line-clamp-1 break-all text-xs font-medium uppercase text-[#AFBBB7]">
            {{ $partner_txt }}</p>
    </div>
    <i class="icon-chevron-right block text-[24px] text-[#465F59]"></i>
</div>
