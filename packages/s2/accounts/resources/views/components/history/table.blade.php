<!-- type: bet/transaction -->

@props([
    'type' => 'transaction',
    'list' => [],
])

@php
    use S2\Accounts\Enums\TransactionStatus;
    use S2\Accounts\Helpers\BankNameHelper;
    $betHeader = config('account.history.betHeader');
    $transactionHeader = config('account.history.transactionHeader');
    $tableHeader = $type === 'bet' ? $betHeader : $transactionHeader;
    $statusBet = config('account.history.statusBet');
    $typeTransaction = config('account.history.typeTransaction');
    $methodTransaction = config('account.history.methodTransaction');
    $statusTransaction = config('account.history.statusTransaction');
@endphp
<div class="divTable table-account table-{{ $type }} type-{{ $type }} text-xs font-medium max-xl:hidden">
    <div class="divTable__header text-[#AFBBB7]">
        <div class="divTable__tr">
            @foreach ($tableHeader as $index => $item)
                <div class="divTable__td">{{ $item }}</div>
            @endforeach
        </div>
    </div>
    @if (count($list) > 0)
        <div class="divTable__body text-white min-h-[464px]">
            @foreach ($list as $item)
                <div class="divTable__tr bg-[#12312B]">
                    @if ($type === 'bet')
                        <div class="divTable__td">
                            {{ $item->id }}
                        </div>
                        <div class="divTable__td">
                            <span class="js-time-convert" data-time="{{ $item->created_time }}">
                                {{ format_date_vn($item->created_time, 'd/m/Y H:i') }}
                            </span>
                        </div>
                        <div class="divTable__td">
                            {{ $item->product ?? '' }}
                        </div>
                        <div class="divTable__td">
                            {{ addSymbolToNumber($item->stake ?? 0) }}
                        </div>
                        <div class="divTable__td">
                            {{ addSymbolToNumber($item->winlost ?? 0) }}
                        </div>
                        <div class="divTable__td">
                            {{ addSymbolToNumber($item->turnover_rolling ?? 0) }}
                        </div>
                        <div class="divTable__td history--{{ strtolower($item->ticket_status ?? '') }}">
                            <div class="history-status {{ TransactionStatus::getStatusClass($item->ticket_status ?? '', true) }} inline-flex items-center gap-1 rounded-full text-[10px] font-medium leading-[15px]">
                                {{ TransactionStatus::getStatusText($item->ticket_status ?? '', true) ?? '' }}
                            </div>
                        </div>
                    @else
                        @php
                            $isShowInfoCard = isset($item->method) && $item->method === 'phone_card' && $item->status === 'FINISHED' && $item->action === 'WITHDRAW';
                        @endphp
                        <div class="divTable__td">
                            <span class="js-time-convert" data-time="{{ $item->created_time }}">
                                {{ format_date_vn($item->created_time, 'd/m/Y H:i') }}
                            </span>
                        </div>
                        <div class="divTable__td">
                            {{ $typeTransaction[$item->type] ?? $typeTransaction[$item->action ?? ''] ?? '' }}
                        </div>
                        <div class="divTable__td uppercase">
                            {{ BankNameHelper::getBankName($item) }}
                        </div>
                        <div class="divTable__td flex items-center gap-1 capitalize [&[data-method='p2p']]:!uppercase" data-method="{{ $item->method ?? '' }}">
                            {{ $item->method_txt ?? '' }}
                            @if (isset($item->live_check))
                                <a href="{{ $item->live_check }}" class="ml-[4px]" target="_blank">
                                    <img class="size-4 rounded-xl bg-[#fde9bf] p-[2px]"
                                        src="{{ asset('/vendor/accounts/images/history/icon-link.svg') }}"
                                        alt="link" />
                                </a>
                            @endif
                        </div>
                        <div class="divTable__td">
                            {{ addSymbolToNumber($item?->amount_txt ?? 0) }}
                        </div>
                        <div class="divTable__td">
                            {{ $item->id }}
                        </div>
                        <div
                            class="divTable__td js-info-card relative flex items-center justify-end gap-1">
                            <div
                            @class([
                                'history-status',
                                TransactionStatus::getStatusClass($item->status ?? ''),
                                'underline cursor-pointer js-info-card-btn' => $isShowInfoCard,
                            ])>
                                {{ TransactionStatus::getStatusText($item->status ?? '') ?? '' }}
                            </div>

                            @if ($isShowInfoCard)
                                <div
                                    class="js-info-card-content left-unset absolute right-[calc(50%_-_112px)] xl:-right-[42px] bottom-[44px] hidden w-[234px] z-[103] rounded-lg px-3 pt-2.5 pb-3 text-xs font-medium text-black-650 shadow-[0px_1px_20px_0px_#********]">
                                    <x-accounts::history.phone-card-info
                                        :item="$item"></x-accounts::history.phone-card-info>
                                </div>
                            @endif
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    @else
        <div class="flex min-h-[464px] h-full flex-grow flex-col items-center justify-center gap-2">
            <i class="icon-nodata text-[70px] text-[#465F59]"></i>
            <p class="text-xs font-normal text-[#AFBBB7]">Không có dữ liệu</p>
        </div>
    @endif
</div>
