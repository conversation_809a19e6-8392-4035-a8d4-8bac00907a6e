@props(['item'])
@php
    $cardId = $item->id . '-' . random_int(1, 1000);
    $isJsonString = isset($item->card_serial) && is_string($item->card_serial) && str_starts_with($item->card_serial, '[');
    $cards = $isJsonString
        ? json_decode($item->card_serial, true)
        : [['serial' => $item->card_serial ?? '', 'pincode' => $item->card_code ?? '']];
@endphp

<div class="history-tooltip text-[#AFBBB7] xl:after:absolute xl:after:inset-0 xl:after:rounded-lg xl:after:bg-[#00221B]"
    data-id="{{ $cardId }}">
    <div
        class="relative z-[1] mb-5 w-full text-[14px] max-xl:py-[7px] xl:text-left font-medium leading-[18px] text-account-text-primary xl:mb-1">
        {{ $item->card_provider }}
    </div>

    <div id="cards-container-{{ $cardId }}" class="relative z-[1]" data-cards='@json($cards)'
        data-is-json-string="{{ $isJsonString }}">
    </div>

    @if ($isJsonString && count($cards) > 1)
        <div class="swiper swiper-{{ $cardId }} mt-[29px] w-full xl:mt-2">
            <div @class(['swiper-wrapper', 'justify-center' => count($cards) < 8]) id="pagination-{{ $cardId }}">
            </div>
        </div>
    @endif
    <div
        class="absolute -bottom-[5px] left-1/2 xl:left-2/3 size-[11px] -translate-x-1/2 rotate-45 bg-[#00221B] shadow-[0px_1px_20px_0px_#********] max-xl:hidden">
    </div>
</div>
