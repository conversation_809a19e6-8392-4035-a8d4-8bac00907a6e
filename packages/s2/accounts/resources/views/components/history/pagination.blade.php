@php
    $isFirst = (int) $pagination['current'] === 1;
    $isLast = (int) $pagination['current'] === (int) $pagination['last'];
@endphp
@if ($pagination['last'] > 1)
    <div class="flex items-center justify-between">
        <p class="text-xs font-medium text-[#AFBBB7]">Trang {{ $pagination['current'] }}/{{ $pagination['last'] }}</p>

        <div class="flex items-center gap-[8px]">
            <a href="?tab={{ $selectedTab }}&page={{ $pagination['current'] - 1 }}" @class([
                'flex justify-center items-center w-[32px] h-[32px] rounded-[8px] bg-[#23413A]',
                $isFirst ? 'pointer-events-none' : '[&_i]:text-white',
            ])>
                <i class="icon-chevron-left text-[16px] text-[#465F59]"></i>
            </a>
            <div
                class="flex h-[32px] min-w-[48px] items-center justify-center rounded-[8px] px-[12px] text-[12px] font-semibold leading-[16px] text-white bg-[#23413A]">
                <p class="text-primary">{{ $pagination['current'] }}</p>
                <p>/</p>
                <p>{{ $pagination['last'] }}</p>
            </div>
            <a href="?tab={{ $selectedTab }}&page={{ $pagination['current'] + 1 }}" @class([
                'flex justify-center items-center w-[32px] h-[32px] rounded-[8px] bg-[#23413A]',
                $isLast ? 'pointer-events-none' : '[&_i]:text-white',
            ])>
                <i class="icon-chevron-right text-[16px] text-[#465F59]"></i>
            </a>
        </div>
    </div>
@endif
