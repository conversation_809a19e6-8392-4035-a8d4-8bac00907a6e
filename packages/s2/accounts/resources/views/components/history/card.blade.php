<!-- type: bet/transaction -->

@props([
    'type' => 'transaction',
    'item' => null,
])

@php
    use S2\Accounts\Enums\TransactionStatus;
    use S2\Accounts\Helpers\BankNameHelper;
    $statusBet = config('account.history.statusBet');
    $statusTransaction = config('account.history.statusTransaction');
    $typeTransaction = config('account.history.typeTransaction');
    $methodTransaction = config('account.history.methodTransaction');
@endphp
<div class="js-info-card card-item type-{{ $type }} border-b border-[#12312B] py-2.5 text-xs text-[#AFBBB7]">
    @if ($type === 'bet')
        <div class="px-3">
            <div class="card-item__header mb-1.5 flex items-center justify-between">
                <button type="button"
                    class="js-copy-btn flex items-center gap-[2px] rounded-full bg-[#23413A] px-2 py-[2px] text-[10px] font-medium leading-[15px] text-[#AFBBB7] [&.active]:bg-[#E1C081] [&.active]:text-white"
                    data-copy="{{ $item->id }}">
                    <i class="icon-copy text-[12px]"></i>
                    {{ $item->id }}
                </button>
                <div class="js-toggle-history-card flex items-center gap-1">
                    <span class="js-time-convert [word-spacing:4px]" data-time="{{ $item->created_time }}">
                        {{ format_date_vn($item->created_time, 'd/m/Y H:i') }}
                    </span>
                    <i class="icon-chevron-down text-[18px] text-[#8C9D98] transition-all"></i>
                </div>
            </div>
            <div class="js-history-card-header flex flex-col gap-1.5">
                <div class="flex min-h-5 items-center justify-between gap-2">
                    <div class="break-all text-white">
                        {{ $item->product ?? '' }}
                    </div>
                    <div class="history-status {{ TransactionStatus::getStatusClass($item->ticket_status ?? '', true) }} inline-flex items-center gap-1 rounded-full text-[10px] leading-[15px]">
                        {{ TransactionStatus::getStatusText($item->ticket_status ?? '', true) ?? '' }}
                    </div>
                </div>
                <div class="flex min-h-5 items-center justify-between">
                    <div class="flex items-center gap-1 text-white">
                        Thắng/Thua
                    </div>
                    <div class="font-semibold text-account-text-primary">
                        {{ addSymbolToNumber($item->winlost ?? 0) }}
                    </div>
                </div>
            </div>
        </div>
        <div class="card-item__body js-history-card-body space-y-1.5 px-3 text-white"
            style="display: none;">
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Game</div>
                <div class="break-all text-account-text-primary">
                    {{ $item->product ?? '' }}
                </div>
            </div>
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Số tiền</div>
                <div class="font-medium text-account-text-primary">
                    {{ addSymbolToNumber($item->stake ?? 0) }}
                </div>
            </div>
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Thắng/Thua</div>
                <div class="font-medium text-account-text-primary">
                    {{ addSymbolToNumber($item->winlost ?? 0) }}
                </div>
            </div>
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Turnover</div>
                <div class="font-medium text-account-text-primary">
                    {{ addSymbolToNumber($item->turnover_rolling ?? 0) }}
                </div>
            </div>
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Trạng thái</div>
                <div class="history-status {{ TransactionStatus::getStatusClass($item->ticket_status ?? '', true) }} inline-flex items-center gap-1 rounded-full text-[10px] leading-[15px]">
                    {{ TransactionStatus::getStatusText($item->ticket_status ?? '', true) ?? '' }}
                </div>
            </div>
        </div>
    @else
        @php
            $isShowInfoCard = isset($item->method) && $item->method === 'phone_card' && $item->status === 'FINISHED' && $item->action === 'WITHDRAW';
        @endphp
        <div class="px-3">
            <div class="card-item__header mb-1.5 flex items-center justify-between">
                <button type="button"
                    class="js-copy-btn flex items-center gap-[2px] rounded-full bg-[#23413A] px-2 py-[2px] text-[10px] font-medium leading-[16px] text-[#AFBBB7] [&.active]:bg-[#E1C081] [&.active]:text-white"
                    data-copy="{{ $item->id }}">
                    <i class="icon-copy text-[12px]"></i>
                    {{ $item->id }}
                </button>
                <div class="js-toggle-history-card flex items-center gap-1">
                    <span class="js-time-convert [word-spacing:4px]" data-time="{{ $item->created_time }}">
                        {{ format_date_vn($item->created_time, 'd/m/Y H:i') }}
                    </span>
                    <i class="icon-chevron-down text-[18px] text-[#8C9D98] transition-all"></i>
                </div>
            </div>
            <div class="js-history-card-header flex flex-col gap-1.5">
                <div class="flex min-h-5 items-center justify-between">
                    <div class="flex items-center gap-1">
                        <i class="icon-{{ strtolower($item->action ?? '') }}-v2 text-white"></i>
                        <div class="capitalize text-account-text-primary">
                            {{ $typeTransaction[$item->type] ?? $typeTransaction[$item->action ?? ''] ?? '' }}
                        </div>
                    </div>
                    <div class="font-semibold text-account-text-primary">
                        {{ addSymbolToNumber($item->amount_txt ?? 0) }}
                    </div>
                </div>
                <div class="flex min-h-5 items-center justify-between">
                    <div class="flex items-center gap-1">
                        <div class="rounded-full bg-[#23413A] text-[#FFFFFF] capitalize px-[6px] py-0.5 text-xs font-normal">
                            {{ $item->method_txt ?? '' }}
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div
                        @class([
                            'history-status',
                            TransactionStatus::getStatusClass($item->status ?? ''),
                            'underline cursor-pointer js-info-card-btn' => $isShowInfoCard,
                        ])>
                            {{ TransactionStatus::getStatusText($item->status ?? '') ?? '' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-item__body js-history-card-body space-y-1.5 px-3 text-white"
            style="display: none;">
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Loại</div>
                <div class="flex items-center gap-1">
                    <i class="icon-{{ strtolower($item->action ?? '') }}-v2 text-white"></i>
                    <div class="capitalize text-account-text-primary">
                        {{ $typeTransaction[$item->type] ?? $typeTransaction[$item->action ?? ''] ?? '' }}
                    </div>
                </div>
            </div>
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Phương thức</div>
                <div class="flex items-center gap-1 capitalize rounded-full bg-[#23413A] px-2 py-[2px] text-white">
                    {{ $item->method_txt ?? '' }}
                </div>
            </div>
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Ngân hàng</div>
                <div class="flex items-center gap-1 text-account-text-primary uppercase">
                    @if (isset($item->to_bank_code))
                        <img src="{{ asset('vendor/accounts/images/banks/' . strtolower($item->to_bank_code) . '.svg') }}"
                            alt="{{ $item->to_bank_code }}" class="size-4 object-contain" />
                    @endif
                    {{ BankNameHelper::getBankName($item) }}
                </div>
            </div>
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Số tiền</div>
                <div class="flex items-center gap-1 font-semibold text-account-text-primary">
                    {{ addSymbolToNumber($item->amount_txt ?? 0) }}
                </div>
            </div>
            <div class="flex min-h-5 items-center justify-between gap-1.5">
                <div class="capitalize">Trạng thái</div>
                <div
                @class([
                    'history-status',
                    TransactionStatus::getStatusClass($item->status ?? ''),
                    'underline cursor-pointer js-info-card-btn' => $isShowInfoCard,
                ])>
                    {{ TransactionStatus::getStatusText($item->status ?? '') ?? '' }}
                </div>
            </div>
        </div>
            @if ($isShowInfoCard)
                <div
                class="js-info-card-content history-tooltip left-unset fixed bottom-0 left-0 z-[108] hidden w-full whitespace-nowrap rounded-t-3xl bg-account-surface-primary max-xl:pt-3 px-4 py-5 text-[14px] leading-[18px] xl:text-sm font-medium text-black-650 shadow-[0px_2px_4px_0px_#2221211A] max-xl:border-t max-xl:border-[#23413A]">

                <button type="button"
                    class="js-info-card-close group absolute right-[16px] top-[12px] z-[103] inline-flex size-[32px] items-center justify-center rounded-lg bg-[#23413A] text-sm text-gray-600 transition-all duration-300">
                    <i
                        class="icon-close text-[16px] text-neutral-300 transition-all duration-300 group-hover:text-neutral-350"></i>
                </button>
                <x-accounts::history.phone-card-info :item="$item"></x-accounts::history.phone-card-info>
            </div>
        @endif
    @endif
</div>
@pushOnce('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.js-toggle-history-card').forEach(function(toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    const card = this.closest('.card-item');
                    const header = card.querySelector('.js-history-card-header');
                    const body = card.querySelector('.js-history-card-body');
                    const icon = this.querySelector('.icon-chevron-down');

                    // Toggle giữa header và body
                    if (body.style.display === 'none') {
                        // Hiện body, ẩn header
                        body.style.display = 'block';
                        header.style.display = 'none';
                        icon.style.transform = 'rotate(180deg)';
                        card.classList.add('bg-neutral-650');
                    } else {
                        // Hiện header, ẩn body
                        body.style.display = 'none';
                        header.style.display = 'flex';
                        icon.style.transform = 'rotate(0deg)';
                        card.classList.remove('bg-neutral-650');
                    }
                });
            });
        });
    </script>
@endPushOnce
