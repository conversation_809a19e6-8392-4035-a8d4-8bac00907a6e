@props([
    'amountList' => [],
    'defaultAmount' => '',
    'itemClass' => '',
    'suggestAmount' => '',
])

<div class="grid grid-cols-4 gap-2 xl:grid-cols-6">
    @foreach ($amountList as $amount)
        <button
            type="button"
            data-amount="{{ number_format($amount) }}"
            data-value="{{ $amount }}"
            @class([
                "js-amount-item account-active-button relative flex h-[38px] flex-col items-center justify-center rounded bg-neutral-400 px-1 py-2.5 text-xs text-neutral xl:hover:text-neutral shadow-[0_0_0_1px_#23413A]
                    xl:h-[40px] xl:p-2.5 xl:text-sm overflow-hidden",
                "active" => $amount == $defaultAmount,
                $itemClass,
            ])
        >
            @if (!empty($suggestAmount) && $amount === $suggestAmount)
                <span
                    class="absolute right-0 top-0 h-[12px] min-w-[38px] rounded-bl-[8px] bg-[#FABA53] px-1 text-[8px] font-medium leading-[12px] text-neutral-800"
                >
                    <PERSON><PERSON> xuất
                </span>
            @endif
            <span class="xl:whitespace-nowrap">{{ number_format($amount) }} {{ config('app.currency') }}</span>
        </button>
    @endforeach
</div>