@props([
    'data' => [],
    'isPageOverview' => false,
])
@php
    $MIN_VALUE = 0;
    $percent = 0;
    $finalPercent = $MIN_VALUE;
    $listBonus = [];
    $isHaveProgress = isset($data['week_cashback_level']) || isset($data['addition_cashback_level']);
    if ($isHaveProgress) {
        $percent = $data['week_percent'] ?? ($data['percentAdditionalBonus'] ?? 0);
        $listBonus = $data['week_cashback_level'] ?? ($data['addition_cashback_level'] ?? []);
        usort($listBonus, function ($a, $b) {
            return $a->percent <=> $b->percent;
        });
        $index = array_search($percent, array_column(array_values($listBonus), 'percent'));

        if (is_numeric($index) && $index !== false) {
            $finalPercent = max((($index + 1) / count($listBonus)) * 100, $MIN_VALUE);
        }
    }
@endphp
<div>
    <a href="{{ $data['url'] ?? '' }}" class="">
        <div class="flex items-center">
            <picture
                class="aspect-[100/80] h-[80px] shrink-0 overflow-hidden rounded-[12px] object-contain xl:aspect-[210/118] xl:h-[118px]">
                <source srcset="{{ asset($data['imgSrcMobile'] ?? '') }}" media="(max-width: 1199px)">
                <img src="{{ asset($data['imgSrc'] ?? '') }}" alt="promotion-used" class="size-full" />
            </picture>
            <div class="flex flex-1 flex-col justify-center pl-2.5 xl:min-h-[118px] xl:pl-4">
                <div
                    class="text-[14px] font-semibold capitalize leading-[18px] text-white xl:mb-1 xl:text-[16px] xl:leading-[20px]">
                    {{ $data['title'] ?? '' }}
                </div>
                <div class="text-xs text-[#AFBBB7] xl:text-[14px] xl:leading-[18px]">
                    {{ $data['description'] ?? '' }}
                </div>
            </div>
        </div>
    </a>
    @if ($isHaveProgress)
        <x-accounts::promotion.promotion-additional class="h-[42px] xl:h-[58px] mt-2" :data="$data"
            :listBonus="$listBonus" :finalPercent="$finalPercent" />
    @elseif ($data['package_id'] === config('constants.PROMOTION_PLAN_TYPE.COMMISSION'))
        <div class="h-[42px] xl:h-[58px] mt-2"></div>
    @endif
</div>
