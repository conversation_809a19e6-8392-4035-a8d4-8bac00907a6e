@section('beforeBodyClose')
<div class="js-chart-bar-backdrop fixed inset-0 bg-black/50 z-[107] hidden"></div>
    <div
        class="js-chart-bar-detail xl:hidden chart-bar-detail pointer-events-none fixed -bottom-0 left-0 z-[110] h-[198px] w-full rounded-t-3xl p-6 text-xs opacity-0">
        <div
            class="js-chart-bar-close absolute right-[10px] top-[10px] flex size-8 items-center justify-center rounded-lg bg-[#23413A] text-white">
            <span class="icon-close text-[20px]"></span>
        </div>
        <p class="mb-4 text-[18px] font-semibold leading-[22px]">HOÀN TRẢ NGÀY <span class="js-chart-bar-date"></span></p>

        <div class="flex flex-col gap-4 text-xs text-white">
            <div class="flex items-center gap-1">
                <div class="size-[14px] rounded-sm bg-account-chart-bet"></div>
                <div class="flex w-[67px] min-w-max justify-between font-medium">
                    Tổng cược<span>:</span>
                </div>

                <div class="js-chart-bar-bet text-nowrap font-normal"></div>
            </div>
            <div class="flex items-center gap-1">
                <div class="size-[14px] rounded-sm bg-account-chart-return"></div>
                <div class="flex w-[67px] min-w-max justify-between font-medium">
                    Hoàn trả <span>:</span>
                </div>
                <div class="js-chart-bar-return text-nowrap font-normal"></div>
            </div>
        </div>

        <x-kit.button class="js-chart-bar-close mt-6 h-[40px] w-full text-[14px] leading-[18px]">
            Đóng
        </x-kit.button>
    </div>
@endsection
