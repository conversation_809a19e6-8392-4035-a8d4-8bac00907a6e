@props([
    'otherPromotions' => [],
])
<div class="relative mt-3 rounded-xl bg-account-surface-primary p-3 xl:mt-8 xl:rounded-none xl:bg-inherit xl:p-0 xl:pb-0">
    <div class="mb-4 flex items-center justify-between xl:mb-[10px]">
        <div class="flex items-center gap-2">
            <div class="flex size-6 items-center justify-center rounded-lg">
                <img src="{{ asset('vendor/accounts/images/promotion/promotion-title.svg') }}" alt="promotion-used" class="size-full object-contain" />
            </div>
            <div class="text-sm font-medium capitalize text-account-text-primary">
                <PERSON><PERSON><PERSON> khuyến mãi khác
            </div>
        </div>
        <a href="{{ route('events', ['tab' => 'promotions']) }}" class="group relative z-10 flex cursor-pointer items-center gap-1.5">
            <div class="text-xs font-medium text-account-text-brand-primary group-hover:text-account-text-secondary capitalize">Xem thêm</div>
            <i class="icon-chevron-left rotate-180 text-[20px] text-account-text-brand-primary group-hover:text-account-text-secondary"></i>
        </a>
    </div>
    <div class="max-w-full">
        <x-accounts::promotion.promotion-swipper :promotions="$otherPromotions" />
    </div>
    <div class="swiper-pagination promotions-other-swiper-pagination pb-3 xl:hidden"
        style="--swiper-pagination-bullet-inactive-color: #F1F3F9;--swiper-pagination-bullet-inactive-opacity: 1;--swiper-pagination-color:#CDA964">
    </div>
</div>
