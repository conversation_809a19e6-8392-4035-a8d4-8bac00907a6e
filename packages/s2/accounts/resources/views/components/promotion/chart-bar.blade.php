@props([
    'dataSet' => [],
    'lobby' => 'overview', // overview, promotion
])
@php
    $isPageOverview = $lobby === 'overview';
@endphp
<div class="relative w-full">
    @if (!empty($dataSet) && count($dataSet) > 0)
        <div @class([
            'grid grid-cols-7 justify-between divide-x border w-full bg-[#00221B] rounded-lg h-[142px] max-xl:h-[158px] divide-[#23413A] border-[#23413A]',
            'h-[138px] xl:h-[138px]' => !$isPageOverview,
        ])>
            @foreach ($dataSet as $data)
                <div @class([
                    "js-chart-bar relative group flex w-full flex-col items-center justify-end pb-7
                                                                                                        first:rounded-l-lg last:rounded-r-lg xl:hover:bg-[#12312B] text-white xl:hover:text-primary",
                    '' => !$isPageOverview,
                ]) data-date="{{ $data['date'] }}"
                    data-bet="{{ formatNumberWithSymbol($data['bet']) }}"
                    data-return="{{ formatNumberWithSymbol($data['return']) }}">
                    <div
                        class="js-chart-bar-detail invisible absolute bottom-[calc(100%-10px)] left-1/2 z-10 inline-block min-w-[166px] -translate-x-1/2 rounded-lg bg-[#23413A] px-3 py-2.5 text-sm font-medium shadow-[0px_2px_4px_0px_#2221211A] before:absolute before:left-1/2 before:top-full before:-translate-x-1/2 before:border-8 before:border-transparent before:border-t-[#23413A] before:content-[''] group-first:left-0 group-first:translate-x-0 group-first:before:left-[44px] group-first:before:translate-x-0 group-last:left-auto group-last:right-0 group-last:translate-x-0 group-last:before:left-auto group-last:before:right-[44px] group-last:before:translate-x-0 xl:group-hover:visible">
                        <p class="mb-1 text-xs font-medium text-[#AFBBB7]">
                            NGÀY {{ $data['date'] }}
                        </p>
                        <div class="flex flex-col gap-1 text-xs text-white">
                            <div class="flex items-center gap-1">
                                <div class="size-[14px] rounded-sm bg-account-chart-bet"></div>
                                <div class="flex w-[67px] min-w-max justify-between font-medium">
                                    Tổng cược<span>:</span>
                                </div>

                                <div class="text-nowrap font-normal">
                                    {{ formatNumberWithSymbol($data['bet']) }}
                                </div>
                            </div>
                            <div class="flex items-center gap-1">
                                <div class="size-[14px] rounded-sm bg-account-chart-return"></div>
                                <div class="flex w-[67px] min-w-max justify-between font-medium">
                                    Hoàn trả <span>:</span>
                                </div>
                                <div class="text-nowrap font-normal">
                                    {{ formatNumberWithSymbol($data['return']) }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-2 flex h-full w-[30px] flex-col justify-end">
                        <div @class([
                            'w-full return-bar bg-account-chart-return',
                            'min-h-[1px]' => $data['return'] > 0,
                        ]) style="height: {{ $data['returnHeight'] }}%;"></div>
                        <div @class([
                            'bet-bar w-full bg-account-chart-bet',
                            'min-h-[2px]' => $data['bet'] > 0,
                        ]) style="height: {{ $data['betHeight'] }}%;"></div>
                    </div>

                    <div class="item-date absolute bottom-2 left-1/2 -translate-x-1/2 text-xs font-normal">
                        {{ $data['date'] }}
                    </div>
                </div>
            @endforeach
        </div>
        <div @class([
            'flex justify-center items-center w-full gap-6 mt-4 xl:mt-3',
            'xl:justify-start' => !$isPageOverview,
        ])>
            <div class="flex items-center gap-1">
                <div class="size-4 rounded-sm bg-account-chart-bet"></div>
                <div class="text-xs font-normal text-white">Tổng cược</div>
            </div>
            <div class="flex items-center gap-1">
                <div class="size-4 rounded-sm bg-account-chart-return"></div>
                <div class="text-xs font-normal text-white">Hoàn trả</div>
            </div>
        </div>
    @else
        <div class="text-center text-sm text-account-text-secondary">Không có dữ liệu</div>
    @endif
</div>

{{-- chart-bar-detail mobile --}}
<x-accounts::promotion.chart-bar-info-popup-mobile />
