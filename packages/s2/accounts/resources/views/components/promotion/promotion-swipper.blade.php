@props(['promotions' => []])

<section>
    @if (!empty($promotions))
        <div class="swiper promotions-swiper max-xl:pb-[20px]" style="--swiper-scrollbar-drag-bg-color: #6CFE00;">
            <div class="swiper-wrapper">
                @foreach ($promotions as $promotion)
                    <div @class([
                        'swiper-slide',
                        $promotion['customWrapperClass'] ?? 'w-[161px] xl:w-[328px]',
                    ])>
                        <div @class([
                            'group relative h-full',
                            $promotion['customClass'] ??
                            'rounded-2xl xl:rounded-[24px] aspect-[161/168] xl:aspect-[328/310]',
                        ])>

                            <picture class="block size-full overflow-hidden rounded-[inherit]">
                                <source srcset="{{ $promotion['imgSrcMobile'] }}" media="(max-width: 1199px)">
                                <img src="{{ $promotion['imgSrc'] }}" alt="promotion"
                                    class="h-full object-cover group-hover:hidden max-xl:hidden">
                                <img src="{{ $promotion['imgSrcHover'] }}" alt="promotion"
                                    class="xl:hidden h-full object-cover group-hover:block">
                            </picture>
                            @if (!$promotion['isOnlyShowImage'])
                                <div
                                    class="absolute bottom-0 left-0 right-0 top-0 rounded-[inherit] p-3 transition-all max-xl:bg-black/50 xl:p-6 xl:group-hover:rounded-[15px] xl:group-hover:[box-shadow:inset_0_0_0_1px_#6CFE00]">
                                    <div
                                        class="mb-2 hidden text-[10px] font-bold uppercase leading-[15px] text-primary-500 xl:block">
                                        {{ $promotion['type'] }}
                                    </div>
                                    <div
                                        class="mb-1 line-clamp-1 text-xs font-semibold capitalize text-white xl:mb-2 xl:line-clamp-none xl:text-[20px] xl:leading-[26px]">
                                        <span @class([
                                            'hidden xl:block capitalize' => isset($promotion['mbTitle']),
                                        ])>
                                            {{ $promotion['title'] }}
                                        </span>
                                        @if (isset($promotion['mbTitle']))
                                            <span class="block capitalize xl:hidden">{{ $promotion['mbTitle'] }}</span>
                                        @endif
                                    </div>
                                    <div
                                        class="mb-1 text-[10px] leading-[15px] text-neutral-300 xl:mb-2 xl:text-sm xl:leading-[20px]">
                                        <span class="max-xl:hidden">
                                            {{ $promotion['description'] }}
                                        </span>
                                        @if (isset($promotion['descriptionMobile']))
                                            <span class="block xl:hidden">{{ $promotion['descriptionMobile'] }}</span>
                                        @endif
                                    </div>
                                    <a href="{{ $promotion['url'] }}"
                                        class="btn btn-primary btn-medium text-account-text-primary max-xl:h-[28px] max-xl:rounded-lg max-xl:px-3 max-xl:py-1.5 max-xl:text-xs xl:translate-y-2 xl:opacity-0 xl:transition-all xl:group-hover:translate-y-0 xl:group-hover:opacity-100">
                                        {{ __('promotions.apply_now') }}
                                    </a>
                                </div>
                            @else
                                <a href="{{ $promotion['url'] }}"
                                    class="absolute inset-0 z-[1] rounded-2xl transition-all xl:rounded-[16px] xl:group-hover:[box-shadow:inset_0_0_0_1px_#6CFE00]">
                                </a>
                            @endif

                        </div>
                    </div>
                @endforeach
            </div>
            <div class="js-promotions-other-swiper-scrollbar swiper-scrollbar xl:hidden"></div>
        </div>
    @endif
    @pushOnce('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                new Swiper('.promotions-swiper', {
                    slidesPerView: 'auto',
                    breakpoints: {
                        1199: {
                            spaceBetween: 20,
                        },
                        0: {
                            spaceBetween: 12,
                        },
                    },
                    scrollbar: {
                        el: '.js-promotions-other-swiper-scrollbar',
                        hide: false,
                    },
                });
            });
        </script>
    @endPushOnce
</section>
