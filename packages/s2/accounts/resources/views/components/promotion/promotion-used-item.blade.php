@props([
    'data' => [],
    'chartData' => [],
    'class' => '',
    'slotsInfo' => [],
    'casinoInfo' => [],
    'lobby' => 'overview', // promotion, overview
])
@php
    use Carbon\Carbon;
    $isPageOverview = $lobby === 'overview';
    $uuid = uniqid();
    $swiperId = 'swiper-' . $uuid;
    $paginationId = 'pagination-' . $uuid;
    $itemClass =
        'rounded-xl pl-4 text-[10px] font-medium uppercase leading-[16px] text-account-text-secondary xl:px-3 py-0 xl:text-[10px] xl:font-medium xl:leading-[16px] h-[68px] flex flex-col justify-center gap-2';
    $itemBg = 'bg-[#12312B]';

    if ($data['package_id'] === config('constants.PROMOTION_PLAN_TYPE.WELCOME')) {
        if (!$isPageOverview) {
            $itemClass = $itemClass . ' !h-[60px] xl:!h-[64px] !px-4 xl:!flex-row xl:items-center xl:justify-between';
        } else {
            $itemClass = $itemClass . ' !h-[60px]';
        }
    } else {
        if (!$isPageOverview) {
            $itemClass = $itemClass . ' !h-[65px] xl:!flex-row xl:items-center xl:justify-between';
        }
    }

    $itemClass = twMerge($itemClass, $itemBg);

    $welcomeCreatedTime = Carbon::parse($data['created_time'] ?? '')->format('d/m/Y');
    $welcomeEndTime = Carbon::parse($data['end_time'] ?? '')->format('d/m/Y');
@endphp
<div @class([
    'rounded-[24px] flex flex-col bg-account-surface-primary px-3 pt-1.5 pb-3 xl:p-6',
    'xl:bg-[#00221B]' => !$isPageOverview,
    $class,
])>
    @if ($data['package_id'] === config('constants.PROMOTION_PLAN_TYPE.COMMISSION'))
        @php
            $today_bet = 0;
            $today_return = 0;
            $total_return = 0;
            if (!empty($chartData) && count($chartData) > 0) {
                $today_data = end($chartData);
                $today_bet = $today_data['bet'] ?? 0;
                $today_return = $today_data['return'] ?? 0;
                $total_return =
                    array_sum(array_column($chartData, 'return')) > 0
                        ? number_format((float) array_sum(array_column($chartData, 'return')), 2, '.', ',')
                        : 0;
            }
            $promotionSwiper = array_filter([$data, $slotsInfo, $casinoInfo]);
        @endphp

        <div class="xl:mb-3 order-1 mb-4">
            <div id="{{ $swiperId }}" class="swiper swiper-container swiper-promotion pb-5 xl:pb-[18px]">
                <div class="swiper-wrapper">
                    @foreach ($promotionSwiper as $item)
                        <div class="swiper-slide">
                            <x-accounts::promotion.commission-item :data="$item" :isPageOverview="$isPageOverview" />
                        </div>
                    @endforeach
                </div>
                <div id="{{ $paginationId }}" class="swiper-pagination flex justify-center"></div>
            </div>
        </div>
        <div @class(['order-2', 'xl:flex xl:gap-6' => !$isPageOverview])>
            <div @class([
                'w-full' => $isPageOverview,
                'xl:w-[626px] 3xl:w-[726px]' => !$isPageOverview,
            ])>
                <x-accounts::promotion.chart-bar :dataSet="$chartData" :lobby="$lobby" />
            </div>
            <div @class([
                'grid grid-cols-2 gap-2 mt-4 xl:mt-3',
                'xl:!mt-0 xl:grid-cols-1 flex-1 h-fit' => !$isPageOverview,
            ])>
                <div class="{{ $itemClass }} max-xl:px-3">
                    <div class="text-[#AFBBB7]">
                        TỔNG CƯỢC HÔM NAY
                    </div>
                    <div
                        class="flex items-center gap-1 text-xs font-bold text-white xl:text-[16px] xl:font-semibold xl:leading-[20px]">
                        <span class="line-clamp-1 break-all">{{ formatNumberWithSymbol($today_bet) }}</span>
                    </div>
                </div>
                <div class="{{ $itemClass }} max-xl:px-3">
                    <div class="text-[#AFBBB7]">
                        HOÀN TRẢ HÔM NAY
                    </div>
                    <div
                        class="flex items-center gap-1 text-xs font-bold text-white xl:text-[16px] xl:font-semibold xl:leading-[20px]">
                        <span class="line-clamp-1 break-all">{{ formatNumberWithSymbol($today_return) }}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="order-0">
            <div
                class="{{ $itemBg }} mb-4 flex h-[52px] items-center justify-between gap-1 rounded-xl px-5 max-xl:h-[68px] xl:mb-3 xl:px-6">
                <p class="text-xs font-medium uppercase text-white xl:text-[10px] xl:leading-[16px] xl:text-[#AFBBB7]">
                    Tổng hoàn trả</p>
                <p
                    class="line-clamp-1 break-all text-[18px] font-semibold leading-[22px] text-white xl:text-[16px] xl:leading-[20px]">
                    {{ addSymbolToNumber($total_return) }}
                </p>
            </div>
        </div>
    @else
        @php
            $deposit_amount = number_format((int) ($data['deposit_amount'] / 1000), 0, ',');
            $bonus_amount = number_format((int) ($data['promotion_amount'] / 1000), 0, ',');
            $bonus_amount_remaining = number_format((int) ($data['turnover'] / 1000), 0, ',');
            $total_bet = number_format((int) ($data['rolling'] / 1000), 0, ',');
            $ratio = $data['rolling'] > 0 ? ($data['turnover'] / $data['rolling']) * (int) $data['multiplier'] : 0;
            $round = min(floor($ratio), (int) $data['multiplier']);
        @endphp
        <x-accounts::promotion.commission-item :data="$data" :isPageOverview="$isPageOverview" />
        <div class="xl:mt-4 order-2 mt-3">
            <div @class([
                'mb-4 grid grid-cols-2 gap-2',
                'xl:!grid-cols-4' => !$isPageOverview,
            ])>
                <div @class([$itemClass, '!gap-0.5'])>
                    <div class="text-[#AFBBB7]">
                        {{ __('promotions.deposit_amount') }}
                    </div>
                    <div
                        class="flex items-center gap-1 text-xs font-medium text-white xl:text-[16px] xl:font-semibold xl:leading-[20px]">
                        <span class="line-clamp-1 break-all">{{ addSymbolToNumber($deposit_amount) }}</span>
                    </div>
                </div>
                <div @class([$itemClass, '!gap-0.5'])>
                    <div class="text-[#AFBBB7]">
                        {{ __('promotions.bonus_amount') }}
                    </div>
                    <div
                        class="flex items-center gap-1 text-xs font-medium text-white xl:text-[16px] xl:font-semibold xl:leading-[20px]">
                        <span class="line-clamp-1 break-all">{{ addSymbolToNumber($bonus_amount) }}</span>
                    </div>
                </div>
                <div @class([$itemClass, '!gap-0.5'])>
                    <div class="text-[#AFBBB7]">
                        {{ __('promotions.bonus_amount_remaining') }}
                    </div>
                    <div
                        class="flex items-center gap-1 text-xs font-medium text-white xl:text-[16px] xl:font-semibold xl:leading-[20px]">
                        <span class="line-clamp-1 break-all">{{ addSymbolToNumber($bonus_amount_remaining) }}</span>
                    </div>
                </div>
                <div @class([$itemClass, '!gap-0.5'])>
                    <div class="text-[#AFBBB7]">
                        TỔNG CƯỢC <br class="max-xl:hidden" />CẦN ĐẠT
                    </div>
                    <div
                        class="flex items-center gap-1 text-xs font-medium text-white xl:text-[16px] xl:font-semibold xl:leading-[20px]">
                        <span class="line-clamp-1 break-all">{{ addSymbolToNumber($total_bet) }}</span>
                    </div>
                </div>
            </div>

            <div @class([
                'mb-2 flex items-center justify-between',
                'max-xl:!mb-2 !mb-3' => !$isPageOverview,
            ])>
                <div class="text-xs font-medium text-white capitalize">
                    Vòng cược
                </div>
                <div class="text-xs font-medium text-[#6CFE00]">
                    {{ $round }}/{{ $data['multiplier'] }} Vòng
                </div>
            </div>

            <div class="relative">
                <progress @class([
                    'progress block mb-4 w-full xl:mb-2 align-middle',
                    '!mb-3' => !$isPageOverview,
                ]) value="{{ $round }}"
                    max="{{ $data['multiplier'] }}"></progress>
                <span class="absolute left-0 top-0 size-3 bg-[#6CFE00] rounded-full {{ $round > 0 ? 'hidden' : 'block'}}"></span>
            </div>

            <div class="flex items-center justify-between text-xs text-[#AFBBB7] xl:text-[14px] xl:leading-[18px]">
                <div>Hạn dùng: <br class="xl:hidden">{{ $welcomeCreatedTime }} - {{ $welcomeEndTime }}</div>
                <p class="cursor-pointer font-medium capitalize max-xl:text-[14px] max-xl:leading-[18px] xl:hover:text-account-text-primary"
                    onclick="openCancelPromoModal()">
                    Hủy khuyến mãi
                </p>
            </div>
        </div>
    @endif
</div>
@pushOnce('css')
    <style>
        .progress {
            height: 12px;
            border-radius: 100px;

            &.sm {
                height: 6px;
            }

            &::-webkit-progress-value {
                background: #6CFE00;
                border-radius: 100px;
            }

            &::-webkit-progress-bar {
                background: #23413A;
                border-radius: 100px;
            }

            &::-moz-progress-bar {
                background: #6CFE00;
                border-radius: 100px;
            }
        }
    </style>
@endpushOnce
@pushOnce('scripts')
    @vite(['resources/js/promotion/index.js'])
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const swiper = new Swiper('#{{ $swiperId }}', {
                slidesPerView: 1,
                pagination: {
                    el: '#{{ $paginationId }}',
                    clickable: true,
                },
                spaceBetween: 10,
                autoHeight: true,
            });
        });
    </script>
@endpushOnce
