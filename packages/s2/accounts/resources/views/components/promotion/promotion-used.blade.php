@props([
    'promotions' => [],
    'commission' => [],
    'slotsInfo' => [],
    'casinoInfo' => []
])
@php
    $user = Auth::user();
    $hasUsedPromotion = $user->package_id ?? null;
    if($hasUsedPromotion === 5){
        $hasUsedPromotion = null;
    }
    $title = $hasUsedPromotion ? 'Khuyến mãi đang Sử dụng' : 'Khuyến mãi';
    $appendText = $hasUsedPromotion ? '('.count($promotions).')' : '';
@endphp

<div class="relative rounded-[16px] bg-account-surface-primary xl:bg-inherit xl:pb-0">
    <div class="mb-[10px] flex items-center justify-between p-3 pb-0 xl:px-0 xl:py-[3px]">
        <div class="flex items-center gap-2">
            <div class="size-6 flex items-center justify-center rounded-lg">
                <img src="{{ asset('vendor/accounts/images/overview/icon-overview-promotion.png') }}" alt="promotion-used" class="size-6 object-contain" />
            </div>
            <div class="text-sm font-medium capitalize text-neutral">
                Khuyến mãi đang Sử dụng
                @if (count($promotions) > 0)
                    <span class="text-primary-400">
                        {{ $appendText }}
                    </span>
                @endif
            </div>
        </div>
        @if (count($promotions) > 2)
            <div class="hidden items-center gap-2 xl:flex">
                <div
                    class="promotions-used-swiper-button-prev size-9 flex cursor-pointer items-center justify-center rounded-full bg-neutral-50">
                    <i class="icon-chevron-left text-[20px] [&.unactive]:text-neutral-200"></i>
                </div>
                <div
                    class="promotions-used-swiper-button-next size-9 flex cursor-pointer items-center justify-center rounded-full bg-neutral-50">
                    <i class="icon-chevron-left rotate-180 text-[20px] [&.unactive]:text-neutral-200"></i>
                </div>
            </div>
        @endif
    </div>
    @if (empty($promotions) || !$hasUsedPromotion)
        <div class="p-3 xl:p-0">
            <x-accounts::promotion.promotion-used-empty />
        </div>
    @else
        <div class="swiper promotions-used-swiper">
            <div class="swiper-wrapper">
                @foreach ($promotions as $promotion)
                    <div class="swiper-slide">
                        <x-accounts::promotion.promotion-used-item
                            :data="$promotion"
                            :commission="$commission"
                            :chartData="$commission"
                            :slotsInfo="$slotsInfo"
                            :casinoInfo="$casinoInfo"
                            lobby="promotion"
                        />
                    </div>
                @endforeach
            </div>
        </div>
        <div class="swiper-pagination promotions-used-swiper-pagination xl:hidden pb-[20px]"
            style="--swiper-pagination-bullet-inactive-color: #F1F3F9;--swiper-pagination-bullet-inactive-opacity: 1;--swiper-pagination-color:#CDA964">
        </div>
    @endif
</div>
@pushOnce('scripts')
    <script>
        const cancelPromotionModal = `
    <x-ui.modal
        :header="__('auth.verifyEmailText.title')"
        :id="'cancel-promotion-modal'"
        modalClass=" items-center"
        modalContainerClass="max-xl:rounded-[24px] mx-3 xl:mx-0"
    >
        <x-ui.modals.cancel-promotion-modal type="info" />
    </x-ui.modal>`
        document.addEventListener('DOMContentLoaded', () => {
            const swiper = new Swiper('.promotions-used-swiper', {
                spaceBetween: 20,
                breakpoints: {
                    1200: {
                        slidesPerView: 'auto',
                    },
                    360: {
                        slidesPerView: 1,
                        spaceBetween: 8,
                    }
                },
                navigation: {
                    nextEl: '.promotions-used-swiper-button-next',
                    prevEl: '.promotions-used-swiper-button-prev'
                },
                pagination: {
                    el: '.promotions-used-swiper-pagination',
                    clickable: true,
                },
                on: {
                    slideChange: function() {
                        const prevBtn = document.querySelector('.promotions-used-swiper-button-prev i');
                        const nextBtn = document.querySelector('.promotions-used-swiper-button-next i');

                        if (this.isBeginning) {
                            prevBtn?.classList?.add('unactive');
                        } else {
                            prevBtn?.classList?.remove('unactive');
                        }

                        if (this.isEnd) {
                            nextBtn?.classList?.add('unactive');
                        } else {
                            nextBtn?.classList?.remove('unactive');
                        }
                    }
                }
            });

            swiper.emit('slideChange');
        });
    </script>
@endPushOnce
