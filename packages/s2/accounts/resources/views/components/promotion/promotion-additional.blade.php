@props(['class' => '', 'data' => [], 'listBonus' => [], 'finalPercent' => 0])
<div @class(['mt-1 flex flex-col xl:mt-2', $class])>
    <div class="mb-0.5 flex items-center justify-between gap-2">
        <span class="text-[10px] font-medium capitalize leading-[16px] text-neutral xl:text-xs">Thưởng bổ
            sung</span>
    </div>
    <div class="relative">
        <progress class="progress sm absolute w-full align-middle" value="{{ $finalPercent }}" max="100"></progress>
        <span class="absolute left-0 top-0 size-1.5 bg-[#6CFE00] rounded-full {{ $finalPercent > 0 ? 'hidden' : 'block'}}"></span>
    </div>
    <div class="flex items-center justify-between mt-2">
        <div class="relative flex w-[30px] flex-col items-start xl:w-[68px]">
            <p class="text-xs font-semibold text-white">%</p>
            <p class="hidden text-[10px] font-medium leading-[16px] text-[#AFBBB7] xl:block">{{ config('app.currency') }}</p>
        </div>
        @foreach ($listBonus as $index => $item)
            <div
                class="{{ $index === count($listBonus) - 1 ? 'items-end' : 'items-center' }} relative flex w-[30px] flex-col xl:w-[68px]">
                <p class="text-xs font-semibold text-white">{{ $item->percent }}%</p>
                <p
                    class="{{ $index === count($listBonus) - 1 ? 'self-end' : 'self-center' }} hidden w-max text-[10px] font-medium leading-[16px] text-[#AFBBB7] xl:block">
                    ({{ addSymbolToNumber($item->min_stake_txt) }})
                </p>
            </div>
        @endforeach
    </div>
</div>