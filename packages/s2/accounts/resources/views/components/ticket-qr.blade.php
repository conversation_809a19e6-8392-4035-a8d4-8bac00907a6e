@props([
    'qrSrc' => '',
    'qrCenterLogo' => '',
    'class' => '',
    'wrapperClass' => '',
    'centerLogoClass' => '',
    'qrImageClass' => '',
])

<div @class([
    'overflow-hidden relative rounded-2xl account-ticket-bg w-full',
    $class,
])>
    <div @class([
        'flex items-start max-xl:flex-col xl:h-[208px]',
        $wrapperClass,
    ])>
        <!-- Left Section - QR Code -->
        <div class="relative flex w-full items-center justify-center xl:h-full xl:w-[208px] xl:pr-2">
            <div
                class="flex w-full shrink-0 flex-col items-center justify-center px-5 max-xl:mx-auto max-xl:pb-[26px] max-xl:pt-[26px] xl:px-8">
                @if (isset($topQR) && $topQR)
                    {{ $topQR }}
                @endif

                <div class="relative max-xl:w-[130px]">
                    <div class="overflow-hidden rounded-[10px] bg-account-surface-primary">
                        @if (!empty($qrCenterLogo))
                            <img src="{{ $qrCenterLogo }}" @class([
                                'absolute left-1/2 top-1/2 size-[28px] -translate-x-1/2 -translate-y-1/2 rounded-full bg-white p-[3px] xl:size-[36px]',
                                $centerLogoClass,
                            ]) alt="qr logo">
                        @endif
                        <img src="{{ $qrSrc }}" alt="QR Code" @class([
                            'js-qr-image xl:size-[136px] bg-neutral size-[130px]',
                            $qrImageClass,
                        ])>
                    </div>
                    <i
                        class="icon-download text-5 js-download-qr absolute left-full top-[15px] grid size-6 cursor-pointer place-content-center rounded-r-lg bg-[#000000A6] text-[16px] leading-4 text-neutral"></i>
                </div>
                @if (isset($bottomQR) && $bottomQR)
                    {{ $bottomQR }}
                @endif
                <div
                    class="absolute bottom-0 h-[3px] bg-[url('/public/vendor/accounts/images/codepay/qr-line-mb.png')] bg-contain bg-center bg-repeat before:absolute before:-bottom-[12px] before:-left-[30px] before:z-[1] before:size-[27px] before:rounded-full before:bg-account-surface-primary before:content-[''] after:absolute after:-right-[30px] after:-top-[12px] after:z-[1] after:size-[27px] after:rounded-full after:bg-account-surface-primary after:content-[''] max-xl:left-[17px] max-xl:right-[17px] max-xl:w-[calc(100%-34px)] max-xl:before:border max-xl:before:border-[#FFFFFF1A] max-xl:after:border max-xl:after:border-[#FFFFFF1A] xl:bottom-5 xl:right-0 xl:h-[168px] xl:w-[3px] xl:bg-[url('/public/vendor/accounts/images/codepay/qr-line.png')] xl:before:-bottom-[41px] xl:before:-left-[17px] xl:before:size-[37px] xl:after:-left-[17px] xl:after:-top-[41px] xl:after:size-[37px]">
                </div>
            </div>
        </div>

        <!-- Right Section - Payment Details -->
        <div class="h-full w-full flex-1 px-5 pb-[19px] max-xl:pt-3 xl:px-[16px] xl:py-0 xl:pl-[22px]">
            <div class="h-full w-full">
                @if (isset($content) && $content)
                    {{ $content }}
                @else
                    {{ $slot }}
                @endif
            </div>
        </div>
    </div>
</div>
@pushOnce('scripts')
    @vite(['resources/js/deposit/downloadQr.js'])
@endPushOnce
