@props(['depositData', 'userVerificationStatus'])

@php
    $freeSpins = config('account.freeSpins');
@endphp

@if($userVerificationStatus->isVerified)
<div
    class="hidden xl:flex w-full -mt-1 cursor-pointer border border-primary shrink-0 items-center gap-4 rounded-2xl bg-[linear-gradient(278.43deg,_#008E5C_6.73%,_#006212_93.27%)] py-[10px] px-[10px] xl:py-[14px] xl:px-6"
    data-id="{{ $freeSpins->partner_game_id ?? '' }}"
    data-name="{{ $freeSpins->name ?? '' }}"
    data-image="{{ $freeSpins->image ?? '' }}"
    data-type="{{ $freeSpins->type ?? '' }}"
    data-api-url="{{ $freeSpins->api_url ?? '' }}"
    data-partner-provider="{{ $freeSpins->partner ?? '' }}"
    onclick="window.handleGetGameUrl(this, event)"
>
    <div>
        <img class="size-[60px] rounded-[10px]" src="{{ asset('vendor/accounts/images/overview/icon-overview-verify-10round.png') }}"
            alt="">
    </div>
    <div>
        <p class="mb-0.5 text-xs font-semibold uppercase text-primary">THƯỞNG HOÀN THÀNH
        </p>
        <p class="text-neutral text-base font-bold capitalize">10 vòng quay miễn phí</p>
        <p class="text-neutral text-xs">Bạn đã hoàn tất thiết lập tài khoản - nhận thưởng ngay.</p>
    </div>
    <i class="icon-chevron-right text-neutral text-[32px] ms-auto"></i>
</div>
@else
<div class="rounded-2xl bg-neutral-700 xl:py-5 xl:px-6 max-xl:hidden xl:-mt-1 p-3 overflow-hidden">
    <div class="flex items-start gap-8">
        <div class="grow">
            <div class="flex items-center gap-2 mb-4">

                <img alt="" class="size-8"
                    src="{{ asset('vendor/accounts/images/icons/icon-profile.svg') }}">

                <p class="text-neutral text-base capitalize">
                    Thiếp lập tài khoản để nhận thưởng chào mừng
                </p>
            </div>
            <div
                class="flex items-center justify-between gap-2 px-1.5 text-[14px] font-medium leading-[20px] text-account-text-primary">
                <p class="flex items-center gap-1.5">
                    @if ($userVerificationStatus->deposit ?? false)
                        <img class="size-5 shrink-0" src="{{ asset('vendor/accounts/images/icon-user-checked.svg') }}"
                            alt="user-verify">
                    @endif
                    Nạp lần đầu
                </p>
                <div class="flex-1 h-[1px] bg-neutral-600 mx-3"></div>
                <p class="flex items-center gap-1.5">
                    @if ($userVerificationStatus->bank ?? false)
                        <img class="size-5 shrink-0" src="{{ asset('vendor/accounts/images/icon-user-checked.svg') }}"
                            alt="user-verify">
                    @endif
                    Liên kết ngân hàng
                </p>
            </div>
        </div>
        <x-accounts::overview.free-spins />
    </div>
</div>
@endif
