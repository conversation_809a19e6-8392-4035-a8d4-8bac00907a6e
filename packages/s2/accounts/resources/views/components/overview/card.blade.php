@props(['title', 'link' => null, 'icon' => null, 'titleClass' => '', 'classCard' => '', 'appendText' => null])

<div class="{{ $classCard }} overflow-hidden rounded-3xl bg-account-surface-primary p-3 xl:p-4 2xl:p-6">
    <div class="{{ $titleClass }} mb-3 flex items-center justify-between">
        <div class="flex items-center gap-2">
            <img src="{{ $icon }}" alt="" class="size-6">
            <p class="text-[14px] font-medium capitalize leading-[18px] text-white">{{ $title }}
                @if ($appendText)
                    <span class="text-[#6CFE00]">
                        {{ $appendText }}
                    </span>
                @endif
            </p>
        </div>
        @if ($link)
            <div class="flex items-center gap-2">
                <a href="{{ $link }}"
                    class="flex items-center justify-center gap-1 text-xs text-[#6CFE00] font-medium py-1.5 px-2 rounded-lg bg-[#FFFFFF33] transition-all duration-300 xl:hover:bg-[#FFFFFF22] min-w-[78px]">
                    Xem Thêm
                </a>
            </div>
        @endif
    </div>
    {{ $slot }}
</div>
