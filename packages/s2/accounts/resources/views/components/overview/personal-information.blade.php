@props(['userVerificationStatus', 'depositData' => []])
@php
    $user = auth()->user();
    $menu = config('account.personal_information_menu');
    $userBadge = $userVerificationStatus->isVerified ? 'icon-user-checked' : 'icon-user-warning';
    $badgeDeposit = $userVerificationStatus->deposit ? 'icon-user-checked' : 'icon-user-processing';
    $badgeBank = $userVerificationStatus->bank ? 'icon-user-checked' : 'icon-user-processing';
@endphp
<div class="hidden rounded-3xl bg-account-surface-primary p-6 xl:block">
    <div class="flex justify-between gap-4">
        <div class="flex items-center gap-2 2xl:gap-3">
            <img class="size-[60px] shrink-0 overflow-hidden rounded-full 2xl:size-[80px]"
                src="{{ asset('vendor/accounts/images/profile-avatar.jpg') }}" alt="user-avatar">
            <div class="py-1">
                <p class="mb-1 text-sm font-medium text-account-text-secondary xl:text-xs">Xin chào,</p>
                <div class="flex items-center gap-2">
                    <p
                        class="line-clamp-1 break-all text-base font-medium py-[3px] capitalize leading-[26px] text-account-text-primary xl:text-[14px] xl:leading-[18px]">
                        {{ $user?->fullname ?? '' }}
                    </p>
                    {{-- <img class="size-5 shrink-0" src="{{ asset('vendor/accounts/images/' . $userBadge . '.png') }}"
                        alt="user-verify"> --}}
                </div>
            </div>
        </div>
        @if ($user && $user->balance > 0)
            <div
                class="flex min-w-[560px] items-center gap-3 rounded-[20px] bg-[url('/public/vendor/accounts/images/overview/user-info-bg.jpg')] bg-no-repeat bg-center px-5 py-4 2xl:min-w-[844px] 2xl:gap-4">
                <div>
                    <p class="mb-3 text-[14px] font-semibold leading-[18px] text-[#AFBBB7]">Số dư ví</p>
                    <p class="js-user-balance text-[22px] font-bold leading-[35px] text-account-text-primary xl:text-[18px] xl:leading-[22px]">
                        {{ formatToCurrency($user->balance ?? 0) }}</p>
                </div>
                <div class="flex grow justify-end gap-3">
                    <a href="{{ route('deposit.index') }}"
                        class="relative border-gradient border-gradient-custom flex h-[68px] w-[74px] flex-col items-center justify-center rounded-2xl p-3 xl:hover:shadow-[0px_4px_27.8px_0px_#********]">
                        <img src="{{ asset('vendor/accounts/images/icons/icon-deposit.svg') }}" alt="" class="size-6 mb-1">
                        <p class="text-xs font-medium text-[#6CFE00]">Nạp</p>
                    </a>
                    <a href="{{ route('withdraw.index') }}"
                        class="relative border-gradient-custom flex h-[68px] w-[74px] flex-col items-center justify-center rounded-2xl p-3 xl:hover:shadow-[0px_4px_27.8px_0px_#********]">
                        <img src="{{ asset('vendor/accounts/images/icons/icon-withdraw.svg') }}" alt="" class="size-6 mb-1">
                        <p class="text-xs font-medium text-[#6CFE00]">Rút</p>
                    </a>
                </div>
            </div>
        @else
            <div
                class="flex min-w-[560px] items-center gap-3 rounded-[20px] p-4 2xl:min-w-[844px] xl:gap-4 xl:py-4 xl:px-5 bg-account-custom-gradient-primary">
                <p>
                    <img src="{{ asset('vendor/accounts/images/icons/icon-wallet.svg') }}" alt="wallets"
                        class="size-[60px] shrink-0 2xl:size-[72px]">
                </p>
                <div class="flex grow flex-col justify-center gap-1 xl:h-[58px] xl:gap-2">
                    <p class="text-base font-medium leading-[26px] text-account-text-primary xl:text-[16px] xl:leading-[20px] capitalize">Tăng số dư, nâng cơ
                        hội!</p>
                    <p class="text-sm text-account-text-secondary xl:text-[14px] xl:leading-[18px]">Nạp ngay để nhận thưởng !</p>
                </div>
                <x-kit.button :link="route('deposit.index')" class="w-[102px] h-[40px] capitalize">
                    Nạp ngay
                </x-kit.button>
            </div>
        @endif
    </div>
</div>

<div class="block xl:hidden">

    <div class="mb-2.5 flex items-center gap-2">
        <img class="size-[46px] shrink-0 overflow-hidden rounded-full"
            src="{{ asset('vendor/accounts/images/profile-avatar.jpg') }}" alt="user-avatar">
        <div class="flex grow flex-col gap-0.5">
            <div class="flex items-center gap-2">
                <p class="line-clamp-1 break-all text-base font-medium capitalize text-account-text-primary">
                    {{ $user?->fullname ?? '' }}</p>
                {{-- <img class="size-5 shrink-0" src="{{ asset('vendor/accounts/images/' . $userBadge . '.png') }}"
                    alt="user-verify"> --}}
            </div>

            <p class="text-xs font-normal text-account-text-secondary">
                Số dư ví:
                <span
                    class="js-user-balance text-sm font-semibold text-account-text-primary">{{ formatToCurrency($user->balance ?? 0) }}</span>
            </p>
        </div>
    </div>
    <div class="rounded-3xl bg-[linear-gradient(180deg,_#183B2F_0%,_#072C20_100%)]">
        <div class="bg-account-surface-primary p-4 rounded-3xl">
            <div class="grid grid-cols-4 gap-2 capitalize">
                @foreach ($menu as $item)
                    <a href="{{ $item['url'] }}"
                        class="flex flex-col items-center gap-1 rounded-2xl bg-[linear-gradient(131.42deg,#2A6246_1.59%,_#418457_50.02%,_#2A6246_98.41%)] py-3">
                        <img src="{{ asset($item['icon']) }}" alt="icon" class="w-6 h-6" />
                        <p class="text-xs font-medium text-account-text-primary">{{ $item['title'] }}</p>
                    </a>
                @endforeach
            </div>
            @if (isset($depositData['lastDeposit']) && count($depositData['lastDeposit']) > 0)
            <div class="mt-4">
                <div class="text-sm text-white mb-2 capitalize">
                    Nạp nhanh
                </div>
                <x-accounts::deposit.suggest-deposit :depositData="$depositData" class="xl:hidden [&_.suggest-deposit\_\_header]:hidden [&_.suggest-deposit-item]:w-[240px]" />
            </div>
            @endif
        </div>
        {{-- @if ($userVerificationStatus->isShowSection)
        <div class="pb-2 px-1">
            @if ($userVerificationStatus->isVerified)
                @php
                    $freeSpins = config('account.freeSpins');
                @endphp
                <div
                    class="px-3 pt-3 pb-2"
                >
                    <x-accounts::overview.free-spins />
                </div>
            @else
                <x-kit.collapse
                    class="!border-none [background:transparent]"
                    id="verify-account"
                    title="Thiết Lập Tài Khoản"
                    titleClass="!font-normal text-base"
                    subTitle="Thiết lập ngay để nhận thưởng chào mừng."
                    icon="{{ 'vendor/accounts/images/icons/icon-profile.svg' }}"
                    isOpen
                >
                    <div class="flex flex-col gap-2.5">
                        <div class="flex items-center gap-1.5">
                            <img src="{{ asset('vendor/accounts/images/' . $badgeDeposit . '.png') }}" alt="deposit" class="shrink-0 size-4">
                            <p class="text-sm text-account-text-primary">Nạp lần đầu</p>
                        </div>
                        <div class="flex items-center gap-1.5">
                            <img src="{{ asset('vendor/accounts/images/' . $badgeBank . '.png') }}" alt="bank" class="shrink-0 size-4">
                            <p class="text-sm text-account-text-primary">Liên kết ngân hàng</p>
                        </div>
                    </div>
                    <div
                        class="mt-3"
                    >
                        <x-accounts::overview.free-spins />
                    </div>
                </x-kit.collapse>
            @endif
        </div>
        @endif --}}
    </div>
</div>
@pushOnce('scripts')
    @vite('resources/js/common/accordion.js')
@endPushOnce
