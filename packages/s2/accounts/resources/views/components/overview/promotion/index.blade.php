@props([
    'depositData',
    'promotions' => [],
    'commission',
    'slotsInfo' => null,
    'casinoInfo' => null
])
@php
    $user = Auth::user();
    $promotionsList = config('package-promotion.overview_promotions');
    $hasUsedPromotion = $user->package_id ?? null;
    if($hasUsedPromotion === 5){
        $hasUsedPromotion = null;
    }
    $title = $hasUsedPromotion ? 'Khuyến mãi đang Sử dụng' : 'Khuyến mãi';
    $appendText = $hasUsedPromotion ? '('.count($promotions).')' : '';
@endphp
<x-accounts::overview.card :title="$title" :appendText="$appendText" classCard="min-xl:!px-5"
    icon="{{ asset('vendor/accounts/images/overview/icon-overview-promotion.png') }}" link="{{ route('account.promotions') }}">
    @if ($hasUsedPromotion)
        @foreach ($promotions as $promotion)
        <x-accounts::promotion.promotion-used-item
            class="!p-0"
            :data="$promotion"
            :commission="$commission"
            :chartData="$commission"
            :slotsInfo="$slotsInfo"
            :casinoInfo="$casinoInfo"
        />
        @endforeach
    @else
        <x-accounts::overview.promotion.empty :promotions="$promotionsList" />
    @endif
</x-accounts::overview.card>
