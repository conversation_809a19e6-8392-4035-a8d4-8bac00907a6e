@props(['promotions'])
<div class="mb-3 flex flex-col gap-1 rounded-lg bg-[#12312B] px-5 py-3 xl:mb-[19px]">
    <p class="text-[16px] leading-5 font-medium capitalize text-white"">Chưa tham gia</p>
    <p class="text-xs text-white">Nạp ngay và nhận phần thưởng của bạn!</p>
</div>
{{--  hover add class when item > 2 --}}
<div
    class="overview-promotions-mb scrollbar-type2 xl:-mr-3 xl:pr-[7px] overflow-auto xl:h-[379px] space-y-3 xl:space-y-[19px]">
    @foreach ($promotions as $promotion)
        <div
            class="group relative shrink-0 overflow-hidden rounded-xl xl:h-[180px] xl:rounded-2xl">
            <a href="{{ $promotion['url'] }}">
                <picture>
                    <source srcset="{{ $promotion['imgSrcMobile'] }}" media="(max-width: 1199px)">
                    <img src="{{ $promotion['imgSrc'] }}" alt="promotion" class="size-full object-cover">
                </picture>
            </a>
            <div class="absolute left-0 right-0 top-0 h-full flex-col p-3 xl:justify-center xl:px-6 hidden xl:flex">
                <div
                    class="mb-1 hidden text-xs capitalize font-medium text-white xl:mb-1 xl:block xl:text-[18px] xl:font-semibold xl:leading-[22px]">
                    {{ $promotion['title'] }}
                </div>
                <div
                    class="mb-1 block text-xs font-medium text-white xl:mb-5 xl:hidden xl:text-[18px] xl:font-bold xl:leading-[22px]">
                    {{ $promotion['titleMobile'] }}
                </div>
                <p
                    class="mb-1 hidden text-[10px] font-normal leading-[15px] text-white xl:mb-5 xl:block xl:text-xs">
                    {!! $promotion['description'] !!}</p>
                <p
                    class="mb-1 block text-[10px] font-medium leading-[15px] text-neutral-300 xl:mb-2 xl:hidden xl:text-sm ">
                    {!! $promotion['descriptionMobile'] !!}</p>
                <div>
                    <a href="{{ $promotion['url'] }}"
                        class="flex items-center capitalize justify-center gap-1.5 bg-[linear-gradient(180deg,#0A2A1C_0%,#1A4D1D_100%)] w-[133px] h-[28px] border-solid border-[#80D59C] border-[1px] rounded-lg text-xs font-medium text-white xl:hover:bg-[linear-gradient(180deg,#0B5836_0%,#258F2B_100%)]">
                        Áp Dụng ngay
                        <i class="icon-effect-arrow text-[20px]"></i>
                    </a>
                </div>
            </div>
        </div>
    @endforeach
</div>
