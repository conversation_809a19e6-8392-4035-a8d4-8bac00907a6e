@php
    use App\Helpers\DetectDeviceHelper;
    $isMobile = DetectDeviceHelper::isMobile();
    $categories = config('account.overview_categories');
@endphp
<x-accounts::overview.card title="Danh mục phổ biến"
    icon="{{ asset('vendor/accounts/images/overview/icon-overview-game.png') }}">
    <div class="-mr-3 xl:-mr-4 2xl:-mr-6">
        <div class="swiper js-categories-popular swiper-container group w-full pb-[18px] pr-3 xl:h-[362px] xl:pr-6">
            <div class="swiper-wrapper group-[&:not(.swiper-initialized)]:hidden">
                @foreach ($categories as $category)
                    <div class="swiper-slide xl:!h-[calc((100%_-_12px)/2)] xl:w-[148px]">
                        <x-kit.link
                            :link="$category['url'] ?? ''"
                            :onclick="isset($category['apiUrl']) ? 'onClickSport(' . json_encode($category) . ')' : null"
                            class="category-item relative block cursor-pointer overflow-hidden rounded-lg transition-all duration-300 hover:brightness-95 xl:rounded-xl"
                        >
                            <img
                                src="{{ $category['thumbnail'] }}"
                                alt="{{ $category['name'] }}"
                                class="size-full"
                                loading="lazy"
                            >

                            <span class="category-name absolute left-0 right-0 top-0 line-clamp-1 break-all pt-3 text-center text-xs font-semibold capitalize text-account-text-primary xl:h-[32px] xl:text-[16px] xl:leading-5">
                                {{ $category['name'] }}
                            </span>
                        </x-kit.link>
                    </div>
                @endforeach
            </div>
            <div class="relative hidden size-full group-[&:not(.swiper-initialized)]:block">
                <div class="button__loading absolute inset-0 z-10 flex w-full items-center justify-center rounded-lg">
                    <img src="/asset/images/spinner.svg" class="size-6" />
                </div>
            </div>
            <div class="js-categories-popular-swiper-scrollbar swiper-scrollbar"></div>
        </div>
    </div>
</x-accounts::overview.card>
@pushOnce('scripts')
    <script>
        window.addEventListener("load", (event) => {
            new Swiper('.js-categories-popular', {
                slidesPerView: 3.155,
                spaceBetween: 8,
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                scrollbar: {
                    el: '.js-categories-popular-swiper-scrollbar',
                    hide: false,
                },
                breakpoints: {
                    1200: {
                        slidesPerView: 2.6,
                        spaceBetween: 12,
                        grid: {
                            rows: 2,
                            fill: "column"
                        },
                    },
                    1400: {
                        slidesPerView: 3.21,
                        spaceBetween: 12,
                        grid: {
                            rows: 2,
                            fill: "column"
                        },
                    },
                    1508: {
                        slidesPerView: 3.51,
                        spaceBetween: 12,
                        grid: {
                            rows: 2,
                            fill: "column"
                        },
                    },
                },
            });
        });
    </script>
@endPushOnce
