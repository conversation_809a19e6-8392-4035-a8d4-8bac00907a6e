<x-accounts::overview.card title="Tr<PERSON> chơi đ<PERSON>ợc đề xuất"
    icon="{{ asset('vendor/accounts/images/overview/icon-overview-game.svg') }}">
    <div class="-mr-3 xl:-mr-4 2xl:-mr-6">
        <div
            class="js-recommend-games swiper swiper-container group h-[236px] w-full pr-3 pb-4 xl:pb-[18px] xl:h-[362px] xl:pr-6">
            <div class="swiper-wrapper group-[&:not(.swiper-initialized)]:hidden">
                @foreach ($recommendGames as $game)
                    <div class="swiper-slide !h-[calc((100%_-_4px)/2)] !mr-1 xl:!mr-2">
                        <x-accounts::card-horizontal name="{{ $game->name ?? 'title' }}"
                            partner="{{ $game->partner ?? '' }}"
                            partner_txt="{{ $game->partner_txt ?? '' }}" image="{{ $game->image ?? '' }}"
                            partner_provider="{{ $game->partner_provider ?? '' }}"
                            is_favorite="{{ $game->is_favorite ?? false }}" data-api="{{ $game->api_url ?? '' }}"
                            id="{{ $game->partner_game_id ?? '' }}" lobbyType="game" deny_info="{{ $game->deny_info ?? false }}"
                            tags="{{ $game->tags ?? '' }}"
                            class="loader-image-transparent flex flex-col items-center text-marron"
                            category="{{ $game->display_types[0] ?? '' }}"
                            />
                    </div>
                @endforeach
            </div>
            <div class="relative hidden size-full group-[&:not(.swiper-initialized)]:block">
                <div class="button__loading absolute inset-0 z-10 flex w-full items-center justify-center rounded-lg">
                    <img src="/asset/images/spinner.svg" class="size-6" />
                </div>
            </div>
            <div class="js-recommend-games-swiper-scrollbar swiper-scrollbar max-xl:!h-[4px]"></div>
        </div>
    </div>
</x-accounts::overview.card>