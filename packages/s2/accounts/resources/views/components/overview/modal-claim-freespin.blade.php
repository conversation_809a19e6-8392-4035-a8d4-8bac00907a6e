@props([
    'dataFreeSpins' => null
])
@php
    use Carbon\Carbon;
    $freeSpins = config('account.freeSpins');
    $freeSpinsStart = isset($dataFreeSpins->created_date) ? Carbon::parse($dataFreeSpins->created_date)->format('H:i d-m-Y') : '';
    $freeSpinsExpired = isset($dataFreeSpins->expired_date) ? Carbon::parse($dataFreeSpins->expired_date)->format('H:i d-m-Y') : '';
@endphp

<x-ui.modal
    id="free-spins-modal"
    modalContainerClass="!max-w-[366px] xl:!max-w-[440px] !px-4 !py-5 xl:!p-8 before:bg-[url('/public/asset/images/auth/popup-bg.jpg')]"
    actionCloseModal="handleCloseFreeSpinsModal()"
>
    <div class="relative">
        <div class="mx-auto size-[96px] mb-4">
            <img src="{{ asset('vendor/accounts/images/overview/freespin-thumb.png') }}" alt="free spin" class="w-full h-full object-cover">
        </div>
        <div class="mb-5 xl:mb-6">
            <h3 class="text-lg leading-[22px] font-medium uppercase text-center text-neutral mb-2">
                Bạn đã nhận được <br />
                10 vòng quay miễn phí
            </h3>
            <p class="text-sm leading-[18px] text-neutral text-center">
                Áp dụng cho trò chơi CUNG HÝ PHÁT TÀI <br />
                với mức cược là 2K/vòng.<br /><br />
            </p>
            @if ($freeSpinsStart && $freeSpinsExpired)
            <p class="text-sm leading-[18px] text-neutral text-center">
                Hạn dùng <span class="font-bold">{{ $freeSpinsStart }}</span>
                đến <span class="font-bold whitespace-nowrap">{{ $freeSpinsExpired }}</span>
            </p>
            @endif
        </div>
        <x-kit.button
            variant="primary"
            class="h-[40px]"
            data-id="{{ $freeSpins->partner_game_id ?? '' }}"
            data-name="{{ $freeSpins->name ?? '' }}"
            data-image="{{ $freeSpins->image ?? '' }}"
            data-type="{{ $freeSpins->type ?? '' }}"
            data-api-url="{{ $freeSpins->api_url ?? '' }}"
            data-partner-provider="{{ $freeSpins->partner ?? '' }}"
            onclick="handleClaimFreeSpins(this, event)"
        >
            Chơi Ngay
        </x-kit.button>
    </div>
</x-ui.modal>
@pushOnce('scripts')
    <script>
        async function handleClaimFreeSpins(target, event) {
            target.disabled = true;
            await submitData('/api-promotion/v1/verification/close', {})
            window.handleGetGameUrl(target, event)
            target.disabled = false;
            setTimeout(() => {
                closeModal();
            }, 1000);
        }
        async function handleCloseFreeSpinsModal() {
            await submitData('/api-promotion/v1/verification/close', {})
            closeModal();
        }
    </script>
@endPushOnce
