@props(['list', 'emptyRoute', 'statusTransaction'])
@php
    use S2\Accounts\Enums\TransactionStatus;
@endphp
<div class="divTable type-transaction text-xs font-medium max-xl:hidden">
    <div class="divTable__header text-account-text-secondary capitalize">
        <div class="divTable__tr h-6 items-center px-5">
            <div class="divTable__td w-[80px]">Ngày</div>
            <div class="divTable__td w-[119px]">Phương thức</div>
            <div class="divTable__td w-[96px]">Số tiền</div>
            <div class="divTable__td grow text-right">Trạng thái</div>
        </div>
    </div>
    <hr class="mt-[7px] pb-2 border-[#23413A]">
    <div class="divTable__body flex h-[376px] flex-col gap-[2px] text-white">
        @if (empty($list))
            <div class="flex h-full flex-col items-center justify-center gap-2 rounded-2xl">
                <i class="icon-nodata text-[70px] text-[#465F59]"></i>
                <div class="text-center text-xs text-[#AFBBB7]">Không có dữ liệu</div>
            </div>
        @else
            @foreach ($list as $item)
                @php
                    $isShowInfoCard = isset($item->method) && $item->method === 'phone_card' && $item->status === 'FINISHED' && $item->action === 'WITHDRAW';
                @endphp
                <div class="divTable__tr min-h-[52px] items-center px-4 2xl:px-5 py-[14px] bg-[#12312B] rounded-lg">
                    <div class="divTable__td w-[80px]">{{ format_date_vn($item->created_time, 'd/m/Y') }}
                    </div>
                    <div class="divTable__td w-[119px] break-all pr-1 2xl:pr-2">
                        {{ $item?->method_txt ?? '' }}</div>
                    <div class="divTable__td w-[96px] break-all pr-1 2xl:pr-2">
                        {{ addSymbolToNumber($item?->amount_txt ?? 0) }}</div>
                    <div
                        class="divTable__td js-info-card relative history--{{ strtolower($item->status) }} flex grow items-center justify-end gap-1">
                        <span class="size-1 rounded-full"></span>
                        <p @class([
                            'history-status',
                            TransactionStatus::getStatusClass($item->status ?? ''),
                            'underline cursor-pointer js-info-card-btn' => $isShowInfoCard,
                        ])>
                            {{ TransactionStatus::getStatusText($item->status ?? '') ?? '' }}
                        </p>
                        @if ($isShowInfoCard)
                            <div
                                class="js-info-card-content left-unset absolute -right-8 2xl:-right-[44px] bottom-[25px] z-[103] hidden w-[234px] whitespace-nowrap rounded-lg px-3 py-2.5 text-xs font-medium text-black-650 shadow-[0px_1px_20px_0px_#********]">
                                <x-accounts::history.phone-card-info
                                    :item="$item"></x-accounts::history.phone-card-info>
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        @endif
    </div>
</div>
@pushOnce('scripts')
    @vite(['resources/js/history/phone-card-info.js'])
@endPushOnce
