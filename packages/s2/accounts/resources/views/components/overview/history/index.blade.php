@php
    $statusTransaction = config('account.history.statusTransaction');
@endphp
<x-accounts::overview.card titleClass="!mb-2" title="Giao dịch gần đây" icon="{{ asset('vendor/accounts/images/overview/icon-overview-history.png') }}"
    link="/account/history?tab=transaction" classCard="hidden xl:block">
    @php
        $activeTab = request()->get('tab') ?? 'deposit';
        $tabs = [
            'deposit' => 'Nạp',
            'withdraw' => 'Rút',
        ];
    @endphp

    <x-kit.tabs :tabs="$tabs" :activeTab="$activeTab">
        <x-slot:customNav>
            <nav class="inline-flex overflow-hidden rounded-[16px] bg-[#23413A]">
                @foreach ($tabs as $key => $label)
                    @php
                        $isActive = $activeTab ? $activeTab === $key : $key === array_key_first($tabs);
                    @endphp
                    <button type="button" data-tab="{{ $key }}"
                        class="tab-btn {{ $isActive ? 'active' : '' }} capitalize min-w-[96px] px-4 py-3 text-[14px] font-medium leading-[18px] text-[#AFBBB7] transition-all [&.active]:bg-[#6CFE00] [&.active]:text-[#00120C]">
                        {{ $label }}
                    </button>
                @endforeach
            </nav>
        </x-slot:customNav>

        <x-slot name="deposit">
            <x-accounts::overview.history.transaction-table
                :list="$listDeposit"
                empty-route="deposit.index"
                :statusTransaction="$statusTransaction"
            />
        </x-slot>

        <x-slot name="withdraw">
            <x-accounts::overview.history.transaction-table
                :list="$listWithdraw"
                empty-route="deposit.index"
                :statusTransaction="$statusTransaction"
            />
        </x-slot>
    </x-kit.tabs>
</x-accounts::overview.card>
