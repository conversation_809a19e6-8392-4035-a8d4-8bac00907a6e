@props(['filters'])

<div class='flex gap-8 h-full'>
    <div class="flex items-center gap-4">
        <div class="flex cursor-pointer items-center">
            {{-- Dropdown Desktop --}}
            <div class="relative z-10">
                <button type="button"
                    class="dropdownButton hidden items-center justify-between gap-2 rounded-full bg-grey-200 px-3 py-2.5 text-sm font-medium capitalize text-black ring-0 lg:inline-flex">
                </button>
                <div
                    class="dropdownMenu hidden absolute right-0 mt-2 origin-top-right rounded-md shadow-lg w-48 bg-account-surface-primary ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div class="py-1">
                        @foreach ($filters as $filter)
                            <div class="dropdown-item block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                data-value="{{ $filter['value'] }}">
                                {{ $filter['label'] }}</div>
                        @endforeach
                    </div>
                </div>

                {{-- Dropdown Mobile --}}
                <button type="button"
                    class="dropdownButton flex flex-col items-end justify-center gap-1 rounded-lg py-1 text-[9px] font-normal capitalize text-black ring-0 xs:inline-flex xs:flex-row xs:items-center xs:text-sm lg:hidden">
                </button>
                <div
                    class="dropdownMenu hidden absolute right-0 mt-2 origin-top-right rounded-md shadow-lg w-48 bg-account-surface-primary ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <div class="py-1">
                        @foreach ($filters as $filter)
                            <div class="dropdown-item block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                data-value="{{ $filter['value'] }}">
                                {{ $filter['label'] }}</div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
        {{-- Refresh --}}
        <button class="account-refresh hidden h-10 w-10 items-center justify-center rounded-full bg-grey-200 lg:flex">
            <img src="{{ asset('vendor/accounts/images/refresh.svg') }}" class="w-3 h-3" />
        </button>
    </div>
</div>
@pushOnce('scripts')
    <script>
        const filters = (@json($filters))
        let activeFilter = filters[0]
        const svgArrow = `<svg class="w-5 h-5 ml-2 -mr-1" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
                stroke="currentColor" aria-hidden="true">
                <path fill-rule="evenodd"
                    d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                    clip-rule="evenodd" />
            </svg>`

        window.addEventListener("load", () => {
            const renderOptions = (label = activeFilter.label) => {
                $(".dropdownButton").html(label + svgArrow)
            }
            const handleClickOptions = (value) => {
                return filters.find((item) => item.value === value)
            }

            renderOptions();

            $(".dropdownButton").click(function(event) {
                const index = $(".dropdownButton").index(this)
                $(".dropdownMenu").eq(index).toggleClass("hidden");
            });

            $(".dropdownMenu .dropdown-item").click((event) => {
                const value = event.target.getAttribute('data-value')
                activeFilter = handleClickOptions(value)
                renderOptions(activeFilter.label)
                $(this).trigger('accountFilter', activeFilter)
            })

            // Hide the dropdown menu whenever click outside the dropdown menu
            $(document).click(function(event) {
                if (!$(event.target).closest('.dropdownButton').length) {
                    $(".dropdownMenu").addClass("hidden");
                }
            });

            $(".account-refresh").click(() => {
                const index = $(".account-refresh").index(this)
            })
        });
    </script>
@endPushOnce
