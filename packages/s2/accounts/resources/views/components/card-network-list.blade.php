@props([
    'label' => 'Chọn nhà mạng',
    'networks' => [],
    'defaultNetwork' => '',
    'isShowRate' => false,
    'inputName' => 'to_telcom_code',
    'isShowMaintenance' => false,
])

<div class="flex flex-col gap-1">
    <!-- Network Selection -->
    <div class="text-xs font-medium text-neutral-200">{{ $label }}</div>
    <div class="js-card-list grid grid-cols-2 gap-2 xl:grid-cols-3">
        <input class="js-card-network-input absolute hidden" name="{{ $inputName }}" value="{{ $defaultNetwork }}">
        @foreach ((array) ($networks ?? []) as $key => $phonecard)
            @php
                $isDisabled = ($phonecard->status ?? null) === 0 && $isShowMaintenance;
            @endphp
            <div @class([
                'js-card-network-item account-active-button group h-[44px] relative flex items-center cursor-pointer gap-2 py-1 px-3 rounded-[6px] [&:has(.badge-maintenance)]:pointer-events-none',
                '!border-primary-500 !bg-neutral active' => $key == $defaultNetwork,
                'is-disabled' => $isDisabled,
            ]) data-value="{{ $key }}" data-card="{{ json_encode($phonecard) }}">
                @if ($key == $defaultNetwork)
                    <i
                        class="icon-radio absolute right-[4px] top-1 text-[10px] leading-[1] text-[#CDA964] xl:right-1 xl:top-1 xl:text-[16px] xl:leading-4"></i>
                @endif
                <div class="text-secondary-500 flex h-[24px] w-[24px] items-center justify-center rounded-full bg-neutral-150 font-bold">
                    <img src="{{ asset('/vendor/accounts/images/icons/' . strtolower($key) . ($isDisabled ? '-disabled' : '') . '.png') }}"
                        alt="{{ $key }}" class="size-6">
                </div>
                <div class="flex flex-col">
                    <div
                        class="text-[14px] font-medium capitalize leading-[20px] text-neutral group-[.is-active]:text-account-text-primary group-[.is-disabled]:text-account-text-secondary">
                        {{ mb_strtolower($key) === 'zing' ? 'Zingpay' : mb_strtolower($key) }}
                    </div>
                    @if ($isShowRate)
                        <div class="text-[10px] font-normal leading-4 text-primary group-[.is-disabled]:text-account-text-secondary">Phí
                            nạp: {{ (1 - $phonecard->rate) * 100 }}%
                        </div>
                    @endif
                </div>

                @if ($isDisabled)
                    <div
                        class="badge-maintenance absolute left-1/2 top-0 -translate-x-1/2 -translate-y-1/2 rounded-full bg-neutral-300 px-[8px]">
                        <p class="text-[8px] font-medium uppercase leading-[12px] text-neutral">Bảo trì</p>
                    </div>
                @endif
            </div>
        @endforeach
    </div>
</div>
