@props([
    'cryptoCurrencyList' => [],
    'label' => '',
    'listClass' => '',
    'itemClass' => '',
])

<div>
    <div class="mb-2 text-xs font-medium text-account-text-secondary">{{ $label }}</div>
    <div @class([
        'crypto-list js-packages no-scrollbar grid grid-cols-2 gap-2 max-xl:-mx-4 max-xl:overflow-auto max-xl:px-4 xl:gap-2',
        $listClass,
    ])>
        @foreach ($cryptoCurrencyList as $key => $package)
            @php
                $isActive = $key == 0;
            @endphp
            <label @class([
                'js-crypto-currency-option account-active-button crypto__item relative flex  cursor-pointer items-center rounded-[6px] px-[7px] py-[10px] transition-all max-xl:min-w-[136px] max-xl:shrink-0 max-xl:py-[9px] min-h-[60px] xl:px-2 xl:hover:bg-account-surface-primary',
                'active' => $isActive,
                $itemClass,
            ])>
                <input type="radio" name="method" data-currency="{{ $package->currency }}" data-network="{{ $package->network[0] }}"
                    value="{{ $package->currency . $package->network[0] }}" class="package-radio peer absolute opacity-0"
                    data-percent="{{ $package->promotion ?? 0 }}" data-total="{{ number_format($package->max_amount ?? 0) }}"
                    data-multiplier="{{ $package->multiplier ?? 0 }}" {{ $isActive ? 'checked' : '' }}>
                <i
                    class="icon-radio absolute right-[3px] top-[3px] text-[12px] leading-[1] text-primary-200 xl:text-[12px] xl:leading-[12px]"></i>
                <div class="flex items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/crypto-' . strtolower($package->currency) . '.png') }}" alt="crypto"
                        class="size-6">
                    <div>
                        <div class="currency-name mb-[2px] text-sm font-medium uppercase text-neutral">
                            {{ $package->currency . ' (' . $package->network[0] . ')' ?? '' }}</div>
                        <div class="text-xs text-primary">
                            {{ '≈ ' . formatAmount($package->price) . ' VND' ?? '' }}
                        </div>
                    </div>
                </div>
            </label>
        @endforeach
    </div>
</div>
