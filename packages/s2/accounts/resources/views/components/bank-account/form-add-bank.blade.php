<div class="form-bank">
    @php
        $firstBank = array_values($listBanks)[0] ?? null;
        $bankCodeOriginal = $firstBank ? $firstBank->bank_code : '';
        $bankCodeDefault = $firstBank ? $firstBank->bank_code : '';
        $bankNameDefault = $firstBank ? $firstBank->bank_name : '';
        $bankImageDefault = $firstBank
            ? asset('/vendor/accounts/images/banks/' . mb_strtolower($firstBank->bank_code) . '.svg')
            : '';
        $formRules = config('validation.rules');
    @endphp
    <form id="add-bank" data-bank-code="{{ $bankCodeDefault }}" data-bank-name="{{ $bankNameDefault }}"
        data-bank-image="{{ $bankImageDefault }}" data-bank-code-original="{{ $bankCodeOriginal }}">
        <div class="flex flex-col gap-y-4 xl:gap-y-6">
            <div>
                <x-kit.dropdown-bank label="Ngân hàng" isRequire name="bank_code" search type="bank" :options="$listBanks"
                    placeholderIcon="icon-bank" placeholder="Chọn tài khoản ngân hàng của bạn">
                </x-kit.dropdown-bank>
            </div>
            <div class="flex flex-col lg:flex-row items-start justify-between gap-4 xl:gap-5">
                <div class="relative w-full">
                    <x-kit.input
                        label='Số tài khoản'
                        name="bank_account_no"
                        isRequire
                        type="tel"
                        placeholder="Nhập số tài khoản"
                        id="bank_account_no"
                        classNameInput="w-full"
                        maxlength="{{ $formRules['bank_account_no']['max'] }}"
                        minlength="{{ $formRules['bank_account_no']['min'] }}"
                    >
                    </x-kit.input>
                </div>

                <div
                    @class([
                        "absolute pointer-events-none opacity-0 w-0" => $bankAccountName,
                        "relative w-full" => !$bankAccountName,
                    ])
                >
                    <x-kit.input
                        id="bank-account-name"
                        label='Tên tài khoản'
                        data-lettersOnly="true"
                        isRequire
                        name="bank_account_name"
                        placeholder="Nhập tên tài khoản"
                        value="{{ $bankAccountName ?? '' }}"
                        readonly="{{ $bankAccountName ? true : false }}"
                        class="[&_div]:bg-neutral [&_div]:border-neutral-200 uppercase [&::placeholder]:normal-case"
                        minlength="{{ $formRules['bank_account_name']['min'] }}"
                        maxlength="{{ $formRules['bank_account_name']['max'] }}"
                        limitSpecialCharacters
                    >
                    </x-kit.input>
                </div>
            </div>

            <x-kit.button disabled type="submit" id="submit-btn"
                class="button-submit js-submit-withdraw-card add-bank-button mx-auto h-[40px] w-full min-w-[220px] capitalize disabled:cursor-not-allowed lg:w-max">
                {{ __('account.dropdown_bank.btn_add_bank') }}
            </x-kit.button>
        </div>
    </form>
</div>

<style>
    form#add-bank span.form-input-error {
        position: relative;
        top: 0;
    }
    form#add-bank .base-input:has(.form-input-error:not([style*="display: none;"]):not(:empty)) {
        margin-bottom: 0;
    }
</style>

@pushOnce('scripts')
    @vite(['resources/js/account/bank-management.js'])
    <script>
        window.addEventListener('DOMContentLoaded', () => {
            handleDropdown();
            handleInput();
        })
    </script>
@endpushOnce
