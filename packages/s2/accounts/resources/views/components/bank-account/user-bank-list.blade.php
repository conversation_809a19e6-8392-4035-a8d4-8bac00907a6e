<div id="user-bank-list" class="grid grid-cols-2 gap-2.5">

    @foreach ($userBanks as $userBank)
        <div
            class="user-bank-item box-border border border-[#23413A] flex h-[60px] xl:h-[70px] items-center gap-2 rounded-xl bg-[url('/public/vendor/accounts/images/bank-item-bg.jpg')] px-3 py-[7px] lg:gap-2.5 lg:px-4 lg:py-2.5">
            <img src="{{ asset('vendor/accounts/images/banks/' . strtolower($userBank->bank_code ?? '') . '.svg?v=1.0.1') }}"
                alt="{{ $userBank->bank_name }}" class="aspect-square h-[1.75rem] w-auto lg:h-8" />

            <div>
                <p class="text-xs xl:text-[14px] xl:leading-[18px] text-[#AFBBB7]">{{ $userBank->bank_name }}</p>
                <p class="text-xs xl:text-[16px] xl:leading-[20px] font-medium text-white">{{ $userBank->bank_account_no }}</p>
                <p @class([
                    'text-[10px] leading-[16px] font-medium',
                    'text-functional-success' => $userBank->bank_status != 1,
                    'text-functional-warning' => $userBank->bank_status == 1,
                ])>{{ $userBank->bank_txt }}</p>
            </div>
        </div>
    @endforeach
</div>
