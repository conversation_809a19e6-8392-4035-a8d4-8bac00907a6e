@php
    $bankAccountName = '';
    if (!empty($userBanks) && count($userBanks) >= 0) {
        $bankAccountName = $userBanks[0]->bank_account_name;
    }
    $limitUserBank = config('constants.limitUserBank');
@endphp
@section('account-header')
    <x-accounts::header-navigation backUrl="/account" title="Tài khoản ngân hàng" />
@endsection
<div class="flex-1 w-full xl:bg-[#00221B] xl:px-[24px] rounded-3xl">
    <div class="bank-management py-3 xl:pt-6 xl:pb-8 mx-auto xl:max-w-[550px]">
        @if (Auth::check() && !empty($userBanks) && count($userBanks) >= 0)
            <div class="bg-transparent max-xl:bg-[#00221B] rounded-2xl p-3 xl:p-0">
                <div class="flex items-center justify-start gap-x-2 mb-4 xl:mb-6">
                    <img src="{{ asset('vendor/accounts/images/icons/icon-bank.svg') }}" alt="bank icon"
                            class="size-6 object-contain">
                    <p class="font-medium text-[14px] leading-[18px] text-account-text-primary capitalize">{{ __('account.bank_management.title') }}</p>
                </div>
                <div class="flex items-center gap-x-2 mb-2 font-medium text-xs xl:text-[14px] xl:leading-[18px]">
                    <p class="text-account-text-secondary">{{ __('account.bank_management.bank_user_txt') }}</p>
                    <p class="text-account-text-primary">{{ $bankAccountName ?? '' }}</p>
                </div>
                <x-accounts::bank-account.user-bank-list :userBanks="$userBanks" />
            </div>
        @else
            <div id="user-bank-empty" class="bg-transparent max-xl:bg-[#00221B] rounded-2xl p-3 xl:p-0 hidden mb-3 xl:mb-6">
                <div class="flex items-center justify-start gap-x-2 mb-4 xl:mb-6">
                    <img src="{{ asset('vendor/accounts/images/icons/icon-bank.svg') }}" alt="bank icon"
                            class="size-6 object-contain">
                    <p class="font-medium text-[14px] leading-[18px] text-account-text-primary capitalize">{{ __('account.bank_management.title') }}</p>
                </div>
                <div class="flex items-center gap-x-2 mb-2 font-medium text-xs xl:text-[14px] xl:leading-[18px]">
                    <p class="text-account-text-secondary">{{ __('account.bank_management.bank_user_txt') }}</p>
                    <p class="text-account-text-primary"><span id="bank-account-name-empty"></span></p>
                </div>
                <div id="user-bank-list" class="grid grid-cols-2 gap-2.5"></div>
            </div>
        @endif

        <div
            id="add-bank-button-group"
            @class([
                'hidden' => count($userBanks) >= $limitUserBank,
            ])
        >
            @if (!empty($userBanks) && count($userBanks) >= 0)
                <div class="mt-3 xl:mt-6">
                    <x-kit.collapse id="add-bank" title="{{ __('account.bank_management.add_bank') }}"
                        icon="{{ 'vendor/accounts/images/icons/icon-bank.svg' }}">
                        <x-accounts::bank-account.form-add-bank :listBanks="$listBanks" :userBanks="$userBanks" :bankAccountName="$bankAccountName" />
                    </x-kit.collapse>
                </div>
            @else
                <div class="bg-transparent max-xl:bg-[#00221B] rounded-2xl p-3 xl:p-0">
                    <div id="add-bank-button-group-title" class="flex items-center justify-start gap-x-2 mb-4 xl:mb-6">
                        <img src="{{ asset('vendor/accounts/images/icons/icon-bank.svg') }}" alt="bank icon"
                            class="size-6 object-contain">
                        <p class="font-medium text-[14px] leading-[18px] text-account-text-primary capitalize">{{ __('account.bank_management.title') }}</p>
                    </div>
                    <x-accounts::bank-account.form-add-bank :listBanks="$listBanks" :userBanks="$userBanks" :bankAccountName="$bankAccountName" />
                </div>
            @endif
        </div>
    </div>
</div>
