@props([
    'isOpen' => false
])
<x-kit.accordion
    :isOpen="$isOpen"
    title="{{ config('account.deposit_codepay_guide.title') }}"
    icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
    @foreach (config('account.deposit_codepay_guide.sections') as $section)
        <div class="rounded-lg bg-[#F8FAFB] px-4 py-3">
            <p class="mb-2 text-[10px] font-bold uppercase leading-[15px] text-[#835A0E]">
                {{ $section['heading'] }}
            </p>
            <ul class="list-disc space-y-1 pl-4 text-xs text-[#505364]">
                @foreach ($section['items'] as $item)
                    <li>{{ $item }}</li>
                @endforeach
            </ul>
        </div>
    @endforeach
</x-kit.accordion>
