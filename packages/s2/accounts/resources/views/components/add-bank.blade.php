@props([
    'isFromModal' => false,
    'withdrawData' => null,
    'wrapperClass' => ''
])

@php
    $formRules = config('validation.rules');
@endphp
<form
    id="add_banking_submit"
    @class([
        'flex flex-col gap-[32px]',
        $wrapperClass
    ])
>
    <div class="flex flex-col gap-[24px] items-start">
        @if ($isFromModal)
            <h2 class="mx-auto text-[18px] leading-[26px] font-bold text-neutral-800 uppercase">Thêm tài khoản ngân hàng</h2>
        @else
            <h2 class="text-[14px] leading-[20px] font-medium text-neutral-800">Thông tin người nhận</h2>
        @endif
        <x-kit.dropdown-bank
            label="Ngân hàng"
            isRequire
            name="bank_code"
            search
            type="bank"
            :options="$withdrawData['bankList'] ?? []"
            placeholderIcon="icon-bank"
            placeholder="Chọn ngân hàng của bạn">
        </x-kit.dropdown-bank>
        <div class="w-full">
            <x-kit.input
                name="bank_account_no"
                isRequire
                type="number"
                label="Số tài khoản"
                placeholder="Nhập số tài khoản"
                minlength="{{ $formRules['bank_account_no']['min'] }}"
                maxlength="{{ $formRules['bank_account_no']['max'] }}"
            ></x-kit.input>
        </div>
        @if (isset($withdrawData['userBankInfo'] -> bank_account_name))
            <div class="w-full">
                <x-kit.input
                    :value="$withdrawData['userBankInfo'] -> bank_account_name"
                    isRequire
                    name="bank_account_name"
                    label="Chủ tài khoản"
                    placeholder="Nhập tên tài khoản"
                    minlength="{{ $formRules['bank_account_name']['min'] }}"
                    maxlength="{{ $formRules['bank_account_name']['max'] }}"
                    disabled
                >
                </x-kit.input>
            </div>
        @else
            <div class="w-full">
                <x-kit.input
                    isRequire
                    name="bank_account_name"
                    label="Chủ tài khoản"
                    placeholder="Nhập tên tài khoản"
                    minlength="{{ $formRules['bank_account_name']['min'] }}"
                    maxlength="{{ $formRules['bank_account_name']['max'] }}"
                >
                </x-kit.input>
            </div>
        @endif
    </div>
    <div class="flex flex-col gap-[36px]">
        <div class="flex flex-col gap-[32px]">
            <x-kit.button
                disabled
                type="submit"
                class="btn btn-primary bg-primary-500 h-[48px] max-w-[215px] button-submit mx-auto xl:flex items-center justify-center"
            >
                Thêm tài khoản
            </x-kit.button>

            @if ($isFromModal)
                <p class="text-[14px] leading-[20px] text-center">Sau khi thêm tài khoản thành công, xin liên hệ với bộ phận CSKH qua Live Chat hoặc Hotline để xác minh tài khoản nhanh chóng.</p>
            @endif

        </div>
    </div>
</form>
<style>
    form#add_banking_submit span.form-input-error {
        position: relative;
        top: 0;
    }
    form#add_banking_submit .base-input:has(.form-input-error:not([style*="display: none;"]):not(:empty)) {
        margin-bottom: 0;
    }
</style>