@php
    $bankAccountName = '';
    if (!empty($userBanks) && count($userBanks) >= 0) {
        $bankAccountName = $userBanks[0]->bank_account_name;
    }
@endphp
@section('account-header')
    <x-accounts::header-navigation backUrl="/account" title="Tài khoản ngân hàng" />
@endsection
<div class="flex-1 w-full xl:bg-neutral xl:px-[24px] rounded-3xl">
    <div class="bank-management py-3 xl:py-6 mx-auto xl:max-w-[650px]">
        @if (Auth::check() && !empty($userBanks) && count($userBanks) >= 0)
            <div class="bg-neutral rounded-2xl p-3 xl:p-0">
                <div class="flex items-center justify-start gap-x-2 mb-4 xl:mb-6">
                    <div class="w-8 h-8 rounded-lg bg-neutral flex items-center justify-center">
                        <img src="{{ asset('vendor/accounts/images/icons/icon-bank.svg') }}" alt="bank icon"
                            class="w-8 aspect-square object-contain">
                    </div>
                    <p class="font-medium xl:text-sm text-account-text-primary capitalize">{{ __('account.bank_management.title') }}</p>
                </div>
                <div class="flex items-center gap-x-2 mb-2">
                    <p class="text-account-text-secondary text-sm font-normal">{{ __('account.bank_management.bank_user_txt') }}</p>
                    <p class="text-account-text-primary font-medium text-sm">{{ $bankAccountName ?? '' }}</p>
                </div>
                <x-accounts::bank-account.user-bank-list :userBanks="$userBanks" />
            </div>
        @endif

        @if (!empty($userBanks) && count($userBanks) >= 0)
            <div class="mt-3 xl:mt-4">
                <x-kit.collapse id="add-bank" title="{{ __('account.bank_management.add_bank') }}"
                    icon="{{ 'vendor/accounts/images/icons/icon-plus.svg' }}">
                    <div class="my-1">
                        <x-accounts::bank-account.form-add-bank :listBanks="$listBanks" :userBanks="$userBanks" :bankAccountName="$bankAccountName" />
                    </div>
                </x-kit.collapse>
            </div>
        @else
            <div class="bg-neutral rounded-2xl p-3 xl:p-0">
                <div class="flex items-center justify-start gap-x-2 mb-4 xl:mb-6">
                    <div class="w-8 h-8 rounded-lg bg-neutral flex items-center justify-center">
                        <img src="{{ asset('vendor/accounts/images/icons/icon-bank.svg') }}" alt="bank icon"
                            class="w-8 aspect-square object-contain">
                    </div>
                    <p class="font-medium xl:text-sm text-account-text-primary capitalize">{{ __('account.bank_management.title') }}</p>
                </div>
                <x-accounts::bank-account.form-add-bank :listBanks="$listBanks" :userBanks="$userBanks" :bankAccountName="$bankAccountName" />
            </div>
        @endif

        <div class="mt-3 xl:mt-6">
            <x-accounts::bank-account.guide-withdraw :isOpen="empty($userBanks) ? true : false" />
        </div>
    </div>
</div>
