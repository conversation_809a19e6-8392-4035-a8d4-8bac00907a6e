@props(['withdrawData', 'isOpenNote' => 'false'])
@php
    $cryptoCurrencyList = $withdrawData['cryptoCurrencyList'];
    $defaultCurrency = !empty($cryptoCurrencyList) ? $cryptoCurrencyList[0] : [];
    $suggestCurrency = 'kdg';
@endphp
<div>
    <div class="rounded-2xl bg-account-surface-primary p-3 pt-4 xl:rounded-none xl:bg-transparent xl:p-0 ">
        <div class="mb-5 xl:mb-6 ">
            <div class="text-xs text-neutral-200 leading-4">{{ __('account.chose_crypto') }}</div>
            <div class="deposit-codepay-packages js-packages no-scrollbar pt-2 mb-5 grid grid-cols-2 gap-2 max-xl:-mx-4 max-xl:overflow-auto max-xl:px-4 xl:grid-cols-2 xl:gap-2">
                @foreach ($cryptoCurrencyList as $key => $package)
                    <div class="relative z-[99] overflow-visible">
                        @php
                            $isActive = strtolower($defaultCurrency->currency) === strtolower($package->currency);
                        @endphp
                        <label class="js-crypto-currency-option crypto__item relative account-active-button flex min-h-[60px] cursor-pointer items-center rounded-[6px] max-h-[60px] p-3 {{$isActive ? 'active' : ''}}">
                            <input type="radio" {{ $isActive ? 'checked' : '' }} value="{{ $package->network[0] }}" name="packageId" class="package-radio peer absolute opacity-0">
                            <div class="flex items-center gap-2">
                                <img src="{{ asset('vendor/accounts/images/crypto/crypto-' . strtolower($package->currency) . '.png') }}"
                                    alt="crypto" class="size-6">
                                <div>
                                    <div class="mb-[2px] text-sm font-medium uppercase text-neutral leading-[18px]">
                                        {{ $package->currency . ' (' . $package->network[0] . ')' ?? '' }}</div>
                                    <div class="text-xs text-[#6CFE00]">
                                        {{ '≈ ' . $package->price_txt . ' VND' ?? '' }}
                                    </div>
                                </div>
                            </div>
                        </label>
                        @if (strtolower($package->currency) === $suggestCurrency)
                            <img src="{{ asset('vendor/accounts/images/icons/badge-suggest.svg') }}" alt="label-suggest" class="absolute left-1/2 -top-[5px] -translate-x-1/2 w-[42px] h-[12px] object-contain z-[1]">
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
        <form action="{{ route('withdraw.cryptopay') }}" id="withdraw-crypto-form" method="POST" class="mb-0 xl:mb-6"
            data-ex-rate="{{ $defaultCurrency->price ?? 0 }}" data-currency="{{ $defaultCurrency->currency ?? '' }}"
            data-network="{{ $defaultCurrency->network[0] ?? '' }}">
            <div class="js-usdt-value mb-5 xl:mb-6">
                <x-kit.input type="tel" maxlength="9" label="Số tiền rút" placeholder="Nhập số tiền rút"
                    covertUsdtAmount="{{ $defaultCurrency->price ?? 0 }}" id="amount-input" name="amount"
                    wrapperClass="mb-2 js-amount" data-noZero="true" currentBalance="{{ Auth::user()->balance }}"
                    covertCurrencyUnit="{{ $defaultCurrency->currency ?? '' }}"/>
            </div>

            <div class="mb-5 xl:mb-6">
                <x-kit.input type="text" label="Địa chỉ ví" placeholder="Nhập địa chỉ ví" id="address-input"
                    name="address" isPaste inputClass="pr-[40px] xl:pr-[44px]" pasteClass="right-[6px]" />
            </div>

            <div class="mb-5 xl:mb-6">
                <x-kit.input type="tel" label="Số điện thoại" maxlength="5" placeholder="Nhập 5 số cuối điện thoại"
                    id="phone-input" name="phone" />
            </div>

            <div class="flex justify-center">
                <x-kit.button type="submit" id="withdraw-crypto-btn" variant="primary" disabled
                    class="btn btn-primary mx-auto min-h-[40px] w-auto min-w-full xl:min-w-[220px]">Rút tiền</x-kit.button>
            </div>
        </form>
    </div>
    <div class="mt-3 xl:mt-0">
        <x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.withdraw_banking_guide.title') }}"
            icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
            @foreach (config('account.withdraw_cryptopay_guide.sections') as $section)
                <div class="rounded-lg bg-neutral-700 px-4 py-3">
                    <p class="mb-2 text-[10px] font-bold uppercase leading-[15px] text-[#835A0E]">
                        {{ $section['heading'] }}
                    </p>
                    <ul class="list-disc space-y-1 pl-4 text-xs text-neutral-200">
                        @foreach ($section['items'] as $key => $value)
                            <li @class(['hidden' => $key !== 0 && !str_contains($value, $defaultCurrency->currency)]) data-value="{{ $value }}" >{{ $value }}</li>
                        @endforeach
                    </ul>
                </div>
            @endforeach
        </x-kit.accordion>
        <div class="mb-2 xl:mb-4"></div>
        <x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.deposit_crypto_guide.title') }}"
            icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
            <div class="flex items-center gap-5 rounded-lg bg-neutral-700 p-6 xl:gap-10">
                <div class="flex flex-col items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/binance.png') }}" alt="crypto"
                        class="size-[52px]">
                    <div class="text-xs font-medium text-neutral-200">
                        Binance
                    </div>
                </div>
                <div class="flex flex-col items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/coin12.png') }}" alt="crypto"
                        class="size-[52px]">
                    <div class="text-xs font-medium text-neutral-200">
                        Coin12
                    </div>
                </div>
                <div class="flex flex-col items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/houbi.png') }}" alt="crypto" class="size-[52px]">
                    <div class="text-xs font-medium text-neutral-200">
                        Huobi
                    </div>
                </div>
                <div class="flex flex-col items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/remitano.png') }}" alt="crypto"
                        class="size-[52px]">
                    <div class="text-xs font-medium text-neutral-200">
                        Remitano
                    </div>
                </div>
            </div>
        </x-kit.accordion>
    </div>

@pushOnce('scripts')
    <script>
        var cryptoCurrencyList = @json($cryptoCurrencyList);
    </script>
    @vite(['resources/js/withdraw/crypto.js'])
@endpushOnce