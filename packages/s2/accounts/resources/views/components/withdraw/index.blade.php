@props(['withdrawData', 'setDefaultBank', 'recentWithdrawTab'])
@php
    $withdrawTabs = config('account.withdraw_tabs', []);
    $withdrawTabs = array_map(function ($tab) use ($recentWithdrawTab, $withdrawData) {
        if ($tab['id'] === $recentWithdrawTab) {
            $tab['badge'] = 'recent';
        }
        if ($tab['id'] === 'p2p' && empty($withdrawData['p2pLink'])) {
            $tab['is_active'] = false;
        }
        return $tab;
    }, $withdrawTabs);
    $tab = request()->tab ?? 'banking';
    $user = Auth::user();
    $isOpenNote = empty($user->package_id);
@endphp

<div class="xl:max-w-[550px] xl:mx-auto">

    <x-accounts::layouts.content-tab-item
        routeName="withdraw.index"
        currentTab="{{ $tab }}"
        :apiData="$withdrawData"
        :items="$withdrawTabs"
        :lastDeposit="[]"
        :lastDepositSuccess="[]"
    />
    <div>
        @if ($tab === 'p2p')
            @section('contentFullWidth')
                <x-accounts::deposit.p2p :p2pLink="$withdrawData['p2pLink']" :isOpenNote="$isOpenNote" />
            @endsection
        @else
            <div class="mt-3 xl:mt-6" id="withdraw-container">
                @if ($tab === 'card')
                    <x-accounts::withdraw.card :withdrawData="$withdrawData" :isOpenNote="$isOpenNote" />
                @elseif($tab === 'cryptopay' || $tab === 'coin12')
                    <x-accounts::withdraw.cryptopay :withdrawData="$withdrawData" :isOpenNote="$isOpenNote" />
                @elseif($tab === 'banking')
                    <x-accounts::withdraw.banking :withdrawData="$withdrawData" :setDefaultBank="$setDefaultBank" :isOpenNote="$isOpenNote" />
                @else
                    <x-accounts::withdraw.cryptopay :withdrawData="$withdrawData" :isOpenNote="$isOpenNote" />
                @endif
            </div>
        @endif
    </div>
</div>
