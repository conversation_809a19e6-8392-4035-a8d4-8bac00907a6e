@props(['withdrawData', 'setDefaultBank', 'isOpenNote' => 'false'])
@php
    use App\Helpers\DetectDeviceHelper;
    $isMobile = DetectDeviceHelper::isMobile();
    $defaultData;
    $limitUserBank = config('constants.limitUserBank');
    if(isset($withdrawData['userBankInfo']) && $withdrawData['userBankInfo']){
        $defaultData =  $withdrawData['userBankInfo'];
    };
    $yourBanks = isset($withdrawData['userBankList']) && count($withdrawData['userBankList']) > 0 ? $withdrawData['userBankList'] : [];
    $bankList = count($yourBanks) >= $limitUserBank ? [] : (isset($withdrawData['bankList']) && count($withdrawData['bankList']) > 0 ? $withdrawData['bankList'] : []);

    $isFirst = isset($withdrawData['userBankList']) && count($withdrawData['userBankList']) === 0;
    $bankStatus = !$isFirst && $defaultData ? $defaultData -> bank_status : 1;

    $currentBalance = isset(Auth::user()->balance) ? Auth::user()->balance : 0;
    $amountDefault = $currentBalance >= 100 ? 100 : '';
    $formRules = config('validation.rules');
@endphp

<div class="witdraw-banking mb-3 xl:mb-6 mx-auto xl:p-0 py-3 max-xl:px-3 max-xl:bg-[#00221B] rounded-[8px]" step="{{ isset($withdrawData['userBankList']) && count($withdrawData['userBankList']) > 0 ? 2 : 1 }}">
    <form
        id="withdraw_bank_form"
        class="flex flex-col gap-[32px] w-full"
        data-default-amount="{{ $amountDefault }}"
        @if(count($yourBanks) > 0 && $defaultData)
            data-name="{{ $defaultData -> bank_account_name }}" data-no="{{ $defaultData -> bank_account_no }}" data-code={{ $defaultData -> bank_code}} data-image="{{ asset('/vendor/accounts/images/banks/' . mb_strtolower($defaultData -> bank_code) . '.svg') }}"
        @else
            data-name="{{ count($yourBanks) > 0 ? $yourBanks[0] -> bank_account_name : '' }}"
        @endif
    >
        <div class="flex flex-col gap-[24px] max-xl:gap-[20px] w-full">
            @if (count($yourBanks) <= 0)
                <div class="flex items-center gap-2">
                    <div class="w-[24px] h-[24px] rounded-lg flex items-center justify-center">
                        <i class="icon-bank text-[17.56px] text-neutral"></i>
                    </div>
                    <div class="text-[14px] capitalize leading-[18px] font-medium text-neutral">
                        Thiết lập tài khoản ngân hàng
                    </div>
                </div>
            @endif
            <div>
                <x-kit.dropdown-bank
                    :setDefaultBank="count($yourBanks) > 0"
                    search
                    isRequire
                    label="Ngân hàng"
                    name="to_bank_code"
                    type="your-bank"
                    :yourBanks="$yourBanks"
                    :options="$bankList"
                    placeholderIcon="icon-bank"
                    placeholder="{{ $isMobile ? 'Chọn tài khoản ngân hàng' : 'Chọn tài khoản ngân hàng của bạn' }}">
                </x-kit.dropdown-bank>
                <div
                    @if(count($yourBanks) > 0 && $defaultData)
                    data-name="{{ $defaultData -> bank_account_name }}"
                    data-no="{{ $defaultData -> bank_account_no }}"
                    @endif
                    class="withdraw-bank-info mt-1 hidden h-[64px] px-6 py-3 bg-neutral-650 rounded-[8px]"
                >
                    <ul class="flex flex-col gap-2">
                        <li class="flex items-center gap-1 leading-[20px]">
                            <span class="text-[12px] min-w-[120px] leading-[16px] font-normal text-neutral-200">Chủ tài khoản:</span>
                            <span class="text-[12px] leading-[16px] font-medium text-neutral" id="withdraw-bank-name">{{ isset($defaultData) ? $defaultData -> bank_account_name : '' }}</span>
                        </li>
                        <li class="flex items-center gap-1 leading-[20px]">
                            <span class="text-[12px] min-w-[120px] leading-[16px] font-normal text-neutral-200">Số tài khoản:</span>
                            <span class="text-[12px] leading-[16px] font-medium text-[#6CFE00]" id="withdraw-bank-no">{{ isset($defaultData) ? $defaultData -> bank_account_no : '' }}</span>
                        </li>
                    </ul>
                </div>
            </div>
            <div @class([
                'add-bank-info flex gap-x-[20px] max-xl:flex-col max-xl:gap-y-[20px]',
            ])>
                <div class="w-full flex-1">
                    <x-kit.input
                        oninput=""
                        class="withdraw-no"
                        name="to_bank_no"
                        isRequire
                        type="tel"
                        label="Số tài khoản"
                        placeholder="Nhập số tài khoản"
                        minlength="{{ $formRules['to_bank_no']['min'] }}"
                        maxlength="{{ $formRules['to_bank_no']['max'] }}"
                    >
                    </x-kit.input>
                </div>
                <div class="w-full flex-1">
                    <x-kit.input
                        class="withdraw-name uppercase [&::placeholder]:normal-case"
                        isRequire
                        name="to_bank_name"
                        label="Tên tài khoản"
                        minlength="{{ $formRules['to_bank_name']['min'] }}"
                        maxlength="{{ $formRules['to_bank_name']['max'] }}"
                        isLettersOnly
                        limitSpecialCharacters
                        placeholder="Nhập tên tài khoản"
                    >
                    </x-kit.input>
                </div>
            </div>
            <div class="flex flex-col gap-[8px]">
                <x-kit.input
                    maxlength="7"
                    is-format-currency="true"
                    currentBalance="{{ $currentBalance }}"
                    class="withdraw-amount"
                    name="amount_withdraw"
                    rightText='= 0 VND'
                    isRequire
                    type="tel"
                    data-noZero="true"
                    covertAmount="true"
                    label="Nhập số tiền"
                    placeholder="Nhập số tiền rút"
                    wrapperClass="js-amount"
                    value="{{ $amountDefault }}"
                >
                </x-kit.input>
                <div class="grid grid-cols-4 xl:grid-cols-6 gap-[8px]">
                    @foreach ([100, 200, 500, 1000, 2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000] as $amount)
                        <div class="relative">
                            <button type="button"
                                value="{{ $amount }}"
                                @class([
                                    'js-withdraw-bank-amount-item account-withdraw-active-button amount-button z-[3] w-full px-1 relative flex items-center justify-center h-[42px] max-xl:h-[38px] text-sm max-xl:text-[12px] text-neutral rounded-[4px] transition-colors xl:text-[14px] xl:leading-[20px]',
                                    'active' => $amount == $amountDefault,
                                ])
                            >
                                @if ($amount == 5000)
                                    <span class="text-[#00120C] absolute top-0 right-0 rounded-bl-[8px] rounded-tr-[4px] w-[38px] h-[12px] text-[8px] leading-3 font-medium bg-[#FABA53] px-1">
                                        Đề xuất
                                    </span>
                                @endif
                                <span>{{ number_format($amount) }} {{ config('app.currency') }}</span>
                            </button>
                            <div class="absolute top-0 left-0 right-0 bottom-0 bg-[linear-gradient(0deg,_#22463A,_#22463A)] rounded-[4px] z-[1]"></div>
                            <div class="absolute top-0 left-0 right-0 bottom-0 bg-[radial-gradient(100%_78.57%_at_0%_50%,_rgba(131,199,149,0.37)_0%,_rgba(131,199,149,0)_100%)] rounded-[4px] z-[2]"></div>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="withdraw-phone">
                <x-kit.input
                    oninput="formatPhoneWithdraw(this)"
                    inputmode="numeric"
                    maxlength="5"
                    wrapperClass="withdraw-phone {{ $bankStatus === 1 ? '' : 'hidden'}}"
                    class="withdraw-phone"
                    name="phone"
                    isRequire
                    type="text"
                    label="Xác thực số điện thoại"
                    placeholder="Nhập 5 số cuối điện thoại">
                </x-kit.input>
            </div>
            <x-kit.button disabled type="submit" class="btn btn-primary mx-auto min-h-[40px] w-auto min-w-full xl:min-w-[220px] button-submit">Rút tiền</x-kit.button>
        </div>
    </form>
</div>
<x-kit.accordion
    isOpen="{{ $isOpenNote }}"
    title="{{ config('account.withdraw_banking_guide.title') }}"
    icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}"
>
    @foreach (config('account.withdraw_banking_guide.sections') as $section)
        <div class="bg-neutral-700 py-3 px-4 rounded-lg mb-1.5">
            <ul class="text-xs text-neutral-200 list-disc pl-4 space-y-1">
                @foreach ($section['items'] as $item)
                    <li>{!! $item !!}</li>
                @endforeach
            </ul>
        </div>
    @endforeach
</x-kit.accordion>

<style>
    .dropdown-bank-label {
        color: #afbbb7 !important;
    }
    .dropdown-bank-label.selected {
        color: #fcf3b3 !important;
    }
</style>
@pushOnce('scripts')
    @vite(['resources/js/withdraw/bank.js'])

    <script>
        window.addEventListener('DOMContentLoaded', () => {
            handleDropdown();
            handleInput();
        })
    </script>
@endpushOnce
