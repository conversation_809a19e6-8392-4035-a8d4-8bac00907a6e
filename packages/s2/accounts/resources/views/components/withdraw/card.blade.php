@props(['withdrawData', 'isOpenNote' => 'false'])
<form
    id="withdraw_card_form"
    method="POST"
    class="flex flex-col gap-[20px] mb-3 xl:mb-6 py-[16px] px-[12px] max-xl:bg-account-surface-primary max-xl:rounded-[16px] xl:p-0"
>
    @php
        $to_telcom_code = 'VIETTEL';
    @endphp
    <input type="hidden" name="card_status" value="1">
    <input class="absolute opacity-0 pointer-events-none" type="number" name="current_balance" value="{{ Auth::user()->balance ?? 0 }}">
    <x-accounts::card-network-list
        :networks="(array) ($withdrawData['phonecardList']['cardlist'] ?? [])"
        :defaultNetwork="$to_telcom_code"
    />

    <x-accounts::card-amount-list
        :networks="($withdrawData['phonecardList']['cardlist']['VIETTEL']->value_txt ?? [])"
        :defaultNetwork="$to_telcom_code"
        inputName="card_amount_unit"
    />

    <!-- Input Fields -->
    <div class="relative w-full">
        <x-kit.number-input
            class="js-card-number"
            name="card_number"
            type="number"
            isRequire
            label="Số lượng thẻ"
            max="99"
        >
        </x-kit.number-input>
    </div>

    @if(session('error'))
        <div class="text-red-500">{{ session('error') }}</div>
    @endif

    <x-kit.button
        type="submit"
        class="min-w-[220px] h-[40px] button-submit js-submit-withdraw-card w-full mx-auto lg:w-max"
        disabled
    >
        Rút Tiền
    </x-kit.button>

</form>
<x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.withdraw_card_guide.title') }}"
    icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
    @foreach (config('account.withdraw_card_guide.sections') as $section)
        <div class="bg-[#0000002E] py-3 px-4 rounded-lg">
            <ul class="text-xs text-neutral-200 list-disc pl-4 space-y-1">
                @foreach ($section['items'] as $item)
                    <li>{!! $item !!}</li>
                @endforeach
            </ul>
        </div>
    @endforeach
</x-kit.accordion>
@pushOnce('scripts')
    @vite(['resources/js/withdraw/card.js'])
@endpushOnce
