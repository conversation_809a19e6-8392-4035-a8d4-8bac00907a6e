@props(['withdrawTab', 'currentTab'])
@php
    $isActiveTab = $withdrawTab['id'] === $currentTab;
@endphp
<div @class([
    'account-nav-tabs-item shrink-0',
    'active' => $isActiveTab,
    'cursor-no-drop' => !$withdrawTab['is_active'],
])>
    <a href="{{ route('withdraw.index', ['tab' => $withdrawTab['id']]) }}" @class([
        'pointer-events-none' => !$withdrawTab['is_active'],
        'relative flex items-center gap-1 max-xl:shrink-0 text-sm xl:text-base font-medium text-neutral-600 [@media(max-height:400px)]:py-2 py-[14px] xl:pt-2.5 xl:pb-2 capitalize whitespace-nowrap',
        'after:absolute after:left-0 after:bottom-0 xl:after:-bottom-[2px] after:w-full after:h-[2px] after:bg-primary-600 !text-neutral-800 selected' =>
            $isActiveTab,
    ])>
        @if (!$withdrawTab['is_active'])
        <img src="{{ asset('vendor/accounts/images/icons/badge-off.png') }}"
        alt="label-{{$withdrawTab['badge']}}" class="absolute -right-2.5 top-[2px] xl:-top-[7px] h-[12px] object-contain">
        @elseif ($withdrawTab['badge'])
            <img src="{{ asset('vendor/accounts/images/icons/badge-' . $withdrawTab['badge'] . '.png') }}"
            alt="label-{{$withdrawTab['badge']}}" class="absolute -right-2.5 top-[2px] xl:-top-[7px] h-[12px] object-contain">
        @endif
        @if ($withdrawTab['icon'])
        <img
        @class([
            'size-5 object-contain',
            'grayscale' => !$withdrawTab['is_active']
        ])
        src="{{ asset('vendor/accounts/images/icons/icon-' . $withdrawTab['icon'] . '.png') }}"
        alt="icon">
        @endif
        <span @class([
        'text-neutral-600 text-[16px] max-xl:text-[14px] max-xl:leading-[20px] leading-[24px] font-[500] [.selected_&]:text-neutral-800',
        '!text-neutral-300' => !$withdrawTab['is_active'],
        ])>{{ $withdrawTab['title'] }}</span>
    </a>
</div>
