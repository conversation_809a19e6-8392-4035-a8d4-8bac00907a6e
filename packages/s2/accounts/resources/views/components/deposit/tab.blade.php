@props(['depositData', 'depositTab', 'lastDeposit', 'currentTab', 'lastDepositSuccess'])
@php
    $isDisabled = $depositData['isDepositMaintenance'][$depositTab['value']] ?? false;

    $badgeIcon = match (true) {
        $isDisabled => 'badge-off',
        $depositTab['value'] === $lastDeposit => 'badge-recent',
        !empty($depositTab['badge']) => 'badge-' . $depositTab['badge'],
        count($lastDepositSuccess) <= 0 && $depositTab['value'] === 'codepay' => 'badge-suggest',
        default => null,
    };

    $isActiveTab = $depositTab['id'] === $currentTab;
@endphp
<div @class([
    'account-nav-tabs-item shrink-0 bg-no-repeat bg-center',
    'cursor-no-drop' => $isDisabled,
    'active' => $isActiveTab,
])>
    <a href="{{ route('deposit.index', ['tab' => $depositTab['id']]) }}" @class([
        'pointer-events-none' => $isDisabled,
        'relative flex items-center gap-1 max-xl:shrink-0 text-sm xl:text-base font-medium text-account-text-primary py-[14px] px-[16px] xl:pt-2.5 xl:pb-2 capitalize whitespace-nowrap',
        'after:absolute after:left-0 after:bottom-0 after:w-full after:bg-account-text-brand-primary !text-account-text-brand-primary' => $isActiveTab,
    ])>
        {{-- Badge Icons --}}
        @if ($badgeIcon)
            <img src="{{ asset("vendor/accounts/images/icons/{$badgeIcon}.png") }}"
                alt="label-{{ $depositTab['badge'] ?? 'recent' }}"
                class="absolute -right-2.5 top-[2px] h-[12px] object-contain xl:-top-[5px]">
        @endif

        {{-- Tab Icon --}}
        @if ($depositTab['icon'])
            <img src="{{ asset('vendor/accounts/images/icons/icon-tab-' . $depositTab['icon'] . ($isActiveTab ? '-active' : '') . '.svg') }}"
                alt="icon" @class(['size-5 object-contain z-[1]', 'grayscale' => $isDisabled])>
        @endif

        {{-- Tab Title --}}
        <span @class(['z-[1]', 'text-neutral-300' => $isDisabled])>
            {{ $depositTab['title'] }}
        </span>

        <span class="absolute z-[1] bottom-[-1px] left-1/2 translate-x-[-50%] hidden w-[28px] h-[4px] rounded-t-lg bg-account-text-brand-primary [.active_&]:block"></span>
        <span class="absolute bottom-[-1px] left-0 hidden w-full h-[calc(100%+1px)] bg-[linear-gradient(180deg,_rgba(24,_59,_47,_0)_0%,#183B2F_100%)] [.active_&]:block"></span>
    </a>
</div>
