@props(['class' => '', 'depositData' => []])

@php
    if (!function_exists('filterMethod')) {
        function filterMethod($method)
        {
            $originalMethod = $method;
            $method = strtolower($method);
            $filters = config('account.history.suggestMethodDeposit');

            // Kiểm tra xem method có trong bất kỳ filter nào không
            $found = false;
            foreach ($filters as $methods) {
                if (in_array($method, $methods)) {
                    $found = true;
                    break;
                }
            }

            // Nếu không tìm thấy trong filters, trả về method gốc
            if (!$found) {
                return $originalMethod;
            }

            // Nếu có trong filters thì tìm group tương ứng
            foreach ($filters as $newMethod => $oldMethods) {
                if (in_array($method, $oldMethods)) {
                    return $newMethod;
                }
            }

            return $originalMethod;
        }
    }
@endphp

@if (isset($depositData['lastDeposit']) && count($depositData['lastDeposit']) > 0)
    <div
        class="{{ $class }} suggest-deposit flex gap-2 max-xl:flex-col xl:items-center xl:gap-8 xl:rounded-[20px] xl:bg-[#00221B] xl:p-4">
        <div class="suggest-deposit__header flex shrink-0 items-center gap-2">
            <img src="{{ asset('vendor/accounts/images/codepay/icon-codepay-suggest.svg') }}" class="max-xl:size-8 shrink-0"
                width="24" height="24" alt="">
            <div>
                <p class="text-[14px] leading-[18px] font-medium text-white capitalize">Nạp nhanh</p>
                <p class="text-[12px] leading-[16px] text-[#AFBBB7]">Giao dịch gần đây</p>
            </div>
        </div>
        <div
            class="suggest-deposit-list no-scrollbar js-suggest-deposit-list flex grow gap-2 max-xl:-mx-4 max-xl:overflow-auto max-xl:px-4 xl:grid xl:grid-cols-4 xl:gap-2.5">
            @foreach ($depositData['lastDeposit'] as $amount)
                <div data-to-bank-code="{{ $amount['to_bank_code'] }}"
                    data-link= "{{ filterMethod($amount['method']) }}" data-method="{{ $amount['method'] }}"
                    data-amount="{{ $amount['amount'] / 1000 }}"
                    class="suggest-deposit-item js-suggest-deposit-item group relative flex cursor-pointer items-center gap-2 rounded-xl border border-[#23413A] bg-[#12312B] p-2 max-xl:w-[250px] max-xl:shrink-0 max-xl:py-[7px] xl:transition-all xl:hover:bg-[#23413A] xl:hover:shadow-[0px_4px_10.8px_0px_#48484D26]">
                    <img src="{{ asset('vendor/accounts/images/codepay/' . filterMethod($amount['method']) . '.png') }}"
                        class="size-10" alt="">
                    <div class="grow">
                        <p class="whitespace-nowrap text-[16px] leading-5 font-medium text-white">{{ $amount['amountText'] }}
                        </p>
                        <p class="text-xs font-medium capitalize text-[#AFBBB7]">{{ $amount['methodText'] }}</p>
                    </div>
                    <i
                        class="icon-chevron-right mr-1 grid size-6 shrink-0 place-content-center text-[22px] text-[#8C9D98]"></i>
                    <div
                        class="button__loading absolute inset-0 z-10 hidden w-full items-center justify-center rounded-lg bg-grey-500 bg-opacity-50 transition-opacity group-[.is-loading]:flex">
                        <img src="/asset/images/spinner.svg" class="size-6" />
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    @pushOnce('scripts')
        @vite('resources/js/deposit/suggest-deposit.js')
    @endpushOnce
@endif
