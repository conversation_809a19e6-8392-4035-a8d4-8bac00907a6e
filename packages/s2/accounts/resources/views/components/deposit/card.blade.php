@props(['depositData' => [], 'isOpenNote' => 'false'])
@php
    $dataInit = session('data') ?? [];
    $defaultNetwork = request()->get('to_bank_code') ?? ($dataInit['to_telcom_code'] ?? 'VIETTEL');
    if (isset($depositData['networks'][$defaultNetwork]) && $depositData['networks'][$defaultNetwork]->status === 0) {
        foreach ($depositData['networks'] as $key => $network) {
            if ($network->status !== 0) {
                $defaultNetwork = $key;
                break;
            }
        }
    }
    $defaultAmount = (request()->get('amount') ? request()->get('amount') * 1000 : null) ?? ($dataInit['card_amount'] ?? null);

    $viettelRate = isset($depositData['networks']['VIETTEL']) ? (1 - $depositData['networks']['VIETTEL']->rate) * 100 : 0;
    $mobifoneRate = isset($depositData['networks']['MOBIFONE']) ? (1 - $depositData['networks']['MOBIFONE']->rate) * 100 : 0;
    $vinaphoneRate = isset($depositData['networks']['VINAPHONE']) ? (1 - $depositData['networks']['VINAPHONE']->rate) * 100 : 0;
    $vietnamobileRate = isset($depositData['networks']['VIETNAMOBILE']) ? (1 - $depositData['networks']['VIETNAMOBILE']->rate) * 100 : 0;
    $zingRate = isset($depositData['networks']['ZING']) ? (1 - $depositData['networks']['ZING']->rate) * 100 : 0;

@endphp
<div class="flex flex-col gap-3 lg:gap-6">
    <form action="{{ route('deposit.card-create') }}" method="POST" id="deposit_card_form"
        class="flex flex-col gap-5 p-3 max-xl:rounded-2xl max-xl:bg-account-surface-primary xl:gap-6 xl:p-0">
        <input type="hidden" name="card_status" value="1">
        <x-accounts::card-network-list :networks="$depositData['networks']" :defaultNetwork="$defaultNetwork" isShowRate isShowMaintenance />

        <x-accounts::card-amount-list :networks="$depositData['networks'][$defaultNetwork]->value_txt ?? []" :rate="$depositData['networks'][$defaultNetwork]->rate" :defaultAmount="$defaultAmount" :defaultNetwork="$defaultNetwork" isShowReceive />
        <!-- Input Fields -->
        <div class="flex flex-col items-start gap-5 lg:flex-row">
            <div class="relative w-full">
                <x-kit.input label="Số serial" placeholder="Nhập số serial" type="tel" maxlength="20" name="card_serial" isPaste>
                </x-kit.input>
            </div>
            <div class="relative w-full">
                <x-kit.input label="Mã thẻ (PIN)" placeholder="Nhập mã thẻ (PIN)" type="tel" maxlength="20" name="card_code" isPaste>
                </x-kit.input>
            </div>
        </div>

        @if (session('error'))
            <div class="text-red-500">{{ session('error') }}</div>
        @endif

        <div class="text-center">
            <x-kit.button type="submit" variant="primary"
                class="js-submit-deposit-card mx-auto min-h-[40px] w-auto min-w-full xl:min-w-[220px]" disabled>
                Nạp Tiền
            </x-kit.button>
        </div>
    </form>

    <div class="mb-2">
        <x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.deposit_card_guide.title') }}"
            icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
            @foreach (config('account.deposit_card_guide.sections') as $section)
                <div class="rounded-lg bg-[#0000002E] px-4 py-3 mb-1.5">
                    @if ($section['heading'])
                        <p class="mb-2 text-[10px] font-bold uppercase leading-[12px] text-primary">
                            {{ $section['heading'] }}
                        </p>
                    @endif
                    <ul class="list-disc space-y-1 pl-4 text-xs text-neutral-200">
                        @foreach ($section['items'] as $item)
                            <li>{{ $item }}</li>
                        @endforeach
                    </ul>
                </div>
            @endforeach
        </x-kit.accordion>
    </div>
</div>

@pushOnce('scripts')
    @vite(['resources/js/deposit/card.js'])
@endpushOnce
