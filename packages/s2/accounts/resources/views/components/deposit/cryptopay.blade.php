@props(['depositData' => [], 'isOpenNote' => 'false'])
@php
    $cryptoCurrencyList = $depositData['cryptoCurrencyList'];
    $defaultCurrency = !empty($cryptoCurrencyList) ? strtolower($cryptoCurrencyList[0]->currency) : null;
@endphp
<div>
    <div class="flex flex-col gap-5 rounded-2xl bg-account-surface-primary p-3 xl:gap-6 xl:rounded-none xl:bg-transparent xl:p-0">
        <x-accounts::crypto-list :cryptoCurrencyList="$cryptoCurrencyList" :label="__('account.chose_crypto')" />
        <x-accounts::ticket-qr :qrSrc="$depositData['cryptoInfo']->qrcode ?? ''" :qrCenterLogo="asset('vendor/accounts/images/crypto/crypto-' . $defaultCurrency . '.svg')" centerLogoClass="js-crypto-logo"
            wrapperClass="js-crypto-qr-container [&.is-loading]:opacity-50 max-xl:h-[301px]" qrImageClass="js-qr-image">
            <x-slot:content>
                <div class="flex h-full flex-col justify-center">
                    <div class="mb-0 text-xs font-medium capitalize text-neutral xl:mb-1">
                        {{ __('account.deposit_with_qr') }}
                    </div>

                    <div class="mb-2 text-[10px] leading-[15px] text-neutral-200 xl:mb-[20px] xl:text-xs">
                        {{ __('account.scan_qr_code') }}
                    </div>

                    <div class="flex h-auto w-full items-center rounded-md border border-neutral-600 bg-neutral-700 px-[10px] py-[7px]">
                        <p class="line-clamp-1 max-w-[90%] flex-1 text-wrap break-all text-xs text-primary" id="js-wallet-address-value">
                            {{ $depositData['cryptoInfo']->address ?? '' }}
                        </p>
                        <button type="button" id="js-wallet-address-value_copy-btn"
                            class="js-copy-btn ml-auto shrink-0 text-[20px] leading-none text-neutral-300"
                            data-copy="{{ $depositData['cryptoInfo']->address ?? '' }}">
                            <i class="icon-copy"></i>
                        </button>
                    </div>
                </div>
            </x-slot:content>
        </x-accounts::ticket-qr>
    </div>
    <div class="mb-2 mt-3 xl:mt-6">
        <x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.deposit_codepay_guide.title') }}"
            icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
            @foreach (config('account.deposit_cryptopay_guide.sections') as $section)
                <div class="rounded-lg bg-[#0000002E] px-4 py-3 mb-1.5">
                    @if ($section['heading'])
                        <p class="mb-2 text-[10px] font-bold uppercase leading-[12px] text-primary">
                            {{ $section['heading'] }}
                        </p>
                    @endif
                    <ul class="list-disc space-y-1 pl-4 text-xs text-neutral-200">
                        <li>Bước 1: Chọn loại tiền ảo muốn nạp.</li>
                        <li>Bước 2: Chuyển tiền vào địa chỉ ví tương ứng ở bước 1. <span
                                class="js-crypto-currency-value">{{ !empty($cryptoCurrencyList) ? $cryptoCurrencyList[0]->currency : '' }}</span>
                            - <span
                                class="js-crypto-network-value">{{ !empty($cryptoCurrencyList) ? $cryptoCurrencyList[0]->network[0] : '' }}</span>.
                        </li>
                        <li>Bước 3: Hệ thống sẽ tự động cập nhật số dư tài khoản sau 5 phút.</li>
                    </ul>
                </div>
            @endforeach
        </x-kit.accordion>
        {{-- <div class="mb-2 xl:mb-4"></div>
        <x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.deposit_crypto_guide.title') }}"
            icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
            <div class="flex items-center gap-5 rounded-lg bg-[#0000002E] p-6 xl:gap-10">
                <div class="flex flex-col items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/binance.png') }}" alt="crypto" class="size-[52px]">
                    <div class="text-xs font-medium text-account-text-primary">
                        Binance
                    </div>
                </div>
                <div class="flex flex-col items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/coin12.png') }}" alt="crypto" class="size-[52px]">
                    <div class="text-xs font-medium text-account-text-primary">
                        Coin12
                    </div>
                </div>
                <div class="flex flex-col items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/houbi.png') }}" alt="crypto" class="size-[52px]">
                    <div class="text-xs font-medium text-account-text-primary">
                        Huobi
                    </div>
                </div>
                <div class="flex flex-col items-center gap-2">
                    <img src="{{ asset('vendor/accounts/images/crypto/remitano.png') }}" alt="crypto" class="size-[52px]">
                    <div class="text-xs font-medium text-account-text-primary">
                        Remitano
                    </div>
                </div>
            </div>
        </x-kit.accordion> --}}
    </div>
    @vite(['resources/js/deposit/crypto.js'])
