@props([
    'depositData' => [],
    'lastDeposit' => '',
    'lastDepositSuccess' => [],
])
@php
    $userBalance = isset(auth()->user()->balance) ? auth()->user()->balance : 0;
    $isOpenNote = count($lastDepositSuccess) <= 0 && $userBalance == 0 ? 'true' : 'false';
    $depositTabs = config('account.deposit_tabs', []);
    // Lấy tab từ URL nếu có
    $currentTab = request()->segment(3) ?: 'codepay';
    // Kiểm tra nếu tab không tồn tại trong danh sách hoặc đang bảo trì
    $tabExists = false;
    foreach ($depositTabs as $tab) {
        if ($tab['id'] === $currentTab && !($depositData['isDepositMaintenance'][$tab['value']] ?? true)) {
            $tabExists = true;
            break;
        }
    }

    // Nếu tab không hợp lệ, tìm tab đầu tiên không bảo trì
    if (!$tabExists) {
        foreach ($depositTabs as $value) {
            if (!($depositData['isDepositMaintenance'][$value['value']] ?? true)) {
                $currentTab = $value['id'];
                break;
            }
        }
    }
@endphp
<div>

    <x-accounts::layouts.content-tab-item
        routeName="deposit.index"
        currentTab="{{ $currentTab }}"
        :apiData="$depositData"
        :items="$depositTabs"
        :lastDeposit="$lastDeposit"
        :lastDepositSuccess="$lastDepositSuccess"
    />

    <x-accounts::deposit.suggest-deposit class="xl:hidden" />
    @if ($currentTab === 'p2p')
        @section('contentFullWidth')
            <x-accounts::deposit.p2p :p2pLink="$depositData['p2pLink']" :isOpenNote="$isOpenNote" />
        @endsection
    @else
        <div class="rounded-2xl max-xl:overflow-hidden xl:pt-6 account-tabs-content xl:max-w-[550px] mx-auto">
            @if ($currentTab === 'codepay')
                <x-accounts::deposit.codepay :depositData="$depositData" :isOpenNote="$isOpenNote" />
            @elseif($currentTab === 'ewallet')
                <x-accounts::deposit.ewallet :depositData="$depositData" :isOpenNote="$isOpenNote" />
            @elseif($currentTab === 'card')
                <x-accounts::deposit.card :depositData="$depositData" :isOpenNote="$isOpenNote" />
            @elseif($currentTab === 'cryptopay')
                <x-accounts::deposit.cryptopay :depositData="$depositData" :isOpenNote="$isOpenNote" />
            @endif
        </div>
    @endif
</div>
