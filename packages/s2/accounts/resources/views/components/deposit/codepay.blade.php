@props(['depositData' => [], 'isOpenNote' => 'false'])
@php
    use S2\Accounts\Helpers\BankNameHelper;
    $user = Auth::user();
    $currentUserPackageId = isset($user->package_id) ? $user->package_id : null;
    $step = isset($depositData['nicepayData']) && $depositData['nicepayData'] ? 2 : 1;
    $defaultAmount = 100;
    if ($step == 2) {
        $bankImagePath = 'vendor/accounts/images/banks/' . strtolower($depositData['nicepayData']->bank_code) . '.svg';
        $qrLogoBank = file_exists(public_path($bankImagePath))
            ? asset($bankImagePath)
            : asset('vendor/accounts/images/banks/default.svg');
    }

    $isJustCommission =
        isset($depositData['packages']) &&
        count($depositData['packages']) === 1 &&
        $depositData['packages'][0]->id === config('constants.PROMOTION_PLAN_TYPE.COMMISSION') &&
        $currentUserPackageId === config('constants.PROMOTION_PLAN_TYPE.COMMISSION');
@endphp
<form method="POST" action="{{ $step == 1 ? route('deposit.codepay') : route('deposit.codepay.cancel') }}"
    id="deposit-codepay-form"
    class="codepay-form w-full max-xl:mb-3 max-xl:rounded-2xl max-xl:bg-account-surface-primary max-xl:p-3">
    <!-- Progress Steps -->
    <div class="mb-10 flex items-center justify-between gap-1.5 px-4 italic xl:mb-[46px]">
        <!-- Step 1 -->
        <div class="relative flex flex-col items-center gap-1">

            @if ($step == 1)
                <div
                    class="grid size-4 place-items-center rounded-full border-[5px] border-account-text-brand-primary font-medium text-account-text-primary">
                </div>
            @else
                <img src="{{ asset('vendor/accounts/images/icons/icon-check-circle.svg') }}" alt="Check Circle"
                    class="size-4">
            @endif
            <span
                class="absolute top-full whitespace-nowrap pt-1.5 text-xs font-normal leading-[18px] text-account-text-primary">Nạp</span>
        </div>

        <!-- Connector 1-2 -->
        <div
            class="{{ $step >= 2 ? 'border-account-text-brand-primary' : 'border-account-surface-quaternary' }} h-[1px] flex-1 border-b border-solid">
        </div>

        <!-- Step 2 -->
        <div class="relative flex flex-col items-center gap-1">
            <div @class([
                'size-4 border-2 border-account-surface-quaternary rounded-full grid place-items-center text-account-text-secondary step-2-icon-uncheck',
                '!text-account-text-primary border-[5px] !border-account-text-brand-primary' =>
                    $step >= 2,
            ])></div>
            <img src="{{ asset('vendor/accounts/images/icons/icon-check-circle.svg') }}" alt="Check Circle"
                class="step-2-icon-check hidden size-4">
            <span
                class="{{ $step >= 2 ? '!text-account-text-primary' : '' }} absolute top-full whitespace-nowrap pt-1.5 text-xs font-normal leading-[18px] text-account-text-secondary">Thanh
                toán</span>
        </div>

        <!-- Connector 2-3 -->
        <div @class([
            'js-progress-step-three h-[1px] border-b border-solid border-account-surface-quaternary flex-1',
        ])></div>

        <!-- Step 3 -->
        <div class="js-step-codepay-three relative flex flex-col items-center gap-1">
            <div
                class="step-3-icon-uncheck grid size-4 place-items-center rounded-full border-2 border-account-surface-quaternary text-account-text-secondary">
            </div>
            <img src="{{ asset('vendor/accounts/images/icons/icon-check-circle.svg') }}" alt="Check Circle"
                class="step-3-icon-check hidden size-4">
            <span
                class="absolute top-full whitespace-nowrap pt-1.5 text-xs font-normal leading-[18px] text-account-text-secondary">Hoàn
                thành</span>
        </div>
    </div>

    @if ($step == 1)
        <!-- Amount Input Section -->
        <div class="mb-5 xl:mb-6">
            <x-kit.input type="tel" value="{{ $defaultAmount }}" maxlength="9" label="Số tiền nạp"
                placeholder="Nhập số tiền " covertAmount="true" id="amount-input" name="amount"
                wrapperClass="mb-2 js-amount" isFormatCurrency />

            <!-- Quick Amount Buttons -->
            <x-accounts::amount-list :amountList="[100, 200, 500, 1000, 2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000]" :defaultAmount="$defaultAmount" :suggestAmount="5000" />
        </div>

        <!-- Promotions Section -->
        @if (isset($depositData['packages']) && count($depositData['packages']) > 0)
            <div @class(['mb-5 xl:mb-6', 'hidden' => $isJustCommission])>
                <div class="mb-1 flex items-center justify-between">
                    <p class="text-xs font-medium leading-6 text-neutral-200">Chọn khuyến mãi</p>
                </div>
                @php
                    $defaultPackage = isset($depositData['packages'][0]) ? $depositData['packages'][0]->id : '';
                @endphp
                <div
                    class="deposit-codepay-packages js-packages no-scrollbar mb-1 grid grid-cols-2 gap-2 max-xl:-mx-4 max-xl:overflow-auto max-xl:px-4 xl:grid xl:grid-cols-2">
                    @foreach ($depositData['packages'] as $key => $package)
                        @php
                            $isActive = $defaultPackage == $package->id;
                        @endphp
                        <label @class([
                            "account-active-button packages__item relative flex min-h-[51px] cursor-pointer items-center justify-center rounded px-[13px] max-xl:min-w-[140px] max-xl:shrink-0 max-xl:py-[7px] xl:min-h-[58px] xl:rounded xl:px-2 !bg-[url('/public/vendor/accounts/images/codepay/promo-card-bg.jpg')] [&:has(input:checked)]:!bg-[url('/public/vendor/accounts/images/codepay/promo-card-active-bg.jpg')]",
                            'active' => $isActive,
                        ])>
                            <input type="radio" name="packageId" value="{{ $package->id }}"
                                class="package-radio peer absolute opacity-0"
                                data-percent="{{ $package->promotion ?? 0 }}"
                                data-total="{{ number_format($package->max_amount ?? 0) }}"
                                data-multiplier="{{ $package->multiplier ?? 0 }}" {{ $isActive ? 'checked' : '' }}
                                data-max-amount="{{ $package->max_amount ?? 0 }}">
                            <div class="flex flex-col text-center xl:px-4">

                                <div
                                    class="packages__title text-xs font-semibold uppercase tracking-tight text-account-text-primary xl:text-sm">
                                    <span>{{ $package->description ?? '' }}</span>
                                </div>
                                {{-- <div class="packages__sub text-[10px] leading-[15px] text-account-text-primary xl:text-[10px] xl:leading-[16px]">
                                    <span @class([
                                        'max-xl:hidden' => isset($package->mbDescription),
                                    ])>{{ $package->description ?? '' }}</span>
                                    @if (isset($package->mbDescription))
                                    <span class="hidden max-xl:block">{{ $package->mbDescription ?? '' }}</span>
                                    @endif
                                </div> --}}
                            </div>
                        </label>
                    @endforeach
                </div>

                <ul @class([
                    'js-packages-content grid grid-cols-1 gap-y-0.5 rounded-lg bg-neutral-650 py-[7px] px-3',
                    'hidden' => count($depositData['packages']) == 0 || $defaultPackage == 1,
                ])>
                    <li class="flex h-[28px] items-center justify-between border-b border-b-[#fff]/5">
                        <p class="text-xs text-neutral-200">Khuyến mãi</p>
                        <p class="js-package-bonus text-xs font-medium text-primary">---</p>
                    </li>
                    <li class="flex h-[28px] items-center justify-between border-b border-b-[#fff]/5">
                        <p class="text-xs text-neutral-200">Thực nhận</p>
                        <p class="js-package-amount text-xs font-medium text-primary">---</p>
                    </li>
                    <li class="flex h-[28px] items-center justify-between border-b border-b-[#fff]/5">
                        <p class="text-xs text-neutral-200">Số vòng cược</p>
                        <p class="js-package-multiplier text-xs font-medium text-primary">---</p>
                    </li>
                    <li class="flex h-[28px] items-center justify-between">
                        <p class="text-xs text-neutral-200">Tiền cược yêu cầu</p>
                        <p class="js-package-required-amount text-xs font-medium text-primary">---</p>
                    </li>
                </ul>

            </div>
        @endif
    @else
        <x-accounts::ticket-qr :qrSrc="$depositData['nicepayData']->qrcode ?? ''" :qrCenterLogo="$qrLogoBank">
            <x-slot:topQR>
                <div class="flex w-full items-center justify-center max-xl:mb-2 max-xl:justify-between">
                    <div class="js-countdown-timer-codepay flex items-center xl:hidden">
                        <div class="js-countdown-progress-codepay flex items-center gap-1">
                            <img src="{{ asset('vendor/accounts/images/codepay/icon-timer.svg') }}" alt="Timer"
                                class="size-4 xl:size-6">
                            <div class="js-countdown-timer text-xs font-semibold text-alert-wait">
                                --:--
                            </div>

                        </div>
                        <div
                            class="js-countdown-timer-codepay-success flex hidden items-center gap-1 text-sm font-semibold leading-[14.4px] text-primary-400">
                            <img src="{{ asset('vendor/accounts/images/codepay/icon-timer.svg') }}" alt="Timer"
                                class="size-4 xl:size-6">
                            <span>Nạp tiền thành công (5s)</span>
                        </div>
                    </div>
                    <p class="text-xs font-normal text-neutral xl:mb-[6px] xl:font-medium">Phiếu
                        <span class="js-codepayPostID">#{{ $depositData['nicepayData']->invoice_id ?? '' }}</span>
                    </p>
                </div>
            </x-slot:topQR>
            <x-slot:bottomQR>
                <div class="mt-2 w-full gap-3 max-xl:flex max-xl:items-center max-xl:justify-between">
                    <div class="flex grow items-center gap-1 xl:hidden xl:gap-1.5">
                        <img src="{{ $qrLogoBank }}" class="h-4 xl:h-5"
                            alt="{{ BankNameHelper::convertBankName($depositData['nicepayData']->bank_name) }}">
                        <span
                            class="text-xs text-neutral">{{ BankNameHelper::convertBankName($depositData['nicepayData']->bank_name) ?? '' }}</span>
                    </div>
                    <img src="{{ asset('vendor/accounts/images/codepay/qr-banks.png') }}"
                        class="max-h-[14px] object-contain xl:max-h-4">
                </div>
            </x-slot:bottomQR>
            <x-slot:content>
                <div class="flex h-full flex-col justify-center">
                    <div class="mb-1 flex items-center max-xl:hidden">
                        <div class="js-countdown-timer-codepay flex items-center">
                            <div class="js-countdown-progress-codepay flex items-center gap-1">
                                <img src="{{ asset('vendor/accounts/images/codepay/icon-timer.svg') }}" alt="Timer"
                                    class="size-6">
                                <div class="js-countdown-timer text-xs font-semibold text-alert-wait">
                                    --:--
                                </div>
                            </div>
                            <div
                                class="js-countdown-timer-codepay-success flex hidden items-center gap-1 text-sm font-semibold leading-[14.4px] text-secondary-300">
                                <img src="{{ asset('vendor/accounts/images/codepay/icon-timer.svg') }}" alt="Timer"
                                    class="size-4 xl:size-6">
                                <span>Nạp tiền thành công (5s)</span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Info -->
                    <div class="text-neutral">
                        <!-- Số tiền -->
                        <div class="flex min-h-[32px] items-center gap-1 px-2 text-xs xl:min-h-[24px]">
                            <p class="w-[120px] shrink-0 text-neutral-200">Số tiền:</p>
                            <p class="grow font-medium capitalize">
                                {{ number_format($depositData['nicepayData']->amount ?? 0) }}
                                VND</p>
                            <button type="button"
                                class="js-copy-btn [&.active]:text-gradient ml-auto shrink-0 text-[16px] text-neutral-300"
                                data-copy="{{ $depositData['nicepayData']->amount ?? 0 }}">
                                <i class="icon-copy"></i>
                            </button>
                        </div>
                        <!-- Ngân hàng -->
                        <div class="flex min-h-[32px] items-center gap-1 px-2 text-xs max-xl:hidden xl:min-h-[24px]">
                            <p class="w-[120px] shrink-0 text-neutral-200">Ngân hàng:</p>
                            <div class="flex grow items-center gap-1.5">
                                <span
                                    class="font-medium">{{ BankNameHelper::convertBankName($depositData['nicepayData']->bank_name) ?? '' }}</span>
                                <img src="{{ $qrLogoBank }}" class="size-4">
                            </div>
                        </div>

                        <!-- Số tài khoản -->
                        <div class="flex min-h-[32px] items-center gap-1 px-2 text-xs xl:min-h-[24px]">
                            <p class="w-[120px] shrink-0 text-neutral-200">Số tài khoản:</p>
                            <p class="grow break-all font-medium">
                                {{ $depositData['nicepayData']->bank_account_no ?? '' }}</p>
                            <button type="button" class="js-copy-btn ml-auto shrink-0 text-[16px] text-neutral-300"
                                data-copy="{{ $depositData['nicepayData']->bank_account_no ?? '' }}">
                                <i class="icon-copy"></i>
                            </button>
                        </div>

                        <!-- Chủ tài khoản -->
                        <div class="flex min-h-[32px] items-center gap-1 px-2 text-xs xl:min-h-[24px]">
                            <p class="w-[120px] shrink-0 text-neutral-200">Chủ tài khoản:</p>
                            <p class="grow break-all font-medium">
                                {{ $depositData['nicepayData']->bank_account_name ?? '' }}</p>
                            <button type="button" class="js-copy-btn ml-auto shrink-0 text-[16px] text-neutral-300"
                                data-copy="{{ $depositData['nicepayData']->bank_account_name ?? '' }}">
                                <i class="icon-copy"></i>
                            </button>
                        </div>

                        <!-- Nội dung -->
                        <div
                            class="flex min-h-[32px] items-center gap-1 rounded bg-[#0000004D] px-2 text-xs xl:min-h-[24px]">
                            <p class="w-[120px] shrink-0 text-neutral-200">Nội dung:</p>
                            <p class="grow break-all font-medium text-primary">
                                {{ $depositData['nicepayData']->code ?? ($depositData['nicepayData']->content ?? '') }}
                            </p>
                            <button type="button" class="js-copy-btn ml-auto shrink-0 text-[16px] text-neutral-300"
                                data-copy="{{ $depositData['nicepayData']->code ?? ($depositData['nicepayData']->content ?? '') }}">
                                <i class="icon-copy"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </x-slot:content>
        </x-accounts::ticket-qr>
        <ul class="mb-5 mt-2.5 list-disc space-y-[2px] pl-4 text-[12px] italic leading-[18px] text-neutral xl:mb-6">
            <li>Nạp bằng tài khoản chính chủ.</li>
            <li>Giao dịch sẽ bị hủy nếu nhập sai thông tin chuyển tiền.</li>
        </ul>
    @endif

    <div class="mb-0 text-center xl:mb-6">
        @if ($step == 1)
            <x-kit.button type="submit" variant="primary" id="form-submit-step1"
                class="js-submit-button mx-auto min-h-[40px] w-auto text-[14px] max-xl:min-w-full xl:!w-[220px]">
                Tạo Mã QR
            </x-kit.button>
        @else
            <x-kit.button type="button" id="form-submit-cancel"
                class="js-qr-button mx-auto min-h-[40px] text-[14px] max-xl:min-w-full xl:!w-[220px]">
                Tạo Mã QR Mới<span class="js-remaining-time"></span>
            </x-kit.button>
        @endif
    </div>

    @if (session('error'))
        <div class="mb-3 text-red-500">{{ session('error') }}</div>
    @endif
</form>

<div class="mb-2">
    <x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.deposit_codepay_guide.title') }}"
        icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
        @foreach (config('account.deposit_codepay_guide.sections') as $section)
            <div class="mb-1.5 rounded-lg bg-[#0000002E] px-4 py-3">
                @if ($section['heading'])
                    <p class="mb-2 text-[10px] font-bold uppercase leading-[12px] text-primary">
                        {{ $section['heading'] }}
                    </p>
                @endif
                <ul class="list-disc space-y-1 pl-4 text-xs text-neutral-200">
                    @foreach ($section['items'] as $item)
                        <li>{{ $item }}</li>
                    @endforeach
                </ul>
            </div>
        @endforeach
    </x-kit.accordion>
</div>
@push('scripts')
    @once
        <script>
            // Define the data first
            window.codepayConfig = {
                nicepayData: @json($depositData['nicepayData'] ?? null),
                step: @json($step),
                expiryTimeUtc: @json($depositData['nicepayData']->expired_at_utc ?? null),
                createdTime: @json($depositData['nicepayData']->expired_countdown_time ?? null),
            };

            const codepayConfirmModal = `<x-ui.modal size="lg" :id="'codepay-confirm-modal'">
                <x-ui.modals.logout-modal :id="'codepay-confirm-modal'" />
            </x-ui.modal>`
        </script>
    @endonce
    @vite(['resources/js/deposit/codepay.js'])
@endpush
