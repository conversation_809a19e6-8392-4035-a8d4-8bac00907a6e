@props(['depositData', 'isOpenNote'])

<div class="section-ewallet max-xl:mb-3 max-xl:rounded-2xl max-xl:bg-account-surface-primary max-xl:p-3">
    <div class="mb-1 text-xs font-medium text-neutral-200">Ch<PERSON><PERSON> ví điện tử</div>
    <div class="ewallet-group mb-5 flex items-center gap-2 xl:mb-6">
        @foreach ($ewallets as $ewallet)
            @php
                $isChecked = $currentEwallet === $ewallet['key'];
                $iconWallet = asset(
                    'vendor/accounts/images/ewallet/' . $ewallet['key'] . ($ewallet['isMaintenance'] ? '-disabled' : '') . '.svg',
                );
            @endphp
            <div class="ewallet-item-wrapper {{ $ewallet['isMaintenance'] ? 'is-maintenance' : '' }}">
                <label @class([
                    'ewallet-item relative account-active-button flex cursor-pointer xl:px-[14px] px-[7px] h-[44px] items-center justify-center gap-2 rounded-md',
                    'active' => $isChecked,
                ])>
                    <input type="radio" name="ewalletType" value="{{ $ewallet['key'] }}" class="absolute opacity-0"
                        {{ $isChecked ? 'checked' : '' }}>
                    <i
                        class="icon-radio group-has-checked:block absolute right-[3px] top-[3px] hidden text-[10px] leading-[1] text-[#CDA964] xl:right-[5px] xl:top-[5px] xl:text-[16px] xl:leading-4"></i>
                    <img src="{{ $iconWallet }}" alt="{{ $ewallet['key'] }}" class="size-6">
                    <p @class([
                        'text-sm font-medium text-neutral group-has-[i]:block',
                        '!text-neutral-200' => $ewallet['isMaintenance'],
                    ])>{{ $ewallet['name'] }}</p>
                    @if ($ewallet['isMaintenance'])
                        <div
                            class="ewallet-item-badge absolute left-auto right-auto top-[-7px] min-w-[48px] rounded-full bg-neutral-500 px-2 text-[8px] font-medium leading-3 text-[#FFFFFF]">
                            Bảo trì</div>
                    @endif
                </label>
            </div>
        @endforeach
    </div>

    <div class="rounded-2xl max-xl:mt-5 xl:mb-6 xl:bg-account-surface-primary">
        <div class="relative mb-5 xl:mb-6">
            <p class="mb-1 text-xs font-medium text-neutral-200">
                Tài khoản
            </p>
            @if (!empty($dropdownOptions))
                <x-kit.dropdown id="provider-filter" :options="$dropdownOptions" class="w-full" :value="$currentEwalletItem->account_no" />
            @endif
        </div>

        <x-accounts::ticket-qr :qrSrc="$currentEwalletItem->qr_code" wrapperClass="max-xl:h-[358px]">
            <x-slot:content>
                <div class="js-crypto-qr-container flex h-full flex-1 flex-col justify-center gap-2 text-neutral">
                    <!-- Chủ tài khoản -->
                    <div class="flex min-h-[32px] items-center gap-1 px-2 py-1.5 text-xs">
                        <p class="w-[120px] shrink-0 text-neutral-200">Chủ tài khoản:</p>
                        <p class="js-account-name grow break-all font-medium">
                            {{ $currentEwalletItem->account_name }}</p>
                        <button type="button" class="js-copy-btn js-account-name-copy ml-auto shrink-0 text-[16px] text-neutral-300"
                            data-copy="{{ $currentEwalletItem->account_name }}">
                            <i class="icon-copy"></i>
                        </button>
                    </div>
                    <!-- Số tài khoản -->
                    <div class="flex min-h-[32px] items-center gap-1 px-2 py-1.5 text-xs">
                        <p class="w-[120px] shrink-0 text-neutral-200">Số tài khoản:</p>
                        <p class="js-account-no grow break-all font-medium">
                            {{ $currentEwalletItem->account_no }}</p>
                        <button type="button" class="js-copy-btn js-account-no-copy ml-auto shrink-0 text-[16px] text-neutral-300"
                            data-copy="{{ $currentEwalletItem->account_no }}">
                            <i class="icon-copy"></i>
                        </button>
                    </div>

                    <!-- Nội dung -->
                    <div class="flex min-h-[32px] items-center gap-1 rounded-md bg-[#0000004D] px-2 py-1.5 text-xs">
                        <p class="w-[120px] shrink-0 text-neutral-200">Nội dung:</p>
                        <p class="grow break-all font-semibold text-primary-400">
                            {{ $depositData['ewalletCode'] }}
                        </p>
                        <button type="button" class="js-copy-btn ml-auto shrink-0 text-[16px] text-neutral-300"
                            data-copy="{{ $depositData['ewalletCode'] }}">
                            <i class="icon-copy"></i>
                        </button>
                    </div>
                </div>
            </x-slot:content>
        </x-accounts::ticket-qr>
    </div>
</div>

<div class="mb-6">
    <x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.deposit_ewallet_guide.title') }}"
        icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
        @foreach (config('account.deposit_ewallet_guide.sections') as $section)
            <div class="rounded-lg bg-[#0000002E] px-4 py-3 mb-1.5">
                @if ($section['heading'])
                    <p class="mb-2 text-[10px] font-bold uppercase leading-[12px] text-primary">
                        {{ $section['heading'] }}
                    </p>
                @endif
                <ul class="list-disc space-y-1 pl-4 text-xs text-neutral-200">
                    @foreach ($section['items'] as $item)
                        <li>{{ $item }}</li>
                    @endforeach
                </ul>
            </div>
        @endforeach
    </x-kit.accordion>
</div>
<div class="mb-2">
    <x-kit.accordion isOpen="{{ $isOpenNote }}" title="{{ config('account.deposit_ewallet_note.title') }}"
    icon="{{ asset('vendor/accounts/images/icons/icon-guide.svg') }}">
    @foreach (config('account.deposit_ewallet_note.sections') as $section)
        <div class="rounded-lg bg-[#0000002E] px-4 py-3 [&_strong]:text-white [&_strong]:font-semibold mb-1.5">
            @if ($section['heading'])
                <p class="mb-2 text-[10px] font-bold uppercase leading-[12px] text-primary">
                    {{ $section['heading'] }}
                </p>
            @endif
            <ul class="list-disc space-y-1 pl-4 text-xs text-neutral-200">
                @foreach ($section['items'] as $item)
                    <li>{!! $item !!}</li>
                @endforeach
            </ul>
        </div>
    @endforeach
</x-kit.accordion>
</div>

@pushOnce('scripts')
    <script>
        var ewalletDropdownOptions = {!! json_encode($dropdownOptions, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_AMP | JSON_HEX_QUOT) !!};
    </script>
    @vite(['resources/js/deposit/ewallet.js', 'resources/js/deposit/downloadQr.js'])
@endPushOnce
