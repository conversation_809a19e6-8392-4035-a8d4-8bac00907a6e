<div class="layout-account bg-neutral-150 pb-3 xl:pb-[80px] xl:pt-10">
    <div class="container-account">
        <div class="flex w-full items-start gap-8 2xl:gap-10">
            <div class="hidden w-[264px] shrink-0 overflow-hidden rounded-2xl bg-account-surface-primary xl:block">
                <x-accounts::layouts.user-card class="mb-6" />
                <x-accounts::layouts.desktop-navbar />
            </div>
            <div class="flex w-full flex-col gap-6 max-xl:gap-3 self-stretch">
                <div class="flex xl:hidden gap-5 items-center w-full">
                    <div
                        class="flex items-center gap-2 h-[62px] w-full"
                    >
                        <i
                            class="icon-left-chevron text-neutral text-[24px]"
                            onclick="goBack()"
                        ></i>
                        <h2 class="text-[16px] pr-8 flex-1 text-center capitalize text-neutral font-bold leading-[24px]">
                            {{-- @if ($currentRoute === 'help.slug' && !empty($currentRouteSlug))
                                {{ __('routeTitle.help.' . $currentRouteSlug) }}
                            @else
                                {{ __('routeTitle.' . $currentRoute) }}
                            @endif --}}
                        </h2>
                    </div>
                </div>
                {{ $slot }}
            </div>
        </div>
    </div>
</div>
