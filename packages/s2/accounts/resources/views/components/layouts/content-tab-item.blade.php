@props([
    'class' => '',
    'routeName' => 'deposit.index',
    'apiData' => [],
    'items' => [],
    'lastDeposit' => null,
    'currentTab' => null,
    'lastDepositSuccess' => null,
])

<div @class([
    'account-nav-tabs shrink-0 no-scrollbar max-xl:-mx-4 max-xl:mb-3 flex items-center max-xl:px-4 xl:border-b-[1px] max-xl:overflow-y-hidden overflow-x-auto border-account-surface-tertiary max-xl:bg-account-surface-primary',
    $class,
])>
    @foreach ($items as $item)
        @php
            $maintenanceFromData = $apiData['isDepositMaintenance'][$item['value']] ?? false;
            $maintenanceFromTab = !($item['is_active'] ?? true);
            $isDisabled = $maintenanceFromData || $maintenanceFromTab;

            $badgeIcon = match (true) {
                $isDisabled => 'badge-off',
                $item['value'] === $lastDeposit => 'badge-recent',
                !empty($item['badge']) => 'badge-' . $item['badge'],
                count($lastDepositSuccess) <= 0 && $item['value'] === 'codepay' => 'badge-suggest',
                default => null,
            };

            $isActiveTab = $item['id'] === $currentTab;
        @endphp

        <div @class([
            'account-nav-tabs-item shrink-0 bg-no-repeat bg-center',

            'cursor-no-drop' => $isDisabled,
            'active' => $isActiveTab,
        ])>
            <a href="{{ route($routeName, ['tab' => $item['id']]) }}" @class([
                'pointer-events-none' => $isDisabled,
                'relative flex items-center gap-2 max-xl:shrink-0 text-[14px] leading-[18px] font-medium text-account-text-primary py-3 px-[12px] xl:px-[16px] capitalize whitespace-nowrap',
                'after:absolute after:left-0 after:bottom-0 after:w-full after:bg-account-text-brand-primary !text-account-text-brand-primary' => $isActiveTab,
            ])>

                {{-- Badge Icons --}}
                @if ($badgeIcon)
                    <img src="{{ asset("vendor/accounts/images/icons/{$badgeIcon}.svg") }}"
                        alt="label-{{ $item['badge'] ?? 'recent' }}"
                        class="absolute right-1 top-0 z-[1] h-[12px] object-contain">
                @endif

                {{-- Tab Icon --}}
                @if ($item['icon'])
                    <img src="{{ asset('vendor/accounts/images/icons/icon-tab-' . $item['icon'] . ($isActiveTab ? '-active' : '') . '.svg') }}"
                        alt="icon" @class(['size-5 object-contain z-[1]', 'grayscale' => $isDisabled])>
                @endif

                {{-- Tab Title --}}
                <span @class(['z-[1]', 'text-neutral-300' => $isDisabled])>
                    {{ $item['title'] }}
                </span>

                <span
                    class="absolute bottom-[-1px] left-1/2 z-[1] hidden h-[4px] w-[28px] translate-x-[-50%] rounded-t-lg bg-account-text-brand-primary [.active_&]:block"></span>
                <span
                    class="absolute bottom-[-1px] left-1/2 z-[1] hidden h-[35px] w-full translate-x-[-50%] rounded-t-lg bg-[url('/public/vendor/accounts/images/history/union.png')] bg-cover [.active_&]:block bg-center"></span>
                <span
                    class="absolute bottom-[-1px] left-0 hidden h-[calc(100%+1px)] w-full bg-[linear-gradient(180deg,_rgba(24,_59,_47,_0)_0%,#183B2F_100%)] [.active_&]:block"></span>
            </a>
        </div>
    @endforeach
</div>
