<nav class="account-nav p-5 pt-0 text-sm font-medium capitalize xl:pt-2">
    <ul>
        @foreach (config('account.navbar') as $group)
            <li class="nav-group border-b border-[#F1F3F9] py-[14px] xl:py-3">
                @if ($group['label'])
                    <h3 class="mb-2 text-xs font-medium text-neutral-600">
                        {{ $group['label'] }}
                    </h3>
                @endif

                <ul class="space-y-1">
                    @foreach ($group['items'] as $item)
                        <li>
                            <a href="{{ $item['href'] }}"
                                class="{{ setMenuActiveClassByRegex($item['urlRegex']) }} account-nav__link group flex items-center rounded-lg px-3 py-3 text-neutral-450 transition-colors xl:hover:bg-[#FFF6E3] xl:hover:text-neutral-800 [&.active]:text-neutral-800">
                                <div class="account-nav__icon relative size-6">
                                    <img src="{{ asset('vendor/accounts/images/icons/' . $item['icon'] . '.svg') }}"
                                        alt="{{ $item['label'] }}"
                                        class="default-icon size-6 transition-opacity xl:group-hover:opacity-0" />
                                    <img src="{{ asset('vendor/accounts/images/icons/' . $item['icon'] . '-active.svg') }}"
                                        alt="{{ $item['label'] }}"
                                        class="active-icon absolute inset-0 size-6 opacity-0 transition-opacity xl:group-hover:opacity-100"
                                        loading="lazy" />
                                </div>
                                <span class="ml-2">{{ $item['label'] }}</span>
                            </a>
                        </li>
                    @endforeach
                </ul>
            </li>
        @endforeach

        <li class="mt-3 xl:pb-[22px]">
            <button type="button" onclick="openLogoutModal()"
                class="h-[48px] w-full rounded-lg bg-[#F8FAFB] px-3 py-2.5 text-center flex items-center justify-center gap-2 group text-sm font-medium capitalize text-[#63687E] hover:bg-[#FFF6E3] hover:text-[#27272A] xl:h-[44px]"
            >
                <div class="account-nav__icon relative size-6">
                    <img src="{{ asset('vendor/accounts/images/icons/icon-logout.svg') }}"
                        alt="logout"
                        class="default-icon size-6 transition-opacity xl:group-hover:opacity-0" />
                    <img src="{{ asset('vendor/accounts/images/icons/icon-logout-active.svg') }}"
                        alt="logout"
                        class="active-icon absolute inset-0 size-6 opacity-0 transition-opacity xl:group-hover:opacity-100"
                        loading="lazy" />
                </div>
                {{ __('auth.logout') }}
            </button>
        </li>
    </ul>
</nav>
