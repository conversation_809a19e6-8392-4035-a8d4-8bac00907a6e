@php
    $user = Auth::user();
@endphp
<nav class="account-nav mt-3">
    <div class="sidebar-user-card rounded-[16px] bg-[url('/public/vendor/accounts/images/wallet-balance-card-bg.jpg')] bg-cover bg-center bg-no-repeat px-6 py-[22px]"
        {{ $attributes }}>
        <div class="text-sm font-medium text-[#666D80]">Số dư ví</div>
        <div class="text-[#27272A]break-all text-[18px] font-bold leading-[26px]">{{ $user->balance_txt ?? 0 }}K</div>
    </div>
    <div class="rounded-[0_0_16px_16px] bg-account-surface-primary p-5 pt-[14px] text-sm font-medium capitalize">

        <ul>
            @foreach (config('account.navbar') as $group)
                <li class="nav-group border-b border-[#F1F3F9] py-3">
                    @if ($group['label'])
                        <h3 class="mb-3 text-xs font-medium text-[#6E6F74]">
                            {{ $group['label'] }}
                        </h3>
                    @endif

                    <ul class="space-y-1">
                        @foreach ($group['items'] as $item)
                            <li>
                                <a href="{{ $item['href'] }}"
                                    class="{{ setMenuActiveClassByRegex($item['urlRegex']) }} account-nav__link group flex items-center rounded-lg px-3 py-3 text-[#27272A] transition-colors hover:bg-[#FFF6E3]">
                                    <div class="account-nav__icon relative size-6">
                                        <img src="{{ asset('vendor/accounts/images/icons/' . $item['icon'] . '.svg') }}"
                                            alt="{{ $item['label'] }}"
                                            class="default-icon size-6 transition-opacity group-hover:opacity-0" />
                                        <img src="{{ asset('vendor/accounts/images/icons/' . $item['icon'] . '-active.svg') }}"
                                            alt="{{ $item['label'] }}"
                                            class="active-icon absolute inset-0 size-6 opacity-0 transition-opacity group-hover:opacity-100"
                                            loading="lazy" />
                                    </div>
                                    <span class="ml-2">{{ $item['label'] }}</span>
                                </a>
                            </li>
                        @endforeach
                    </ul>
                </li>
            @endforeach

            <li class="mt-6">

                <button type="button" onclick="openLogoutModal()"
                    class="flex h-[48px] w-full items-center justify-center rounded-lg bg-[#F8FAFB] px-3 py-2.5 text-center text-sm font-medium text-[#63687E] hover:bg-[#FFF6E3] hover:text-[#27272A]">
                    <img src="{{ asset('vendor/accounts/images/icons/icon-logout.png') }}" alt="logout"
                        class="mr-2 size-6">
                    {{ __('auth.logout') }}

                </button>
            </li>
        </ul>
    </div>
</nav>
