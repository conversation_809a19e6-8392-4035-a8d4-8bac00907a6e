@php
    $currentUser = Auth::user();
    $fullname = $currentUser->fullname;
    $masked = substr($fullname, 0, 4) . '****';
    $isUpdatedFullname = $currentUser->is_updated_fullname ?? (($masked === $currentUser->username) ? 0 : 1);
    $account = config('account.profile');
    $currentTab = request()->tab ?? 'info';
    $tabList = [
        [
            'id' => 'info',
            'icon' => 'user',
            'value' => 'info',
            'title' => 'Thông tin cá nhân',
        ],
        [
            'id' => 'password',
            'icon' => 'password',
            'value' => 'password',
            'title' => 'Mật khẩu',
        ]
    ];

    

@endphp

<x-accounts::layouts.content-tab-item
    class="xl:hidden"
    routeName="account.profile"
    currentTab="{{ $currentTab }}"
    :apiData="[]"
    :items="$tabList"
    :lastDeposit="[]"
    :lastDepositSuccess="[]"
/>
@section('account-header')
    <x-accounts::header-navigation backUrl="/account" title="Tài kho<PERSON>n" />
@endsection
<div
    @class([
        'grid xl:h-full grid-cols-1 gap-[20px] xl:grid-cols-2 xl:px-10',
        $account['wrapper_class'],
    ])>
    <div @class([
        'w-full rounded-2xl xl:rounded-[24px] bg-account-surface-primary p-3 xl:py-6 xl:px-0 !xl:pr-[20px] xl:max-w-[486px] xl:ml-auto',
        'max-xl:hidden' => $currentTab === 'password',
    ])>
        <div class="xl:mb-6 flex items-center gap-2 mb-5">
            <img src="{{ asset('vendor/accounts/images/icons/icon-user.svg') }}" alt="password">
            <label class="block text-[14px] font-medium text-account-text-primary capitalize">
                {{ $account['personal_information'] }}
            </label>
        </div>

        <form id="account-information">
            <div class="account-information__form grid gap-4 xl:gap-[20px]">
                <x-kit.input type="text" label="{{ $account['username'] }}"
                    value="{{ Auth::user()->username ?? '' }}" disabled id="username" name="username"
                    class="bg-neutral-50" />

                <x-kit.input type="text" label="{{ $account['show_username'] }}"
                    value="{{ Auth::user()->fullname ?? '' }}" disabled
                    id="fullname" name="fullname" > 
                    @if (!$isUpdatedFullname)
                    <x-slot:endAdornment>
                    <div class="js-update-fullname-btn cursor-pointer text-[12px] capitalize text-alert-wait font-medium"
                        onclick="updateFullname()">{{ $account['update'] }}</div>
                    </x-slot:endAdornment>
                    @endif
                </x-kit.input>

                <x-kit.input type="text" label="{{ $account['phone_number'] }}"
                    value="{{ Auth::user()->phone ?? '' }}" disabled id="phone-number-input" name="phone-number"
                    class="bg-neutral-50" />
                @if (Auth::user()->is_verify_email)
                <x-kit.input type="text" label="{{ $account['email'] }}" value="{{ Auth::user()->email ?? '' }}"
                    disabled id="email" name="email"
                    placeholder="Nhập Email" isVerified
                    endAdornment="{{ $account['verify'] }}"
                    endAdornmentClass="js-verify-email font-[500] text-[24px] pointer-events-none text-account-text-brand-primary"
                    class="pr-[30px] !bg-account-surface-secondary"
                    inputClass="!bg-account-surface-secondary !border-account-surface-tertiary"
                />
                @else
                <div class="flex h-[50px] items-center justify-between gap-2 rounded-lg border border-[#80D59C] px-2.5 bg-[linear-gradient(180deg,_#0A2A1C_0%,_#1A4D1D_100%)]">
                    <div class="flex items-center gap-2">
                        <div
                            class="flex size-6 items-center justify-center rounded-full bg-[linear-gradient(180deg,_#C9AC06_0%,_#9A6502_100%)] py-[10px]">
                            <i class="icon-mail text-white text-[14px]"></i>
                        </div>
                        <div class="text-account-text-primary text-sm capitalize">Xác thực Email</div>
                    </div>
                    <div class="cursor-pointer text-[12px] capitalize text-alert-wait font-medium" onclick="verifyEmail()">{{ $account['verify'] }}
                    </div>
                </div>
                @endif
            </div>
        </form>
    </div>
    <div @class([
        'xl:max-w-[486px]',
        'max-xl:hidden' => $currentTab === 'info' || !$currentTab,
    ])>
        <x-accounts::change-password-container />
    </div>
</div>
