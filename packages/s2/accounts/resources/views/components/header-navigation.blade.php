@props([
    'backUrl' => '',
    'title' => '',
])
<div class="header-back sticky top-0 z-10 h-[56px] bg-[#00120C] flex justify-center items-center xl:hidden border-b border-[#23413A]">
    <div data-url-back="{{ $backUrl }}" class="absolute top-1/2 -translate-y-1/2 left-3 cursor-pointer" onclick="goBack(event)">
        <i class="icon-chevron-left text-[24px] text-white pointer-events-none"></i>
    </div>
    <span class="text-[16px] font-medium capitalize leading-[24px] text-white">
        {{ with_brand_name($title) }}
    </span>
</div>

@pushOnce('scripts')
    <script>
        const goBack = (event) => {
            if (event.target.dataset.urlBack) {
                window.location.href = event.target.dataset.urlBack;
            } else if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '/';
            }
        }
    </script>
@endPushOnce
