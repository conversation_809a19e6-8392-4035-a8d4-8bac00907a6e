@props([
    'networks' => [],
    'rate' => 0,
    'defaultAmount' => 0,
    'defaultNetwork' => '',
    'label' => 'Mệnh giá thẻ',
    'inputName' => 'card_amount',
    'isShowReceive' => false,
])
<div class="flex flex-col gap-2 text-neutral">
    <div class="text-xs font-medium text-neutral-200">{{ $label }}</div>
    <input class="js-card-amount-input pointer-events-none absolute opacity-0" name="{{ $inputName }}" type="number"
        value="{{ $defaultAmount }}">

    <div class="js-card-amount-list grid grid-cols-3 gap-2 lg:grid-cols-5">
        @foreach ($networks ?? [] as $card)
            <button type="button" @class([
                'js-card-amount-item relative account-active-button bg-card h-[48px] rounded group p-[1px]',
                'active' => $card->key == $defaultAmount,
                'xl:h-[56px]' => $isShowReceive,
                'xl:h-[40px]' => !$isShowReceive,
            ]) data-value="{{ $card->key }}">
                <div class="flex h-full w-full flex-col items-center justify-center rounded-[inherit] bg-card-amount">
                    <div @class([
                        'card-label-amount text-[12px] leading-[16px] text-neutral xl:text-[14px] xl:leading-[20px]',
                    ])>
                        {{ $card->label }}</div>
                    @if ($isShowReceive)
                        <div @class([
                            'card-label-receive text-[10px] leading-4 text-neutral-200',
                        ])>
                            Nhận {{ number_format($card->key * $rate) }}
                        </div>
                    @endif
                </div>
            </button>
        @endforeach
    </div>

</div>
