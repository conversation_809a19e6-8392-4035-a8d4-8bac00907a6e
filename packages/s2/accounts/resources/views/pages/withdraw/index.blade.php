<x-layout>
    <x-accounts::layouts>
        <div class="w-full flex-1 xl:bg-account-surface-primary rounded-xl xl:rounded-3xl">
            <div class="xl:max-w-[650px] xl:mx-auto xl:pt-6 xl:pb-8">
                <x-accounts::withdraw
                    :withdrawData="$withdrawData"
                    :setDefaultBank="$setDefaultBank"
                    :recentWithdrawTab="$recentWithdrawTab"
                />
            </div>
            @hasSection('contentFullWidth')
                <div class="p-3 xl:px-10 xl:pt-0 xl:pb-8 bg-[#00221B] rounded-2xl">
                    @yield('contentFullWidth')
                </div>
            @endif
        </div>
    </x-accounts::layouts>
    @pushOnce('scripts')
        @vite(['resources/js/deposit/index.js'])
    @endPushOnce
</x-layout>
