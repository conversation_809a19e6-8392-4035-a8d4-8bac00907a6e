<x-layout>
    <x-accounts::layouts>
        {{-- Desktop --}}
        {{-- <x-accounts::deposit.suggest-deposit :depositData="$depositData" class="max-xl:hidden" /> --}}
        <div class="w-full flex-1 rounded-xl xl:rounded-3xl xl:bg-account-surface-primary">
            <div class="xl:mx-auto xl:max-w-[635px] xl:py-6">
                <x-accounts::deposit :depositData="$depositData" :lastDeposit="$lastDeposit" :lastDepositSuccess="$lastDepositSuccess" />
            </div>
            @hasSection('contentFullWidth')
                <div class="p-3 xl:px-10 xl:pt-0 xl:pb-8 bg-[#00221B] rounded-2xl">
                    @yield('contentFullWidth')
                </div>
            @endif
        </div>
    </x-accounts::layouts>
    @pushOnce('scripts')
        @vite(['resources/js/deposit/index.js'])
    @endpushOnce
</x-layout>
