@props(['data'])
<x-layout>
    <x-accounts::layouts>
    <div class="layout-account pb-3 xl:pb-[80px]">
        <div class="container-account">
            <div class="flex w-full items-start gap-8 2xl:gap-10">
                <div class="max-xl:mt-3 w-full shrink-0 overflow-hidden rounded-3xl bg-account-surface-primary xl:w-[264px] xl:rounded-2xl xl:hidden">
                    <x-accounts::layouts.user-card class="mb-6" />
                    <x-accounts::layouts.desktop-navbar />
                </div>
                <div class="hidden w-full flex-col gap-6 self-stretch xl:flex">
                    <div class="hidden flex-col gap-5 lg:flex">
                        <x-accounts::overview.personal-information :userVerificationStatus="$userVerificationStatus" />
                        <x-accounts::deposit.suggest-deposit :depositData="$depositData" class="max-xl:hidden" />
                        <div class="grid grid-cols-2 gap-5">
                            <x-accounts::overview.history.index :listDeposit="$listDeposit" :listWithdraw="$listWithdraw" />
                            <x-accounts::overview.promotion.index :depositData="$depositData" />
                        </div>
                        <div class="grid grid-cols-2 gap-5">
                            <x-accounts::overview.games-suggestion :recommendGames="$recommendGames" />
                            <x-accounts::overview.categories-popular />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x-accounts::layouts>
    @pushOnce('scripts')
        @vite(['resources/js/account/overview.js'])
    @endpushOnce
</x-layout>
