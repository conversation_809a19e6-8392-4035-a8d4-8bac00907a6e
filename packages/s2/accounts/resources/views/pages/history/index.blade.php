<x-layout>
    <x-accounts::layouts>
        @section('account-header')
            <x-accounts::header-navigation backUrl="/account" title="Lịch Sử Cượ<PERSON>/<PERSON>iao Dị<PERSON>" />
        @endsection
        <div class="w-full flex-1 rounded-xl xl:rounded-2xl xl:bg-account-surface-primary">

            <div class="page-history-{{ $selectedTab }} xl:p-6">

                <x-accounts::layouts.content-tab-item class="mb-6" routeName="history.index"
                    currentTab="{{ $selectedTab }}" :apiData="[]" :items="$tabList" :lastDeposit="[]"
                    :lastDepositSuccess="[]" />
                <div id="table-history-data" class="hidden">
                    <div class="flex w-full flex-grow flex-col bg-[#00221B] max-xl:rounded-2xl">
                        <div
                            class="flex flex-grow flex-col justify-between gap-3 rounded-2xl p-3 max-xl:min-h-[calc(100svh-184px)] xl:gap-6 xl:rounded-none xl:p-0">
                            <div class="{{ !(count($list) > 0) ? 'flex-1' : '' }} flex flex-col xl:hidden">
                                @if (count($list) > 0)
                                    @foreach ($list as $item)
                                        <x-accounts::history.card :type="$selectedTab"
                                            :item="$item"></x-accounts::history.card>
                                    @endforeach
                                @else
                                    <div
                                        class="flex h-full min-h-[200px] flex-grow flex-col items-center justify-center gap-4 pt-[104px] max-xl:py-[189px] xl:gap-3">
                                        <i class="icon-nodata text-[70px] text-[#465F59]"></i>
                                        <p
                                            class="text-[12px] font-normal leading-[18px] text-[#636975] xl:text-[14px] xl:leading-[20px]">
                                            Chưa
                                            có dữ liệu
                                            hiển thị</p>
                                    </div>
                                @endif
                            </div>
                            <x-accounts::history.table :list="$list" :type="$selectedTab"></x-accounts::history.table>
                            <x-accounts::history.pagination :pagination="$pagination"
                                :selectedTab="$selectedTab"></x-accounts::history.pagination>
                        </div>
                    </div>
                </div>
                <div id="loading-table-history" class=" min-h-[492px] flex items-center justify-center">
                    <x-ui.loading-spinner />
                </div>
            </div>
        </div>
    </x-accounts::layouts>
    @pushOnce('scripts')
        @vite(['resources/js/history/phone-card-info.js'])
        @vite('resources/js/datetime.js')
        <script>
            window.addEventListener('load', function() {
                $('.js-time-convert').each(function() {
                    $(this).text(convertToClientTimezone($(this).data('time')));
                });
                $('#table-history-data').removeClass('hidden');
                $('#loading-table-history').addClass('hidden');
            });
        </script>
    @endPushOnce
</x-layout>
