@props(['data'])
@php
    $tabsMb = [
        [
            'id' => 'promotion',
            'label' => 'Khuyến Mã<PERSON>',
        ],
        [
            'id' => 'games',
            'label' => 'Trò <PERSON>ơ<PERSON>',
        ],
    ];
    $tabDefault = 'promotion';
@endphp
<x-layout>
    <x-accounts::layouts>
        @section('account-header')
            <x-accounts::header-navigation backUrl="/account" title="Tổng quan" />
        @endsection
        {{-- Desktop --}}
        <div class="flex flex-col gap-3 max-xl:pt-3 xl:gap-5">
            <x-accounts::overview.personal-information :depositData="$depositData" :userVerificationStatus="$userVerificationStatus" />
            <x-accounts::deposit.suggest-deposit :depositData="$depositData" class="max-xl:hidden" />
            {{-- @if ($userVerificationStatus->isShowSection)
                <x-accounts::overview.verify-account :userVerificationStatus="$userVerificationStatus" :depositData="$depositData" />
            @endif --}}
            <div class="js-tab-mb flex gap-3 border-b border-[#23413A] xl:hidden">
                @foreach ($tabsMb as $key => $label)
                    @php
                        $isActive = $tabDefault === $label['id'];
                    @endphp
                    <div data-id="{{ $label['id'] }}"
                        class="{{ $isActive ? 'active' : '' }} relative block px-2.5 py-3 text-sm font-medium capitalize text-account-text-secondary after:absolute after:bottom-[-1px] after:left-0 after:hidden after:h-0.5 after:w-full after:bg-account-text-brand-primary [&.active]:text-account-text-primary [&.active]:after:block">
                        {{ $label['label'] }}</div>
                @endforeach
            </div>
            <div class="flex flex-col gap-3 xl:gap-5">
                <div class="js-overview-tabmb {{ $tabDefault === 'promotion' ? 'block' : 'max-xl:hidden' }} grid grid-cols-1 gap-3 xl:grid-cols-2 xl:gap-5"
                    data-tab="promotion">
                    <x-accounts::overview.history.index :listDeposit="$listDeposit" :listWithdraw="$listWithdraw" />
                    <x-accounts::overview.promotion.index
                        :promotions="$promotions"
                        :commission="$commission"
                        :depositData="$depositData"
                        :slotsInfo="$slotsInfo"
                        :casinoInfo="$casinoInfo"
                    />
                </div>
                <div class="js-overview-tabmb {{ $tabDefault === 'games' ? 'block' : 'max-xl:hidden' }} grid grid-cols-1 gap-3 xl:grid-cols-2 xl:gap-5"
                    data-tab="games">
                    <x-accounts::overview.games-suggestion :recommendGames="$recommendGames" />
                    <x-accounts::overview.categories-popular />
                </div>
            </div>
        </div>
    </x-accounts::layouts>
    @pushOnce('scripts')
        @vite(['resources/js/account/overview.js'])
    @endpushOnce
    @if ($userVerificationStatus->is_show_freespin && $userVerificationStatus->isShowSection)
        <script>
            window.document.addEventListener('DOMContentLoaded', () => {
                const modalClaimFreeSpins = `<x-accounts::overview.modal-claim-freespin :dataFreeSpins="$userVerificationStatus" />`
                window.openModal(modalClaimFreeSpins)
            })
        </script>
    @endif
</x-layout>
