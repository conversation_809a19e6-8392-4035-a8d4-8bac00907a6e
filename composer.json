{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "diglactic/laravel-breadcrumbs": "^9.0", "gehrisandro/tailwind-merge-laravel": "^1.2", "jenssegers/agent": "^2.6", "laravel/framework": "^11.9", "laravel/tinker": "^2.9", "nwidart/laravel-modules": "^12.0", "spatie/laravel-sitemap": "^7.2", "spatie/schema-org": "^3.23", "torann/geoip": "^3.0", "voku/html-min": "^4.5"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "S2\\Accounts\\": "packages/s2/accounts/app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/Helpers.php", "app/Helpers/FlattenClasses.php"]}, "autoload-dev": {"psr-4": {"S2\\Accounts\\": "packages/s2/accounts/app/", "Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}, "merge-plugin": {"include": ["Modules/*/composer.json"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "wikimedia/composer-merge-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}