<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class Affiliate
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);
        $trackingParams = config('constants.affiliate_tracking_params');
        if ($request->hasAny($trackingParams)) {
            $trackingData = [];
            foreach ($trackingParams as $param) {
                if ($request->has($param)) {
                    $trackingData[$param] = $request->get($param);
                }
            }
            if ($request->has('a')) {
                $trackingData['aff_id'] = $request->get('a');
                $querystring = $request->getRequestUri();
                $trackingData['querystring'] = ltrim($querystring, '/');
            }
            foreach ($trackingParams as $param) {
                if (isset($trackingData[$param])) {
                    $response->withCookie(cookie($param, $trackingData[$param], 43200));
                }
            }

        }
        return $response;
    }
}
