<?php

namespace App\Http\Controllers;

use App\Helpers\DetectDeviceHelper;
use App\Services\AuthService;
use App\Services\GameService;
use App\Services\GatewayApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{
    // Api gateway service
    protected $GatewayApi;
    protected $gameService;
    protected $authService;
    protected $brandName;

    public function __construct(GatewayApi $GatewayApi, GameService $gameService, AuthService $authService)
    {
        $this->brandName =  config('app.url');
        $this->GatewayApi = $GatewayApi;
        $this->gameService = $gameService;
        $this->authService = $authService;
    }

    public function index(Request $request)
    {
        // $isMobile = DetectDeviceHelper::isMobile();
        // $isMobile = App::make('isMobile');

        $token = $request->query('token');

        if ($token) {
            $result = $this->authService->loginWithToken($request, $token);
            if ($result['status']) {
                $result['status'] = 'OK';
                return redirect()->route('home')->withCookie($result['cookie']);
            } else {
                $request->session()->flash('TokenExpired', 'Không tìm thấy người dùng');
            }
        }

        //config SEO
        $seo = generateSeoMetaData('home');
        $seo->schemas = [

            generateJsonLd("Organization", [
                "name" => $seo->title,
                "url" => route('home'),
                "logo" => asset('/asset/images/' . strtolower($this->brandName) . '-logo.png'),
                "contactPoint" => [
                    "@Type" => "",
                    "telephone" => "",
                    "contactType" => "",
                    "email" => ""
                ],
                "address" => [
                    "@type" => "",
                    "streetAddress" => "",
                    "addressLocality" => "",
                    "addressRegion" => "",
                    "postalCode" => "",
                    "addressCountry" => ""
                ]
            ]),

            generateJsonLd("WebSite", [
                "url" => route('home'),
                "name" => $seo->title,
                "description" => $seo->description,
                "potentialAction" => [
                    "@type" => "",
                    "target" => "",
                    "query-input" => "",
                ]
            ])
        ];

        $isMobile = DetectDeviceHelper::isMobile() || filter_var($request->cookie('isMobile'), FILTER_VALIDATE_BOOLEAN);
        $limitLiveStream = $isMobile ? 1 : 6;


        $isShowLogin = $request->modal === 'login';
        $isShowRegister = $request->modal === 'register';
        $sessionExpired = $request->modal === 'session-expired';

        $jackpots = $this->gameService->getJackpot($request);

        $livecasino = $this->gameService->getGameByParams(
            $request,
            [
                'limit' => $limitLiveStream,
                'partner' => 'vingame,go,b52,rik',
                'type' => 'baccarat,baucua,sicbo',
                'sort' => 'partner_game_type',
            ],
            '/casino/search/'
        );

        $livecasino->items = $this->gameService->mapJackpotToGames($livecasino->items, $jackpots);

        // Get live casino thumbs
        $liveCasinoThumbs = config('games.liveCasinoThumbs');
        $livecasino->items = array_map(function ($item) use ($liveCasinoThumbs) {
            $thumbId = mb_strtolower($item->partner_provider . '_' . $item->partner_game_id);
            if (in_array($thumbId, $liveCasinoThumbs)) {
                $item->image = asset('/asset/images/home/<USER>/' . $thumbId . '.webp');
            }
            return $item;
        }, $livecasino->items);


        $heroBanners = Config::get('home.herobanner');

        if ($isShowLogin && !$request->session()->has('showLogin')) {
            $request->session()->flash('showLogin', 'true');
        }
        if ($isShowRegister && !$request->session()->has('showRegister')) {
            $request->session()->flash('showRegister', 'true');
        }
        if ($sessionExpired && !$request->session()->has('sessionExpired')) {
            $request->session()->flash('sessionExpired', 'true');
        }
        return view('pages.home', compact('heroBanners', 'livecasino'));
    }
}
