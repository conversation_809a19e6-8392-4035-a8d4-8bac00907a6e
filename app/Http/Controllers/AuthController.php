<?php

namespace App\Http\Controllers;

use App\Auth\GatewayUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use App\Services\GatewayApi;
use App\Services\AuthService;

class AuthController extends Controller
{

    protected $GatewayApi;

    protected $authService;

    public function __construct(GatewayApi $GatewayApi, AuthService $authService)
    {
        $this->GatewayApi = $GatewayApi;
        $this->authService = $authService;
    }

    public function index(Request $request)
    {
        // override SEO
        $seo = App::get('seo');
        $seo->title = 'this is login page';

        return view('pages.login');
    }

    public function signup(Request $request)
    {
        // override SEO
        $seo = App::get('seo');
        $seo->title = 'this is signup page';

        return view('pages.signup');
    }

    public function refresh(Request $request)
    {
        $passHeaders = [];
        $headersAll = $request->headers->all();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }

        $userCookie = $request->cookie('user');

        $response = $this->GatewayApi->get(endpoint: '/refresh', headers: $passHeaders, cookies: ['user' => $userCookie]);

        if (!empty($response->status) && $response->status == 'OK') {
            if (isset($response->user)) {
                $cookie = $this->authService->updateCookie(user: $response->user);
                $filteredCookie = $this->authService->filterCookie(user: $response->user);
                $response->data = $filteredCookie;
                return response()->json($response)->cookie($cookie);
            }
            return response()->json($response);
        }

        $request->session()->flush();
        Auth::logout();
        $request->session()->flash('Unauthorized', 'Tài khoản của bạn đã được đăng nhập trên trình duyệt / thiết bị khác.');
        // Handle 401
        return response()->json([
            'status' => 'Unauthorized',
            'message' => 'require login'
        ], 401)->cookie(Cookie::forget(AuthService::COOKIE_USER_TEXT));
    }
    /**
     * Handle login
     *
     * @param request
     */
    public function handleLogin(Request $request)
    {
        $result = $this->authService->checkLogin($request);
        if ($result['status'] === 'OK' && !empty($result['data'])) {
            $cookie = $this->authService->updateCookie(user: $result['data']);
            $filteredCookieValue = $this->authService->filterCookie(user: $result['data']);
            $result['data'] = $filteredCookieValue;
            return response()->json($result)->cookie($cookie);
        }
        return response()->json($result);
    }

    /**
     * Handle login
     *
     * @param request
     */
    public function handleRegister(Request $request)
    {
        $result = $this->authService->register($request);
        if ($result['status']) {
            return response()->json($result)->cookie($result['cookie']);
        }
        return response()->json($result);
    }

    /**
     * Handle login
     *
     * @param request
     */
    public function handleLogout(Request $request)
    {
        Auth::logout();
        return response()->json([
            'message' => 'Logout success',
            'status' => 'success',
        ])->cookie(Cookie::forget(AuthService::COOKIE_USER_TEXT));
    }

    public function resetPasswordByOtp(Request $request)
    {
        $result = $this->authService->resetPasswordByOtp($request);
        return response()->json($result);
    }

    public function updateUserFullname(Request $request)
    {
        $result = $this->authService->updateUserFullname($request);
        return response()->json($result);
    }

    public function loginWithToken(Request $request,  ?string $token = null)
    {
        return redirect()->action([HomeController::class, 'index'], ['token' => $token]);
    }
}
