<?php

namespace App\Http\Controllers;

use App\Services\GatewayApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use App\Services\GameService;

class EventsController extends Controller
{
    // Api gateway service
    protected $GatewayApi;
    protected $gameService;

    public function __construct(GatewayApi $GatewayApi, GameService $gameService)
    {
        $this->GatewayApi = $GatewayApi;
        $this->gameService = $gameService;
    }

    public function index(Request $request)
    {
        $seo = generateSeoMetaData('su-kien');

        $seo->schemas = [

            generateJsonLd('WebSite', [
                "url" => url()->current(),
                "name" => $seo->title,
                "description" => $seo->description,
                "potentialAction" => [
                    "@type" => "",
                    "target" => "",
                    "query-input" => "",
                ]
            ]),

            generateJsonLd('BreadcrumbList', [
                "itemListElement" => [
                    [
                        "@type" => "ListItem",
                        "position" => "1",
                        "name" => "Trang chủ",
                        "item" => route('home'),
                    ],
                    [
                        "@type" => "ListItem",
                        "position" => "2",
                        "name" => "Khuyến mãi",
                        "item" => route('events'),
                    ],
                ],

            ]),
        ];

        $tabs = config('events.tabs');
        $currentTab = request()->query('tab', 'all');
        $vesocaoUrl = $this->gameService->getVesocaoUrl($request);
        $tabs = array_map(function ($tab) use ($vesocaoUrl) {
            if (isset($tab['id']) && $tab['id'] === 'vesocao') {
                $tab['actionUrl'] = $vesocaoUrl;
            }
            return $tab;
        }, $tabs);

        return view('pages.events', compact('tabs', 'currentTab'));
    }
}
