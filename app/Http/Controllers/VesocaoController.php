<?php

namespace App\Http\Controllers;

use App\Helpers\DetectDeviceHelper;
use App\Services\AuthService;
use App\Services\GatewayApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class VesocaoController extends Controller
{
    // Api gateway service
    protected $GatewayApi;
    protected $brandName;
    

    public function __construct(GatewayApi $GatewayApi)
    {
        $this->GatewayApi = $GatewayApi; 
        $this->brandName =  config('app.url');  
    }

    public function index(Request $request)
    {

        //config SEO
        $seo = generateSeoMetaData('vesocao');
        $seo->schemas = [

            generateJsonLd("Organization",[
                "name"=> $seo->title,
                "url"=> route('home'),
                "logo"=> asset('/asset/images/' . strtolower($this->brandName) . '-logo.png'),
                "contactPoint" => [
                    "@Type" => "",
                    "telephone" => "",
                    "contactType" => "",
                    "email" => ""
                ],
                "address" => [
                    "@type"=>"",
                    "streetAddress"=>"",
                    "addressLocality"=>"",
                    "addressRegion"=>"",
                    "postalCode"=>"",
                    "addressCountry"=>""
                ]
            ]),

            generateJsonLd("WebSite",[
                "url" => route('home'),
                "name" => $seo->title,
                "description" => $seo->description,
                "potentialAction" => [
                    "@type" => "",
                    "target" => "",
                    "query-input" => "",
                ]
            ])
        ];

        return view('pages.vesocao');
    }
}
