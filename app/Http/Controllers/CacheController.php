<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Auth;

class CacheController extends Controller
{
    /**
     * Xóa tất cả cache của ứng dụng
     *
     * @return RedirectResponse
     */
    public function clearCache()
    {
        try {
            $commands = [
                'view:clear',      // Xóa cache view
                'cache:clear',     // Xóa cache ứng dụng
                'config:clear',    // Xóa cache config
                'route:clear',     // Xóa cache route
                'optimize:clear'   // Xóa cache tối ưu hóa
            ];

            foreach ($commands as $command) {
                $result = Artisan::call($command);
                Log::info("Cache command executed: {$command}", ['result' => $result]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Đã xóa tất cả cache thành công'
            ]);
        } catch (\Exception $e) {
            Log::error('Lỗi khi xóa cache: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Không thể xóa cache. Vui lòng thử lại sau.'
            ], 500);
        }
    }

    public function getNicepayData()
    {
        $nicepayData = Cache::get('nicepayData' . Auth::user()->getAuthIdentifier());

        return response()->json([
            'success' => true,
            'data' => $nicepayData ?? ''
        ]);
    }

    public function clearNicepayData()
    {
        Cache::forget('nicepayData' . Auth::user()->getAuthIdentifier());

        return response()->json([
            'success' => true,
            'message' => 'Đã xóa dữ liệu cache nicepay thành công'
        ]);
    }
}
