<?php

namespace App\Enums;

enum UrlPathEnum: string
{
    case LOGIN = '/login';

    case SPORTS = '/sports';

    case CASINO = '/live-casino';

    case GAME = '/games';

    case GAME_CARD = '/games/game-card';

    case NOHU = '/games/nohu';

    case LOTTERY = '/games/lottery';

    case FISHING = '/games/fishing';

    case FIGHTCOCK = '/games/fightcock';

    case EVENTS = '/events';

    case EVENTS_ALL = '/events?tab=all';

    case EVENTS_PROMOTIONS = '/events?tab=promotions';

    case EVENTS_EVENTS = '/events?tab=events';

    case HOME = '/';

    case HELP = '/help';

    case KENO = '/games/keno';

    case SLOTS = '/games/slots';

    case BINGO = '/games/bingo';

    case SABA_SPORTS = '/sports/saba-sports';

    case SABA_SPORTS_VITUAL = '/sports/saba-sports-vitual';

    case IM_SPORTS = '/sports/im-sports';

    case K_SPORTS = '/sports/k-sports';

    case K_SPORTS_VITUAL = '/sports/k-sports-vitual';

    case BTI_SPORTS = '/sports/bti-sports';

    case T_SPORTS = '/sports/t-sports';

    case PP_SPORTS = '/sports/pp-sports';

    case NEWS = '/tin-tuc';

    case NEWS_DETAIL = '/tin-tuc/post';

    case CATEGORY = '/danh-muc';

    case POLICY = '/policy';

    case DISCLAIMER = '/disclaimer';

    case QUESTION = '/questions-and-answers';

    case TERMS = '/terms';

    case GUIDELINES = '/guidelines';

    case ABOUT_US = '/about-us';

    case ACCOUNT_HISTORY = '/account/history';

    case VESOCAO_URL = '/vesocaoUrl';
}
