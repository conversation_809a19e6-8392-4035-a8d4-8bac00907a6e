<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\GatewayEndpoint;
use App\Enums\UrlPathEnum;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

final class GameService
{
    public const SEARCH_GAME_URL = '/game/search';
    public const JACKPOT_URL = '/slot/jackpot';
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    /**
     * Get jackpot data
     */
    public function getJackpot(Request $request): array
    {
        $response = $this->gatewayApi->get(
            endpoint: self::JACKPOT_URL,
            cookies: ['lang' => $request->lang],
        );
        if (isset($response->code) && $response->code === Response::HTTP_OK) {
            return json_decode(json_encode($response->data), true) ?? [];
        }

        return [];
    }

    /**
     * Map jackpot data to games
     */
    public function mapJackpotToGames(array $games, array $jackpots): array
    {
        $jackpotMap = collect($jackpots)->toArray();

        return collect($games)->map(function ($game) use ($jackpotMap) {
            $gameId = in_array($game->partner_provider, config('games.jackpotPartner'))
                ? $game->partner_provider . '_' . $game->partner_game_id
                : $game->partner_game_id;

            $game->jackpot = $jackpotMap[$gameId] ?? 0;

            if ($gameId === "") {
                $game->jackpot = number_format($game->jackpot, 0, ',', '.');
            }
            return $game;
        })->toArray();
    }

    /**
     * Get jackpot game
     */
    public function getGameByParams($request, $params = [], $endpoint = self::SEARCH_GAME_URL)
    {
        $response = $this->gatewayApi->get(
            endpoint: $endpoint,
            queryparams: $params,
            cookies: ['lang' => $request->lang],
        );

        if (isset($response->code) && $response->code === Response::HTTP_OK) {
            return $response->data;
        }

        return (object)[
            'items' => [],
            'limit' => 20,
            'page' => 1,
            'total' => 0,
            'totalPage' => 0
        ];
    }

    public function getSportUrl($request, $endpoint, $params = [])
    {
        $userCookie = $request->cookie('user');
        $response = $this->gatewayApi->get(
            endpoint: $endpoint,
            queryparams: $params,
            cookies: ['lang' => $request->lang, 'user' => $userCookie],
        );
        if (isset($response->code) && $response->code === Response::HTTP_OK && isset($response->data->url)) {
            // add loginUrl & registerUrl to url
            $url = $response->data->url ?? '';
            $parsedUrl = parse_url($url);
            parse_str($parsedUrl['query'] ?? '', $queryParams);
            // $currentDomain = $request->getSchemeAndHttpHost();
            // $queryParams['loginUrl'] = $currentDomain . '?modal=login';
            // $queryParams['registerUrl'] = $currentDomain . '?modal=register';
            $parsedUrl['query'] = http_build_query($queryParams);

            $scheme = $parsedUrl['scheme'] ?? 'https';
            $host = $parsedUrl['host'] ?? '';
            $path = $parsedUrl['path'] ?? '';
            $query = $parsedUrl['query'] ?? '';

            $url = $scheme . '://' . $host . $path . ($query ? '?' . $query : '');
            return $url;
        }

        return '';
    }

    public function getRecommendGames(Request $request, $limit = 6)
    {
        $queryparams = [
            'type' => 'all',
            'page' => 1,
            'sort' => 'hot',
            'limit' => $limit
        ];
        $response = $this->gatewayApi->get(
            endpoint: GatewayEndpoint::GAME_SEARCH->value,
            queryparams: $queryparams,
            headers: $request->headers->all(),
            cookies: ['lang' => $request->lang]
        );

        if (!empty($response->data->items)) {
            $items = $response->data->items;
            shuffle($items);
            return $items;
        }

        return [];
    }

    public function getMultipleGameByParams($request, array $requests, $baseEndpoint = self::SEARCH_GAME_URL)
    {
        // Map requests to include endpoint and queryparams
        $formattedRequests = array_map(function ($req) use ($baseEndpoint) {
            return [
                'endpoint' => $req['endpoint'] ?? $baseEndpoint,
                'queryparams' => $req['params'] ?? [],
            ];
        }, $requests);

        $responses = $this->gatewayApi->getMultiple(
            requests: $formattedRequests,
            cookies: ['lang' => $request->lang]
        );
        $data = array_map(function ($response) {
            if (isset($response->code) && $response->code === Response::HTTP_OK) {
                return $response->data ?? [];
            }
            return [];
        }, $responses);

        return $data;
    }
public function getVesocaoUrl(Request $request)
    {
        $response = $this->gatewayApi->get(
            endpoint: UrlPathEnum::VESOCAO_URL->value,
        );
        if (isset($response->code) && $response->code === Response::HTTP_OK) {
            return $response->data;
        }

        return [];
    }
}
