<?php

declare(strict_types=1);

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Jen<PERSON><PERSON>\Agent\Agent;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Log;

class AuthService
{
    private GatewayApi $gatewayApi;

    public const LOGIN_API_URL = '/login';

    public const LOGIN_USER_API_URL = '/login';

    public const REGISTER_API_URL = '/register';

    public const COOKIE_USER_TEXT = 'user';

    public const TIME_COOKIE = 1440;

    public const TOP_WIN_LIMIT_DISPLAY = 10;

    public const RESET_PASSWORD_BY_OTP_API_URL = '/user/resetPasswordByOtp';

    public const UPDATE_USER_FULLNAME_API_URL = '/updateInfo';

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    /**
     * Get jackpot game
     *
     * @param $request data
     */
    public function checkLogin($request)
    {
        $dataRq = $request->all();
        $passHeaders = $this->getDeviceInfo();
        $headersAll = $request->headers->all();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $response = $this->gatewayApi->post(
            endpoint: self::LOGIN_API_URL,
            data: [...$dataRq, ...$this->getDeviceInfo()],
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );

        return $this->handleReponse($response);
    }

    /**
     * Handle register
     *
     * @param $request data
     */
    public function register($request)
    {
        $dataRq = $request->all();
        $passHeaders = $this->getDeviceInfo();
        $headersAll = $request->headers->all();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $response = $this->gatewayApi->post(
            endpoint: self::REGISTER_API_URL,
            data: [...$dataRq, ...$this->getDeviceInfo()],
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );
        return $this->handleReponse($response);

    }

    public function loginWithToken(Request $request, string $token)
    {
        $trackingParams = [
            'a' => $request->query('a'),
            'pxl' => $request->query('pxl'),
            'zoneid' => $request->query('zoneid'),
            'aff_id' => $request->query('aff_id'),
            'querystring' => $request->query('querystring'),
            'utm_source' => $request->query('utm_source'),
            'utm_medium' => $request->query('utm_medium'),
            'utm_campaign' => $request->query('utm_campaign'),
            'utm_term' => $request->query('utm_term'),
            'utm_content' => $request->query('utm_content'),
        ];

        $trackingParams = array_filter($trackingParams);

        Log::info("Login with token: $token and tracking params:", $trackingParams);

        $response = $this->gatewayApi->get(
            endpoint: self::LOGIN_USER_API_URL,
            queryparams: array_merge(['token' => $token, 'off_redirect' => 1], $trackingParams),
            headers: [],
            cookies: [],
        );

        return $this->handleReponse($response);
    }

    public function resetPasswordByOtp($request)
    {
        $dataRq = $request->except(['isMobile', 'lang']);
        $passHeaders = [];
        $headersAll = $request->headers->all();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $response = $this->gatewayApi->post(
            endpoint: self::RESET_PASSWORD_BY_OTP_API_URL,
            data: $dataRq,
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );
        return $this->handleReponse($response);
    }

    public function updateUserFullname($request)
    {
        $dataRq = $request->all();
        $passHeaders = [];
        $headersAll = $request->headers->all();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $response = $this->gatewayApi->post(
            endpoint: self::UPDATE_USER_FULLNAME_API_URL,
            data: $dataRq,
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );
        return $this->handleReponse($response);
    }

    /**
     * Handle response auth
     *
     * @param $response
     */
    private function handleReponse($response)
    {
        try {
            $result['message'] = !empty($response->message) ? $response->message : '';
            if (!empty($response->code) && $response->code == Response::HTTP_OK) {
                $cookie = cookie(self::COOKIE_USER_TEXT, json_encode($response->data[0]), self::TIME_COOKIE, null, null, false, false);
                $result['cookie'] = $cookie; // keep response structure consistent
                $result['data'] = !empty($response->data) ? $response->data[0] : '';
                $result['status'] = $response->code == Response::HTTP_OK ? 'OK' : false;
            }
            return $result;
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'message' => 'Đã có lỗi xảy ra, vui lòng thử lại!',
                'error' => $e->getMessage()
            ]);
        }
    }
    private function getDeviceInfo()
    {
        $agent = new Agent();
        $os = $agent->platform();
        $browser = $agent->browser();
        $device = $agent->device();

        return [
            'ip' => request()->ip(),
            'os' => $os,
            'browser' => $browser,
            'device' => $device,
        ];
    }
    public function updateCookie($user)
    {
        $allowedKeys = config('user.all_user_cookie');
        $user = array_intersect_key((array) $user, array_flip($allowedKeys));
        $cookie = cookie(
            name: self::COOKIE_USER_TEXT,
            value: json_encode($user),
            minutes: self::TIME_COOKIE,
            path: null,
            domain: null,
            secure: true,
            httpOnly: true,
            raw: false,
            sameSite: 'Lax'
        );
        return $cookie;
    }
    public function filterCookie($user)
    {
        $allUserCookie = config('user.all_user_cookie');
        $privateUserCookie = config('user.private_user_cookie');

        $allowedKeys = array_diff($allUserCookie, $privateUserCookie);

        $user = array_intersect_key((array) $user, array_flip($allowedKeys));
        return $user;
    }
}
