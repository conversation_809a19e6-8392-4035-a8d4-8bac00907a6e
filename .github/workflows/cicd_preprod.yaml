name: Build and Deploy to Preprod

on:
  workflow_dispatch:
  push:
    branches: [preprod]
    paths-ignore:
      - '.github/workflows/**'

permissions:
  id-token: write
  contents: read

jobs:
  preprod:
    uses: Seatincorp/devops-actions-s2/.github/workflows/docker-build.yml@main
    with:
      environment: preprod
      app-name: fe-fo88
      project: s2
      frps-port: 7024
      eks-cluster: devops-st002-prod-eks
      eks-cluster-region: ap-east-1
      role-to-assume: arn:aws:iam::339713046864:role/devops-gh-action-s2
      deployment-role-to-assume: arn:aws:iam::614673033037:role/devops-gh-action-st002
      role-session-name: devops-gh-action-s2
      aws-region: ap-southeast-1
      build-env: true
    secrets: inherit
