name: Notify PR to Tel<PERSON><PERSON>

on:
  pull_request:
    types: [opened]

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Send PR link to Telegram
        run: |
          curl --location 'https://api.telegram.org/bot7442308210:AAH2_8_Zo2-CnA7UQw2xZjJ9m9ERl_IKIuc/sendMessage' \
            -H "Content-Type: application/json" \
            -d '{
              "chat_id": "-4753083530",
              "text": "<b>🔔 Pull Request mới</b>\n<b>Title:</b> ${{
                github.event.pull_request.title }}\n<b>Author:</b> @${{
                github.actor }}\n👉 <a href=\"${{ github.event.pull_request.html_url }}\">View PR</a>\n
                <b>Full Link:</b> <a href=\"${{ github.event.pull_request.html_url }}\">${{ github.event.pull_request.html_url }}</a>\n
                <b>cc:</b> @dev_duanyu_007",
              "parse_mode": "HTML"
            }'
