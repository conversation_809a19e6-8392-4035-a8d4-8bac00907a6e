const liveStreamUrl = import.meta.env.VITE_LIVE_STREAM_URL;
const defaultUrl = import.meta.env.VITE_LIVE_STREAM_DEFAULT_URL;
const defaultWs = import.meta.env.VITE_LIVE_STREAM_DEFAULT_WS;
const defaultHls = import.meta.env.VITE_LIVE_STREAM_DEFAULT_HLS;
const MAX_RETRY = 3;
const defaultServer = {
  websocket: defaultWs,
  hls: defaultHls,
}

const isMute = true;
const showIP = true;
const enableSound = null;
let ip = '';
let countRetry = 0;

var obPlayers = {};

const getElementById = (id) => {
  const element = document.getElementById(id)
  return element ? element : false
}

const preventTouchMove = (element) => {
  if (element) {
    element.ontouchmove = (event) => event.preventDefault()
  }
}

const handleToggleSound = (divId) => {
    // if (!obPlayers[divId]?.player) {
    //     console.warn('Player chưa được khởi tạo');
    //     return;
    // }
    const cleanDivId = divId.replace('_mobileLiveStreamItem', '');
    const soundIcon = $(`div[data-key="${cleanDivId}"]`).find(".js-toggle-sound i");
    const isMuted = soundIcon.hasClass("icon-volume");

    try {
        if (isMuted) {
            obPlayers[divId]?.player?.mute();
            soundIcon.removeClass("icon-volume").addClass("icon-volume-mute");
        } else {
            obPlayers[divId]?.player?.unmute();
            soundIcon.removeClass("icon-volume-mute").addClass("icon-volume");
        }
    } catch (error) {
        console.error('Lỗi khi điều chỉnh âm thanh:', error);
    }
};

function setupPlayer(config, divId, streamName) {
    config.source.entries[0].h5live.rtmp.streamname = streamName;
    const player = new window.NanoPlayer(divId);
    player.setup(config).then(
        () => {
            if (!obPlayers[`${divId}`]) {
                obPlayers[`${divId}`] = {};
            }
            obPlayers[`${divId}`] = {
                player,
                state: player.state,
                isMuted: true,
                isErrored: null,
                isLoading: null,
                isPlaying: null,
                isPaused: null,
            };
            if (!config?.playback?.autoplay) {
                obPlayers[`${divId}`].isPaused = true;
            }
        },
        (error) => {
            console.error(error);
            obPlayers[`${divId}`] = {};
        }
    );

    const cleanDivId = divId.replace('_mobileLiveStreamItem', '');
    const toggleSoundButton = $(`div[data-key="${cleanDivId}"]`).find(".js-toggle-sound");
    toggleSoundButton.off("click");
    toggleSoundButton.on("click", function (event) {
        event.stopPropagation();
        event.preventDefault();
        handleToggleSound(divId);
    });
}

async function fetchStreamData(
  url,
  payload,
) {
  return new Promise((resolve, reject) => {
    const xhttp = new XMLHttpRequest()
    xhttp.open('POST', url, true)
    xhttp.setRequestHeader('Content-type', 'application/json')
    xhttp.onreadystatechange = function () {
      if (this.readyState === 4) {
        if (this.status === 200) {
          resolve(JSON.parse(this.response))
        }
        else {
          reject(new Error('Failed to fetch stream data'))
        }
      }
    }
    xhttp.onerror = reject
    xhttp.ontimeout = reject
    xhttp.send(JSON.stringify(payload))
  })
}

async function verifyToken({
  divId,
  groupId,
  streamName,
  site,
  options = {},
}) {
  const config = {
    source: {
      entries: [
        {
          h5live: {
            server: defaultServer,
            rtmp: { url: defaultUrl, streamname: '' },
            security: { jwtoken: '' },
          },
        },
      ],
    },
    playback: {
      autoplay: true,
      automute: true,
      muted: true,
      faststart: true,
      latencyControlMode: 'balancedadaptive',
    },
    style: {
      displayMutedAutoplay: false,
      controls: false,
      fullScreenControl: false,
      interactive: true,
      poster: options.style?.poster,
    },
    events: {
      onLoading: () => {
        if (obPlayers[`${divId}`]) {
          obPlayers[`${divId}`] = { ...obPlayers[`${divId}`], isLoading: true, state: 4, isPaused: false }
        }
      },
      onReady: () => {
        const middleViewEl = getElementById(`middleView-${divId}`)
        preventTouchMove(middleViewEl)
        middleViewEl.style.display = 'none'
        if (obPlayers[`${divId}`]) {
          obPlayers[`${divId}`] = { ...obPlayers[`${divId}`], isMuted: true, state: 3, isLoading: false }
        }
      },
      onPlay: () => {
        if (isMute && (!enableSound || enableSound === 'true')) {
          const soundoff = getElementById('soundoff')
          if (soundoff)
            soundoff.click()
        }
        obPlayers[`${divId}`] = { ...obPlayers[`${divId}`], isPlaying: true, state: 5, isLoading: false, isPaused: false }
      },
      onMute: () => {
        if (obPlayers[`${divId}`]) {
          obPlayers[`${divId}`] = { ...obPlayers[`${divId}`], isMuted: true }
        }
      },
      onUnmute: () => {
        if (obPlayers[`${divId}`]) {
          obPlayers[`${divId}`] = { ...obPlayers[`${divId}`], isMuted: false }
        }
      },
      onPause: () => {
        if (obPlayers[`${divId}`]) {
          obPlayers[`${divId}`] = { ...obPlayers[`${divId}`], isPlaying: false, isPaused: true, state: 11, isLoading: false }
        }
      },
      onError: (error) => {
        if (obPlayers[`${divId}`] && countRetry < MAX_RETRY) {
          obPlayers[`${divId}`].player.destroy()
          delete obPlayers[`${divId}`]
          retryVerifyToken({ divId, groupId, streamName, site, options })
          const playerElement = getElementById(divId)
          playerElement.style.backgroundImage = `url(${options?.style?.poster || '/asset/images/game-default.png'})`
          playerElement.style.backgroundSize = 'cover'
        }
      },
      onWebSocketError: (event) => {
        console.error('WebSocket error occurred:', event)
        retryVerifyToken({ divId, groupId, streamName, site, options })
      },
    },
    // ...options,
  }

  try {
    const result = await fetchStreamData(
      liveStreamUrl,
      { groupId, streamId: streamName, site },
    )
    const data = result.data
    if (data?.token) {
      config.source.entries[0].h5live.security.jwtoken = data.token
    }

    setupPlayer(config, divId, streamName)

    ip = result.ip
    if (ip && showIP) {
      const ipElement = getElementById('ipId')
      if (ipElement) {
        ipElement.textContent = ip
      }
    }
  }
  catch (error) {
    console.error('Verification failed, retrying...', error)
    retryVerifyToken({ divId, groupId, streamName, site, options })
  }
}

function retryVerifyToken(params) {
  if (++countRetry <= MAX_RETRY) {
    verifyToken(params)
  }
}

// const toggleSound = (mute) => {
//   if (player) {
//     mute ? player.mute() : player.unmute()
//   }
// }

const updateSoundUI = (isSoundOn) => {
  const soundOffEl = getElementById('soundoff')
  const soundOnEl = getElementById('soundon')
  if (soundOffEl && soundOnEl) {
    soundOffEl.style.display = isSoundOn ? 'none' : 'block'
    soundOnEl.style.display = isSoundOn ? 'block' : 'none'
  }
}

const loadNanoPlayer = () => {
  if (typeof window === 'undefined' || window.NanoPlayer) {
    return Promise.resolve(window?.NanoPlayer)
  }

  return new Promise((resolve, reject) => {
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = '/assets/js/nanoplayer.4.min.js'
    script.onload = () => resolve(window.NanoPlayer)
    script.onerror = () => reject(new Error('Failed to load NanoPlayer script.'))
    document.head.appendChild(script)
  })
}

window.verifyToken = verifyToken
// window.soundOn = toggleSound(false)
// window.soundOff = toggleSound(true)
window.setSoundOnEl = updateSoundUI(true)
window.setSoundOffEl = updateSoundUI(false)
window.hideSoundEl = updateSoundUI(false)
window.loadNanoPlayer = loadNanoPlayer
window.obPlayers = obPlayers