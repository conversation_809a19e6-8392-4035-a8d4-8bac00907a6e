import { openConfirmPromoModal } from "../promotion";

// Handle NicePay data and status checking
const handleNicePay = (nicepayData, step) => {
    localStorage.removeItem("isDepositSuccessful");
    if (!nicepayData || step !== 2) return;
    let isRequesting = false;

    const interval = setInterval(async () => {
        if (isRequesting) return;

        try {
            isRequesting = true;
            const data = await fetchData(
                "/account/deposit/codepay/info",
                {},
                {},
                "",
                ""
            );

            if (data.data?.deposited) {
                localStorage.setItem("isDepositSuccessful", "true");
                let countdown = 4;
                $(
                    ".js-countdown-timer-codepay .js-countdown-progress-codepay"
                ).remove();
                $(".js-countdown-timer-codepay-success").removeClass("hidden");

                const countdownInterval = setInterval(async () => {
                    $(".step-2-icon-uncheck").addClass("hidden");
                    $(".step-2-icon-check").removeClass("hidden");
                    $(".js-step-codepay-three .step-3-icon-check").removeClass(
                        "hidden"
                    );
                    $(".js-step-codepay-three .step-3-icon-uncheck").addClass(
                        "hidden"
                    );
                    $(".js-step-codepay-three span").addClass("text-white");
                    $(".js-progress-step-three")
                        .addClass("border-account-text-brand-primary")
                        .removeClass("border-account-surface-quaternary");
                    $(".js-countdown-timer-codepay-success span").text(
                        `Nạp tiền thành công (${countdown}s)`
                    );

                    $(".js-qr-button").prop("disabled", false);
                    countdown--;
                    if (countdown <= 0) {
                        clearInterval(countdownInterval);
                        clearInterval(interval);
                        try {
                            await fetchData(
                                "/account/deposit/codepay/success",
                                {},
                                {},
                                "",
                                ""
                            );
                        } catch (error) {
                            console.error("Error fetching success:", error);
                        }
                        window.location.reload();
                    }
                }, 1000);
            }
        } finally {
            isRequesting = false;
        }
    }, 5000);
};

// Initialize deposit form functionality
const initDepositForm = () => {
    const $form = $("#deposit-codepay-form");

    // Validate form
    const formValidator = $form.validate({
        rules: {
            amount: {
                required: true,
                number: true,
                min: 50,
                max: 500000,
            },
            packageId: {
                required: true,
            },
        },
        messages: {
            amount: {
                required: "Vui lòng nhập số tiền nạp",
                number: "Vui lòng nhập số tiền hợp lệ",
                min: "Giá trị không hợp lệ. Vui lòng nhập từ 50K tới 500 triệu",
                max: "Giá trị không hợp lệ. Vui lòng nhập từ 50K tới 500 triệu",
            },
            packageId: {
                required: "Vui lòng chọn gói khuyến mãi",
            },
        },
        errorElement: "span",
        errorClass: "form-input-error-custom",
        errorPlacement: function (error, element) {
            element?.closest(".form__field")?.append(error);
        },
        submitHandler: function (form) {
            if (isWelcomePromotion) {
                openConfirmPromoModal();
            } else {
                const $form = $(form);
                const $submitBtn = $form.find(".js-submit-button");

                $submitBtn.prop("disabled", true).addClass("is-loading");

                $.ajax({
                    url: $form.attr("action"),
                    type: "POST",
                    data: $form.serialize(),
                    success: function (response) {
                        if (response.status === "OK") {
                            window.location.reload();
                        } else {
                            showErrorModal(
                                response.message ||
                                    "Đã có lỗi trong quá trình giao dịch, vui lòng thử lại giao dịch khác"
                            );
                            $submitBtn
                                .prop("disabled", false)
                                .removeClass("is-loading");
                        }
                    },
                    error: function (xhr) {
                        showErrorModal(
                            xhr?.responseJSON?.message ||
                                "Đã có lỗi trong quá trình giao dịch, vui lòng thử lại giao dịch khác"
                        );
                        $submitBtn
                            .prop("disabled", false)
                            .removeClass("is-loading");
                    },
                });
            }
        },
        invalidHandler: function (form, validator) {
            $("#form-submit-step1").prop("disabled", true);
        },
    });

    $form.find("input, select").on("change keyup input-paste", function () {
        if ($form.valid()) {
            $("#form-submit-step1").prop("disabled", false);
        } else {
            $("#form-submit-step1").prop("disabled", true);
        }
    });

    // Amount button clicks
    $(".js-amount-item").on("click", function () {
        const amount = $(this).data("amount");
        $("#amount-input").val(amount).trigger("change");
        $(".js-amount-item").removeClass("active");
        $(this).addClass("active");
    });

    // Xử lý input thay đổi
    $("#amount-input").on("input", function () {
        const inputValue = $(this).val().replace(/[,.]/g, "");
        $(".js-amount-item").each(function () {
            const btnAmount = String($(this).data("amount")).replace(
                /[,.]/g,
                ""
            );

            $(this).toggleClass("active", inputValue === btnAmount);
        });
    });
};

// Timer functionality
const startTimer = (expiryTime) => {
    if (!expiryTime) return;

    const timer = setInterval(() => {
        const now = new Date().getTime();
        const expiry = new Date(expiryTime).getTime();
        const diff = expiry - now;
        if (diff <= 0) {
            $(".js-countdown-timer").text("00:00");
            clearInterval(timer);
            window.location.reload();
            return;
        }

        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        $(".js-countdown-timer").toggleClass("!text-alert-failed", minutes < 5);

        $(".js-countdown-timer").text(
            `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(
                2,
                "0"
            )}`
        );
    }, 1000);
};

function initPackageSelection() {
    const $packageRadios = $(".package-radio");
    const $bonusAmount = $(".js-packages-content .js-package-bonus");
    const $totalAmount = $(".js-packages-content .js-package-amount");
    const $multiplier = $(".js-packages-content .js-package-multiplier");
    const $requiredAmount = $(
        ".js-packages-content .js-package-required-amount"
    );
    const $amountInput = $("#amount-input");

    function updatePackageContent($radio) {
        if (
            !$radio.length ||
            !$bonusAmount.length ||
            !$totalAmount.length ||
            !$multiplier.length ||
            !$requiredAmount.length
        )
            return;

        const deposit = parseInt(
            $amountInput.val()?.replace(/,/g, "") * 1000 || "0"
        );
        const bonus = parseInt(
            $radio.data("bonus")?.toString()?.replace(/,/g, "") || "0"
        );
        const percent = parseFloat($radio.data("percent") || "0");
        const multiplierValue = parseInt($radio.data("multiplier") || "0");
        const maxAmount = parseInt($radio.data("max-amount") || "0");

        // Tính toán tiền thưởng dựa trên phần trăm và số tiền cố định
        const percentBonus = Math.floor(deposit * percent);
        const totalBonus = bonus + percentBonus;

        const maxBonusAmount = Math.min(maxAmount, totalBonus);
        const maxTotalAmount = deposit + maxBonusAmount;
        let maxRequiredAmount;
        if (percentBonus <= maxAmount) {
            maxRequiredAmount = maxTotalAmount * multiplierValue
        } else {
            maxRequiredAmount = (maxAmount / percent + maxAmount) * multiplierValue + (deposit - (maxAmount / percent));
        }

        $bonusAmount.text(formatNumber(maxBonusAmount) + " VND");
        $totalAmount.text(formatNumber(maxTotalAmount) + " VND");
        $multiplier.text($radio.data("multiplier") + " Vòng");
        $requiredAmount.text(formatNumber((maxRequiredAmount / 1000), true) + " "+ currency);
    }

    function formatNumber(num) {
        if (isNaN(num)) return 0;
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    $amountInput.on("change input", function () {
        const $selectedPackage = $(".package-radio:checked");
        if ($selectedPackage.length) {
            updatePackageContent($selectedPackage);
        }
    });

    const $defaultPackage = $(".package-radio:checked");
    if ($defaultPackage.length) {
        updatePackageContent($defaultPackage);
    }

    $packageRadios.on("change", function () {
        const parent = $(this).closest(".account-active-button");
        $(".deposit-codepay-packages .account-active-button").removeClass(
            "active"
        );
        parent.addClass("active");
        $(".js-packages-content").toggleClass(
            "hidden",
            Number($(this).val()) === 1
        );
        updatePackageContent($(this));
    });
}

$(document).ready(function () {
    initPackageSelection();
});

// Hàm khởi tạo chung
const initializeCodePay = (nicepayData, step, expiryTimeUtc, createdTime) => {
    handleNicePay(nicepayData, step);
    initDepositForm();

    if (expiryTimeUtc) {
        startTimer(expiryTimeUtc);
    }
};

// Export cho window object
window.initCodePay = initializeCodePay;

// Khởi tạo tự động nếu có config
if (window.codepayConfig) {
    const { nicepayData, step, expiryTimeUtc, createdTime } =
        window.codepayConfig;
    initializeCodePay(nicepayData, step, expiryTimeUtc, createdTime);
}

$(function () {
    $(".packages__item").on("click", function () {
        const MARGIN_LEFT = 16;
        const $container = $(".js-packages");
        const $item = $(this);
        const itemOffset = $item.offset().left - $container.offset().left;

        $container.animate(
            {
                scrollLeft: $container.scrollLeft() + itemOffset - MARGIN_LEFT,
            },
            300
        );
    });

    $("#form-submit-cancel").on("click", function () {
        openCodepayConfirmModal();
    });
});

function openCodepayConfirmModal() {
    // Check nếu đã nạp thành công thì không hiển thị modal này
    const isDeposited = localStorage.getItem("isDepositSuccessful") === "true";
    if (isDeposited) {
        window.location.reload();
        return;
    }
    const codepayPostID = document.querySelector(".js-codepayPostID").innerText;
    window.dispatchEvent(
        new CustomEvent("swal:confirm", {
            detail: {
                title: "Cảnh báo",
                html: `Phiếu ${codepayPostID} sẽ bị hủy, bạn có muốn tiếp tục?`,
                icon: "success",
                confirmButtonText: "Đồng Ý",
                customClass: {
                    icon: "transaction-confirm",
                },
                cancelButtonText: "Từ Chối",
                cancelButtonFunc: async () => {},
                confirmButtonFunc: async () => {
                    const $form = $("#deposit-codepay-form");
                    const $submitBtn = $form.find("#form-submit-cancel");

                    $submitBtn.prop("disabled", true).addClass("is-loading");

                    $.ajax({
                        url: $form.attr("action"),
                        type: "POST",
                        data: $form.serialize(),
                        success: function (response) {
                            if (response.status === "OK") {
                                window.location.reload();
                            } else {
                                showErrorModal(
                                    response.message ||
                                        "Đã có lỗi trong quá trình giao dịch, vui lòng thử lại giao dịch khác"
                                );
                                $submitBtn
                                    .prop("disabled", false)
                                    .removeClass("is-loading");
                            }
                        },
                        error: function (xhr) {
                            showErrorModal(
                                "Đã có lỗi trong quá trình giao dịch, vui lòng thử lại giao dịch khác"
                            );
                            $submitBtn
                                .prop("disabled", false)
                                .removeClass("is-loading");
                        },
                    });
                },
                allowOutsideClick: false,
            },
        })
    );
}

const showErrorModal = (message) => {
    window.dispatchEvent(
        new CustomEvent("swal:confirm", {
            detail: {
                title: "Cảnh báo",
                html: message,
                icon: "success",
                confirmButtonText: "Liên hệ CSKH",
                customClass: { icon: "deposit-error" },
                cancelButtonText: "Thử lại",
                cancelButtonFunc: () => {
                    window.location.reload();
                },
                confirmButtonFunc: () => {
                    openLiveChat();
                },
                clickClose: () => {
                    window.location.reload();
                },
            },
        })
    );
};
