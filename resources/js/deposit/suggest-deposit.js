import { openConfirmDepositPromoModal } from "../promotion";
class SuggestDeposit {
    constructor() {
        this.init();
    }

    init() {
        $(() => {
            this.handleSuggestDepositClick();
            this.getParamsAndProcess();
        });
    }

    handleSuggestDepositClick() {
        $(".js-suggest-deposit-item").on("click", function () {
            const $this = $(this);
            const amount = $this.data("amount");
            const link = $this.data("link");
            const method = $this.data("method");
            const toBankCode = $this.data("to-bank-code");
            const baseUrl = "/account/deposit";

            $this.addClass('is-loading');

            if (method === "card") {
                window.location.href = `${baseUrl}/${link}?amount=${amount}&to_bank_code=${toBankCode}`;
            } else if (method === "codepay") {

                if (isWelcomePromotion) {
                    openConfirmDepositPromoModal();
                    $this.removeClass(
                        "is-loading pointer-events-none"
                    );
                } else {
                     // Call API to get nicepay data from cache
                     const fetchNicepayData = async () => {
                        const res = await fetchData(
                            "/get-nicepay-data",
                            {},
                            { ajax: true },
                            "",
                            ""
                        );
                        if (res?.success === true) {
                            const transactionId = res?.data?.id;
                            const invoiceId = res?.data?.invoice_id;
                            const currentTime = new Date().getTime();
                            const expiredTime = new Date(
                                res?.data?.expired_at
                            ).getTime();

                            const isExpired = expiredTime
                                ? expiredTime < currentTime
                                : true;

                            if (!isExpired) {
                                const cancelResult =
                                    await showCancelDepositModal({
                                        transactionId,
                                        invoiceId,
                                    });

                                if (!cancelResult) {
                                    $this.removeClass(
                                        "is-loading pointer-events-none"
                                    );
                                    return;
                                }
                            }

                            const payload = {
                                amount: amount,
                                packageId: defaultPackageId,
                            };
                            const resDepositCodepay = await submitData(
                                "/account/deposit/codepay",
                                payload,
                                "",
                                ""
                            );
                            $this.removeClass("is-loading pointer-events-none");

                            if (resDepositCodepay?.status === "OK") {
                                window.location.href =
                                    "/account/deposit/codepay";
                            } else {
                                showErrorModal(
                                    resDepositCodepay.message ||
                                        "Đã có lỗi trong quá trình giao dịch, vui lòng thử lại giao dịch khác"
                                );
                            }
                        } else {
                            $this.removeClass("is-loading pointer-events-none");
                        }
                    };
                    fetchNicepayData();
                }
            } else if(method === "cryptopay"){
                window.location.href = `${baseUrl}/${link}?amount=${amount}&to_bank_code=${toBankCode}`;
            } else {
                window.location.href = `${baseUrl}/${link}?amount=${amount}&to_bank_code=${toBankCode}`;
            }
        });
    }

    getParamsAndProcess() {
        const urlParams = new URLSearchParams(window.location.search);
        const amount = urlParams.get("amount");
        const toBankCode = urlParams.get("to_bank_code");
        const currentPath = window.location.pathname;

        if(currentPath.includes('/cryptopay')) {
            $('.js-crypto-currency-option').each(function () {
                if($(this).find('input').val() === toBankCode){
                    $(this).click();
                }
            })
        }
    }
}

new SuggestDeposit();

const showCancelDepositModal = ({ transactionId, invoiceId }) => {
    return new Promise((resolve) => {
        window.dispatchEvent(
            new CustomEvent("swal:confirm", {
                detail: {
                    title: "Cảnh báo",
                    html: `Phiếu #${invoiceId} sẽ bị huỷ, bạn có muốn tiếp tục?`,
                    icon: "success",
                    confirmButtonText: "Đồng ý",
                    customClass: { icon: "transaction-confirm" },
                    cancelButtonText: "Từ chối",
                    cancelButtonFunc: () => {
                        resolve(false);
                    },
                    confirmButtonFunc: async () => {
                        const res = await submitData(
                            "/account/deposit/codepay/cancel",
                            {
                                nicepay_id: transactionId,
                            }
                        );
                        if (res.status === "OK") {
                            await fetchData("/clear-nicepay-data", {}, "", "");
                            resolve(true);
                        } else {
                            showErrorModal(
                                res.message ||
                                    "Đã có lỗi trong quá trình giao dịch, vui lòng thử lại giao dịch khác"
                            );
                            resolve(false);
                        }
                    },
                    closePopupFunc: () => {
                        resolve(false);
                    }
                },
            })
        );
    });
};

const showErrorModal = (message) => {
    window.dispatchEvent(
        new CustomEvent("swal:confirm", {
            detail: {
                title: 'Cảnh báo',
                html: message,
                icon: 'success',
                confirmButtonText: 'Đóng',
                customClass: { icon: 'deposit-error' },
                cancelButtonText: 'Đồng ý',
                cancelButtonFunc: () => {
                    window.location.reload();
                },
                confirmButtonFunc: () => {
                    window.location.reload();
                }
            }
        })
    );
};

export const refreshSuggestDeposit = () => {
    $.ajax({
        url: '/account/suggest-deposit',
        type: 'GET',
        success: function(res) {
            if (res.length > 0) {
                $('.js-suggest-deposit-list').html('');
                res.forEach(item => {
                    const { amount, amountText, method, methodText, to_bank_code, filterMethod } = item;
                    const itemHtml = `
                        <div data-to-bank-code="${to_bank_code}"
                            data-link= "${filterMethod}" data-method="${method}"
                            data-amount="${amount / 1000}"
                            class="suggest-deposit-item js-suggest-deposit-item group relative flex cursor-pointer items-center gap-2 rounded-xl border border-[#23413A] bg-[#12312B] p-2 max-xl:w-[250px] max-xl:shrink-0 max-xl:py-[7px] xl:transition-all xl:hover:bg-[#23413A] xl:hover:shadow-[0px_4px_10.8px_0px_#48484D26]">
                            <img src="${'/asset/images/account/codepay/' + filterMethod +'.png'}"
                                class="size-10" alt="">
                            <div class="grow">
                                <p class="whitespace-nowrap text-[16px] leading-5 font-medium text-white">
                                ${amountText}
                                </p>
                                <p class="text-xs font-medium capitalize text-[#AFBBB7]">${methodText}</p>
                            </div>
                            <i
                                class="icon-chevron-right mr-1 grid size-6 shrink-0 place-content-center text-[22px] text-[#8C9D98]"></i>
                            <div
                                class="button__loading absolute inset-0 z-10 hidden w-full items-center justify-center rounded-lg bg-grey-500 bg-opacity-50 transition-opacity group-[.is-loading]:flex">
                                <img src="/asset/images/spinner.svg" class="size-6" />
                            </div>
                        </div>
                    `;
                    $('.js-suggest-deposit-list').append(itemHtml);
                });
                new SuggestDeposit();
            }
        },
        error: function(xhr) {
            showErrorModal(xhr?.responseJSON?.message || 'Có lỗi xảy ra. Vui lòng thử lại sau.');
        }
    });
}

window.refreshSuggestDeposit = refreshSuggestDeposit
