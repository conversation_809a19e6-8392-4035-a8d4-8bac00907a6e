document.addEventListener("DOMContentLoaded", function () {
    const $form = $("#withdraw-crypto-form");
    const $submitBtn = $form.find('#withdraw-crypto-btn');

    // Validate form
    if (!cryptoCurrencyList || !Array.isArray(cryptoCurrencyList) || cryptoCurrencyList.length === 0) {
        console.error('Không tìm thấy thông tin tiền tệ');
        return;
    }

    const defaultCurrencyMin = cryptoCurrencyList[0]['min']/1000 ?? 0;

    // Validate form
    const validator = $form.validate({
        rules: {
            amount: {
                required: true,
                number: true,
                min: defaultCurrencyMin,
                max: function (element) {
                    const currentBalance = $(element).attr("currentBalance");
                    return currentBalance;
                },
                normalizer: function (value) {
                    return value.replaceAll(",", "");
                },
            },
            phone: {
                required: true,
                digits: true,
                minlength: 5,
                maxlength: 5,
            },
            address: {
                required: true,
                alphanumeric: true,
            },
        },
        messages: {
            amount: {
                required: "<PERSON>ui lòng nhập số tiền rút",
                number: "<PERSON>ui lòng nhập số tiền hợp lệ",
                min: "Số tiền rút phải tối thiểu là " + defaultCurrencyMin + " "+ currency,
                max: "Không đủ số dư để thực hiện phiếu rút",
            },
            phone: {
                required: validationMessages.phone.required,
                minlength: validationMessages.phone.five_last,
            },
            address: {
                required: "Vui lòng nhập địa chỉ ví nhận",
                alphanumeric: "Địa chỉ ví không hợp lệ",
            },
        },
        errorElement: "span",
        errorClass: "form-input-error",
        errorPlacement: function (error, element) {
            element?.closest(".form__field")?.append(error);
        },
        submitHandler: function (form) {
            handleSubmitForm(form);
        },
        invalidHandler: function(form, validator) {
            $submitBtn.prop("disabled", true);
        },
    });

    $form.find("input, select").on("change keyup input-paste", function () {
        checkShowSubmit($form, $submitBtn);
    });
    const handleSubmitForm = async (form) => {
        try {
            $submitBtn.prop('disabled', true).addClass('is-loading');

            const formData = new FormData(form);
            const amount = formData.get('amount').replaceAll(',', '');

            const requestData = {
                address: formData.get('address'),
                amount: amount,
                amount_withdraw: Number(amount) * 1000,
                currency: $form.data('currency'),
                ex_rate: $form.data('ex-rate'),
                network: $form.data('network'),
                phone: formData.get('phone'),
                wallet_address: formData.get('address')
            };

            $.ajax({
                url: $(form).attr('action'),
                method: 'POST',
                data: requestData,
                success: function(response) {
                    if (response.status === 'OK') {
                        showSuccessModal()
                        resetForm()

                    } else {
                        showWithdrawErrorModal(response?.message || 'Đã có lỗi xảy ra, vui lòng thử lại');
                    }
                },
                error: function(xhr, status, error) {
                    showErrorModal(xhr?.responseJSON?.message || 'Đã có lỗi xảy ra, vui lòng thử lại');
                },
                complete: function() {
                    $submitBtn.prop('disabled', false).removeClass('is-loading');
                }
            });
        } catch (error) {
            console.error('Submit error:', error);
            showToast('error', 'Thất bại', 'Đã có lỗi xảy ra, vui lòng thử lại sau');
            $submitBtn.prop('disabled', false).removeClass('is-loading');
        }
    };


    // Change covert currency unit
    $('.js-crypto-currency-option input').on('change', handleCryptoCurrencyChange);

    function handleCryptoCurrencyChange() {
        const $this = $(this);
        const $amountInput = $('#amount-input');
        const currentValue = $amountInput.val();
        const currentCurrency = cryptoCurrencyList.find(item => item.network[0] === $this.val());
        const minAmount = (currentCurrency.min/1000 ?? 0).toLocaleString("en-US", {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        });

        // Change active currency option
        $(".js-crypto-currency-option").removeClass("active");
        $this.closest(".js-crypto-currency-option").addClass("active");

        // Set input data
        $('.js-input-cryptopay-number').data('rating', currentCurrency.price);
        $('.js-input-cryptopay-number').data('unit', currentCurrency.currency);
        $('.js-input-cryptopay-text').text(currentCurrency.currency);
        
        $('ul.list-disc > li').each(function(index) {
            const value = $(this).data('value');
            if (index !== 0 && !value.includes(currentCurrency.currency)) {
                $(this).addClass('hidden');
            } else {
                $(this).removeClass('hidden');
            }
        });

        // Set form data
        $form.data('ex-rate', currentCurrency.price);
        $form.data('currency', currentCurrency.currency);
        $form.data('network', currentCurrency.network[0]);

        // Kiểm tra và khởi tạo nếu cần thiết
        if (!$form.validate().settings.rules) {
            $form.validate().settings.rules = {};
        }
        if (!$form.validate().settings.rules.amount) {
            $form.validate().settings.rules.amount = {};
        }

        // Gán giá trị cho min
        $form.validate().settings.rules.amount.min = minAmount ?? 0;
        $form.validate().settings.messages.amount.min = `Số tiền rút phải tối thiểu là ${minAmount} K`;
        $form.validate().resetForm();

        if (currentValue) {
            $amountInput.trigger('change');
            $amountInput.val(currentValue);
            $form.validate().element($amountInput);
        }

    }

    const resetForm = () => {

        $submitBtn.prop("disabled", true).attr("loading", false);

        const firstActiveNetwork = $('.js-packages')
            .find(".js-crypto-currency-option").first();

        if (firstActiveNetwork.length) {
            firstActiveNetwork.trigger("click");
            $('#amount-input').val(0).trigger('input');
        }
        $form[0].reset();
        validator.resetForm();

    }
});


const showSuccessModal = (response) => {
    window.dispatchEvent(
        new CustomEvent("swal:confirm", {
            detail: {
                title: 'Tạo phiếu rút thành công',
                html: 'Kiểm tra lịch sử giao dịch của bạn',
                icon: 'success',
                confirmButtonText: 'Đóng',
                customClass: { icon: 'withdraw-success', confirmButton: '!hidden' },
                cancelButtonText: 'Xem lịch sử giao dịch',
                cancelButtonFunc: () => {
                    window.location.href = '/account/history?tab=transaction';
                },
                confirmButtonFunc: () => {}
            }
        })
    );
};
