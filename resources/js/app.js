import Swiper from "swiper";
import {
    Autoplay,
    Navigation,
    Pagination,
    FreeMode,
    Scrollbar,
    Grid,
} from "swiper/modules";
import "./bootstrap";
import "./common/validator-form";
import "./common/dropdown";
import "./common/input-amount";
import "./common/copy";
import "./validation";
import "./common/dropdown-bank";
import "./common/search-input";
import "./common/affiliate";
import "./common/add-bank";
import "./common/format-value";
import "./common/input";
import "./common/number";
import "./common/main";
import "./common/modal";
import "./socket";
import "./modal";
import "./live-stream";
import './common/broadcast';
import "./google-recaptcha";

Swiper.use([Autoplay, Navigation, Pagination, FreeMode, Scrollbar, Grid]);
window.Swiper = Swiper;

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "";
const apiVer = import.meta.env.VITE_API_VER;
function storeSafeUserData(data={}, allowedKeys = allowedCookieKeys, storageKey = 'userInfo') {
    const safeUserData = {};

    allowedKeys.forEach(key => {
        if (data.hasOwnProperty(key)) {
        safeUserData[key] = data[key];
        }
    });

    try {
        localStorage.setItem(storageKey, JSON.stringify(safeUserData));
    } catch (e) {
        console.error("Failed to store user data in localStorage:", e);
    }
}
window.storeSafeUserData = storeSafeUserData;

function getUserInfo(storageKey = 'userInfo') {
  const userInfo = {};
  try {
    // this should be parsed later
    const userData = localStorage.getItem(storageKey);
    if (userData) {
        userInfo.user = userData;
    }
    // userInfo.user is used for compatibility with old code
    return userInfo;
  } catch (e) {
    return {};
  }
}

function clearUserData(storageKey = 'userInfo') {
  try {
    localStorage.removeItem(storageKey);
  } catch (e) {
    console.warn("Failed to clear user data:", e);
  }
}
export function debounce(func, delay = 500) {
    let timeout;
    return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}
window.debounce = debounce;

export function getQueryParamsFromSearchParams(keys = []) {
    const urlParams = new URLSearchParams(window.location.search);

    if (keys.length > 0) {
        return keys.reduce(
            (queryParams, key) => ({
                ...queryParams,
                [key]: urlParams.get(key) ?? "",
            }),
            {}
        );
    }

    return {
        filter: urlParams.get("filter") ?? "",
        type: urlParams.get("type") ?? "",
        keyword: urlParams.get("keyword") ?? "",
        partner: urlParams.get("partner") ?? "",
    };
}

window.getQueryParamsFromSearchParams = getQueryParamsFromSearchParams;

export function pushState(newState = {}) {
    const activeFilter = {
        ...getQueryParamsFromSearchParams([
            "filter",
            "type",
            "keyword",
            "partner",
        ]),
        ...newState,
    };

    const url = new URL(location);

    Object.entries(activeFilter).forEach(([key, value]) => {
        url.searchParams.set(key, value);
    });

    window.history.pushState(
        {
            path: url.href,
            ...activeFilter,
        },
        "",
        url.href
    );

    window.dispatchEvent(
        new CustomEvent("pushstate", {
            detail: {
                path: url.href,
                state: activeFilter,
            },
        })
    );
}

window.pushState = pushState;

function getCookies() {
    if (!document.cookie) return {};

    return document.cookie.split(";").reduce((cookies, cookie) => {
        if (!cookie) return cookies;
        const [name, value] = cookie.split("=").map((c) => c.trim());
        return name ? { ...cookies, [name]: value } : cookies;
    }, {});
}

function deleteCookie(name) {
    clearUserData();
    document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
}

window.deleteCookie = deleteCookie;

async function updateCookie(name, value) {
    await cookieStore.set(name, value);
}

async function setIsMobileCookie() {
    if (window) {
        await updateCookie('isMobile', window.innerWidth < 1200);
    }
}

window.setIsMobileCookie = setIsMobileCookie;

window.addEventListener('resize', async () => {
    await setIsMobileCookie();
})

setIsMobileCookie();

export async function fetchData(url, params, options = {}) {
    try {
        const { useAjax = false, useProxy = true } = options;
        const baseUrl = useProxy ? apiBaseUrl + apiVer : apiBaseUrl;
        const query = new URLSearchParams(params).toString();
        const headers = {
            "Content-Type": "application/json",
            "X-Requested-With": "XMLHttpRequest",
        };

        const response = await fetch(
            `${baseUrl}${url}${query ? `?${query}` : ""}`,
            {
                method: "GET",
                credentials: "include",
                headers,
            }
        );

        const data = await response.json();

        // if (data.status !== "OK") {
        //     throw new Error(data.message || "API request failed");
        // }
        return data;
    } catch (error) {
        console.error("fetchData error:", error);
        throw error;
    }
}
window.fetchData = fetchData;

export async function submitData(url, params) {
    try {
        const baseUrl = apiBaseUrl + apiVer;

        const response = await fetch(`${baseUrl}${url}`, {
            method: "POST",
            credentials: "include",
            headers: {
                "Content-Type": "application/json",
                "X-Requested-With": "XMLHttpRequest",
            },
            body: JSON.stringify(params),
        });

        const data = await response.json();

        // if (typeof data?.status === "string" && data?.status !== "OK") {
        //     throw new Error(data.message || "API request failed");
        // }
        return data;
    } catch (error) {
        console.error("submitData error:", error);
        throw error;
    }
}
window.submitData = submitData;

export const loadExternalScript = (url, callback, type) => {
    const script = document.createElement("script");
    script.async = true;
    if (type) {
        script.type = type;
    }
    script.defer = true;
    script.src = url;
    script.crossorigin = "";
    if (callback) {
        script.addEventListener("load", callback, false);
    }
    if (document) {
        document.body.appendChild(script);
    }
};
window.loadExternalScript = loadExternalScript;

export function hideLoadingIframe() {
    document.querySelector(".loading-iframe")?.classList?.add("hidden");
    document.querySelector("#iframe-wrapper")?.classList?.remove("hidden");
}
window.hideLoadingIframe = hideLoadingIframe;

window.animateCounter = (element, oldValue, newValue, duration = 5000, gameId = '', prefix = '') => {
    if (!element || oldValue === undefined || newValue === undefined) {
        console.warn('Invalid parameters for animateCounter');
        return;
    }
    oldValue = parseFloat(oldValue.toString().replace(/,/g, "")) || 0;
    newValue = parseFloat(newValue.toString().replace(/,/g, "")) || 0;

    if ($(element).is(':animated') && $(element).data('target') === newValue) {
        return;
    }

    const difference = Math.abs(newValue - oldValue);
    if (difference < 10) {
        $(element).html(`${formatAmount(newValue)} ${prefix || ($(element).data('prefix') || '')}`);
        return;
    }

    $(element).stop(true, true).finish();

    const distance = Math.abs(newValue - oldValue);
    const length = distance.toString().length;

    const numberOfSteps = length > 1 ? Math.pow(10, length - 1) : 1;
    const stepSize = Math.ceil(distance / numberOfSteps);

    if (prefix) {
        $(element).data('prefix', prefix);
    }

    $(element).data('target', newValue);

    $(element)
        .prop("Counter", oldValue)
        .animate(
            {
                Counter: newValue,
            },
            {
                duration: duration,
                easing: 'linear',
                step: function (now, fx) {
                    if (fx.pos > 0 && fx.pos < 1) {
                        const delay = 100;
                        const lastUpdate = $(this).data("lastUpdate") || 0;
                        const currentTime = Date.now();

                        if (currentTime - lastUpdate < delay) {
                            return;
                        }
                        $(this).data("lastUpdate", currentTime);
                    }

                    const stepValue = Math.floor(now / stepSize) * stepSize;
                    const displayValue = stepValue < newValue ? stepValue : newValue;
                    const formattedValue = formatAmount(displayValue || 0);
                    const displayPrefix = prefix || $(this).data('prefix') || '';

                    $(this).html(`${formattedValue} ${displayPrefix}`);
                },
                complete: function() {
                    $(this).html(`${formatAmount(newValue)} ${prefix || $(this).data('prefix') || ''}`);
                    $(this).removeData("lastUpdate");
                }
            }
        );
};

// Format amount helper function
window.formatAmount = (value) => {
    if (!value) return "";
    const numbers = value.toString().replace(/\D/g, "");
    return numbers.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

const showWithdrawErrorModal = (message) => {
    window.dispatchEvent(
        new CustomEvent("swal:confirm", {
            detail: {
                title: "TẠO PHIẾU RÚT THẤT BẠI",
                html: message,
                icon: "success",
                customClass: {
                    icon: "withdraw-error",
                    confirmButton: "!hidden",
                },
                confirmButtonText: "Đóng",
                cancelButtonText: "Thử Lại",
            },
        })
    );
};
window.showWithdrawErrorModal = showWithdrawErrorModal;

const goBack = () => {
    window.history.back();
};
window.goBack = goBack;

window.isMobile = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isMobileUA =
        /(android|ipad|playbook|silk|mobile|tablet|iphone|ipod|blackberry|iemobile|opera mini)/i.test(
            ua
        );

    const hasTouch =
        "ontouchstart" in window ||
        navigator.maxTouchPoints > 0 ||
        navigator.msMaxTouchPoints > 0;

    const isMobileSize = window.innerWidth <= 1024;

    return isMobileUA || (hasTouch && isMobileSize);
};

const touchMoveElement = (element) => {
    let isMouseDown = false;
    let startX = 0;
    let scrollLeft = 0;
    let startXMouseUp = 0;
    let scrollLeftMouseUp = 0;
    $(element).on('mousedown', (event) => {
        event.preventDefault();
        event.stopPropagation();
        isMouseDown = true;
        const currentTarget = event.currentTarget;
        if (!currentTarget) return;
        startX = event.pageX - currentTarget.offsetLeft;
        scrollLeft = currentTarget.scrollLeft;
    })
    $(element).on('mouseleave', () => {
        isMouseDown = false;
    })
    $(element).on('mouseup', (event) => {
        event.stopPropagation();
        event.preventDefault();
        isMouseDown = false;
        const currentTarget = event.currentTarget;
        if (!currentTarget) return;
        // use to check if the user click on the element
        startXMouseUp = event.pageX - currentTarget.offsetLeft;
        scrollLeftMouseUp = currentTarget.scrollLeft;
    })
    $(element).on('mousemove', (event) => {
        if (!isMouseDown) return;
        event.stopPropagation();
        event.preventDefault();
        const x = event.pageX - $(element).offset().left;
        const walk = (x - startX);
        $(element).stop().animate({
            scrollLeft: scrollLeft - walk
        }, {
            duration: 0,
            easing: 'linear'
        })
    })
    $(element)?.find('a')?.on('click', (event) => {
        event.preventDefault();
        if (startX === startXMouseUp && scrollLeft === scrollLeftMouseUp) {
            window.location.href = $(event.currentTarget).attr('href');
        }
    })
}
window.touchMoveElement = touchMoveElement;

