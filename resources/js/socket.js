import { io } from "socket.io-client";

// Socket configuration constants
const SOCKET_CONFIG = {
    autoConnect: true,
    reconnectionAttempts: 10,
    withCredentials: true,
    transports: ["websocket"],
};

// Jackpot management class
class JackpotManager {
    constructor() {
        this.jackpotsData = {};
    }

    updateValue(jackpots = this.jackpotsData) {
        Object.entries(jackpots).forEach(([jackpotId, jackpotValue]) => {
            const value = Array.isArray(jackpotValue)
                ? Math.max(...jackpotValue)
                : jackpotValue;
            this.updateJackpotDisplay(jackpotId, value);
        });
    }

    updateJackpotDisplay(jackpotId, value) {
        const $jackpotValues = $(`.js-jackpot-value-${jackpotId}`);
        $jackpotValues.each((index, element) => {
            const $jackpotValue = $(element);
            const $jackpotText = $jackpotValue.find(".js-jackpot-value");

            if ($jackpotValue.length > 0) {
                const oldValue =
                    parseInt($jackpotText.text().replace(/,/g, "")) || 0;
                const newValue = parseInt(value) || 0;

                $jackpotValue.toggleClass("hidden", newValue <= 0).toggleClass("flex", newValue > 0);

                if (oldValue !== newValue) {
                    animateCounter(
                        $jackpotText,
                        oldValue,
                        newValue,
                        5000,
                        jackpotId,
                        ""
                    );
                }
            }
        });

        $(`div[data-jackpotId='${jackpotId}'] span.prize`).text(value);
    }

    updateTotalValue(jackpots = this.jackpotsData) {
        const totalValue = Object.values(jackpots).reduce((total, values) => {
            return (
                total + (Array.isArray(values) ? Math.max(...values) : values)
            );
        }, 0);

        const $totalJackpot = $("#jackpot-number");
        const $totalJackpotText = $totalJackpot.find(".js-jackpot-value");

        if ($totalJackpot.length > 0) {
            $totalJackpot.removeClass("hidden").addClass("flex");
            const oldValue = parseInt($totalJackpotText.text()) || 0;
            const newValue = parseInt(totalValue) || 0;

            if (oldValue !== newValue) {
                animateCounter($totalJackpotText, oldValue, newValue, 5000, 'total');
            }
        }
    }

    updateJackpotValue
}

// Live casino stream management class
class LiveStreamManager {
    constructor(socketUrl) {
        this.b52Views = null;
        this.goViews = null;
        this.socket = this.initializeSocket(socketUrl);
        this.setupEventListeners();
    }

    initializeSocket(url) {
        return io(url, {
            ...SOCKET_CONFIG,
            autoConnect: false,
            path: "/api/v1/ws"
        });
    }

    setupEventListeners() {
        this.socket.on("connect", () => console.info("Live Stream: Connected successfully"));
        this.socket.on("connect_error", () => console.info("Live Stream: Connected failed"));
        this.socket.on("b52-gamels", (payload) => this.b52Views = payload);
        this.socket.on("go-gamels", (payload) => this.goViews = payload);
    }
}

class NotificationManager {
    constructor() {
        this.notifications = [];
    }
}

// Initialize managers
const jackpotManager = new JackpotManager();
const liveStreamManager = new LiveStreamManager(import.meta.env.VIEW_LIVE_SOCKET_URL);
const notificationManager = new NotificationManager(import.meta.env.VITE_WS_URL);
const cookies = () => {
    if (!document.cookie) return {};
    return document.cookie
        .split(";")
        .filter((cookie) => cookie.trim())
        .reduce((cookies, cookie) => {
            try {
                const [name, ...valueParts] = cookie
                    .split("=")
                    .map((part) => part.trim());
                const value = valueParts.join("=");

                if (name && value !== undefined) {
                    cookies[decodeURIComponent(name)] =
                        decodeURIComponent(value);
                }
                return cookies;
            } catch (e) {
                console.warn("Error parsing cookie:", cookie, e);
                return cookies;
            }
        }, {});
}
const token = cookies()?.user ? JSON.parse(cookies().user)?.token || null : null;
// Main socket setup
const mainSocket = io(import.meta.env.VITE_WS_URL, {
    ...SOCKET_CONFIG,
    path: "/api/v1/ws",
    query: { token }
});

mainSocket.io.on("error", (error) => console.error(error));
mainSocket.on("jackpotbroadcast", (payload) => {
    jackpotManager.jackpotsData = payload;
    jackpotManager.updateValue();
    jackpotManager.updateTotalValue();
    updateLivestreamJackpotValue(jackpotManager.jackpotsData);
    setTimeout(() => {
        localStorage.setItem('jackpotData', JSON.stringify(payload));
    }, 2000);
});


mainSocket.on("notification", (payload) => {
    if (Array.isArray(payload)) {
        notificationManager.notifications = payload;

        // Dispatch event to update real-time notifications
        window.dispatchEvent(new CustomEvent('notificationsUpdated', {
            detail: {
                notifications: payload
            }
        }));
    }
});

const updateLivestreamJackpotValue = (jackpots = jackpotsData) => {
    const totalJackpot = Object.values(jackpots).reduce((acc, curr) => acc + curr, 0);
    const oldTotalJackpot = 0;
    animateCounter($(".js-total-jackpot"), oldTotalJackpot, totalJackpot, 5000, 'total');

    Object.keys(jackpots).forEach((key) => {
        const jackpotId = key; // key corresponds to the jackpotId
        const jackpotValue = jackpots[key];

        const $jackpotValue = $(`.js-jackpot-value-${jackpotId}`);
        if ($jackpotValue.length > 0) {

            $jackpotValue.removeClass('hidden').addClass('flex');
            const oldValue = 0;
            const newValue = parseInt(jackpotValue) || 0;
            if (newValue) {
                $(`.js-jackpot-value-${jackpotId}`).removeClass('hidden').addClass('flex');
            } else {
                $(`.js-jackpot-value-${jackpotId}`).addClass('hidden').removeClass('flex');
            }
            if (oldValue !== newValue) {
                if (newValue) {
                    animateCounter($(`.js-jackpot-value-${jackpotId} .js-jackpot-home-value`), oldValue, newValue, 5000, jackpotId);
                } else {
                    $(`.js-jackpot-value-${jackpotId}`).addClass('hidden');
                }
            }
        }

        const $gameItemPreviewJackpot = $(`.js-live-game-item-preview-jackpot`);
        const jackpotPreviewText = $(`.js-live-game-item-preview-jackpot .js-jackpot-value`);
        const jackpotPreviewData = $gameItemPreviewJackpot.attr('data-game-id');

        if ($gameItemPreviewJackpot.length > 0 && jackpotPreviewData === jackpotId) {
            $gameItemPreviewJackpot.removeClass('hidden').addClass('flex');
            const oldValue = parseInt(jackpotPreviewText.text()) || 0;
            const newValue = parseInt(jackpotValue) || 0;
            if (newValue) {
                $gameItemPreviewJackpot.removeClass('hidden').addClass('flex');
            } else {
                $gameItemPreviewJackpot.addClass('hidden').removeClass('flex');
            }
            if (oldValue !== newValue) {
                animateCounter(jackpotPreviewText, oldValue, newValue, 5000, jackpotId);
            }
        }
    });
};

window.updateLivestreamJackpotValue = updateLivestreamJackpotValue;

const updateViewerCount = (element, gameId, oldValue, newValue, partner) => {
    if (oldValue === newValue) return;

    $(element).closest(".js-viewers-wrapper").removeClass("hidden");
    animateCounter(element, oldValue, newValue, 5000, gameId);

    const $gameViewers = $(`.js-game-item[data-id="${gameId}"][data-partner-provider="${partner}"] .js-viewers`);
    if ($gameViewers.length) {
        animateCounter($gameViewers, oldValue, newValue, 5000, gameId);
    }
};

const getUserActive = (partner, data, $viewers) => {
    const games = Array.isArray(data) ? data : [data];

    games.forEach(game => {
        const gameId = game.gameId;
        const activeUsers = game.activeUsers;

        $viewers.each((_, element) => {
            const elementGameId = $(element).data("id");
            if (elementGameId === gameId || elementGameId === game.mapGameId) {
                const oldValue = parseInt($(element).text().replace(/,/g, "")) || 0;
                const newValue = parseInt(activeUsers) || 0;
                updateViewerCount(element, gameId, oldValue, newValue, partner);
            }
        });
    });
};

const liveCasinoSocket = (partner) => {
    mainSocket.on(`${partner}-gamels`, (payload) => {
        const coverPartner = partner === "techplay" ? "vingame" : partner;
        const $viewers = $(`.js-${coverPartner}-viewers`);

        const parsedPayload = JSON.parse(payload);
        getUserActive(coverPartner, parsedPayload, $viewers);
    });
};

liveCasinoSocket("b52");
liveCasinoSocket("go");
liveCasinoSocket("rik");
liveCasinoSocket("sunwin");
liveCasinoSocket("789club");
liveCasinoSocket("techplay");

// Export necessary globals
window.updateJackpotValue = jackpotManager.updateValue.bind(jackpotManager);
window.b52Views = liveStreamManager.b52Views;
window.goViews = liveStreamManager.goViews;
window.viewLiveCasinoSocket = liveStreamManager.socket;
window.notifications = notificationManager.notifications;
window.GlobalSocket = mainSocket;
