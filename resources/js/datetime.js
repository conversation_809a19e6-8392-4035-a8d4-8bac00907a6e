function convertToClientTimezone(utcDate) {
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone; // Get client's timezone
    const date = new Date(utcDate);
    const options = {
        timeZone,
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
    };

    return new Intl.DateTimeFormat("en-GB", options).format(date).replace(",", "");
}

window.convertToClientTimezone = convertToClientTimezone;
