class MainInit {
    constructor() {
        $('body').removeClass('no-js');

        if (!$('body').hasClass('account-page')) {
            this.initSidebarRight();
        }
        this.initFooterMenu();
    }

    initSidebarRight() {
        const checkAndHideSidebar = () => {
            const isMobileView = window.innerWidth <= 1399;

            const isPCView = window.innerWidth >= 1200;
            const isIframe = $('body').hasClass('iframe-page');
            $('.js-sidebar-right, .site-wrapper').toggleClass(
                'right-section-open',
                !isMobileView
            );
            $('.js-sidebar-right, .site-wrapper').toggleClass(
                'right-section-close',
                isMobileView
            );
            $('.js-sidebar-right-content').toggleClass('open', !isMobileView);
            $('.js-sidebar-right-content').toggleClass('close', isMobileView);
            $('.js-sidebar-right-toggle').toggleClass('open', !isMobileView);
            $('.js-sidebar-right-toggle').toggleClass('close', isMobileView);

            if (isIframe && isPCView) {
                $('#leftSiteMenu').removeClass('menu-open');
                $('.site-wrapper').removeClass('menu-open');
                $('#leftSiteMenu').width('72px');
                $('.js-sidebar-right-toggle').removeClass('open');
                $('.js-sidebar-right-toggle').addClass('close');
                $('.js-sidebar-right').removeClass('right-section-open');
                $('.js-sidebar-right').addClass('right-section-close');
                $('.js-sidebar-right-content').removeClass('open');
                $('.js-sidebar-right-content').addClass('close');
                $('.js-toggle-hidden').addClass('hidden');
                $('.js-icon-open').removeClass('hidden');
                $('.js-icon-close').addClass('hidden');
                $('#leftSiteMenu').find('.js-toggle-hidden').addClass('hidden');
            }
        };

        // Kiểm tra khi trang web được tải
        checkAndHideSidebar();

        // Kiểm tra khi resize window
        $(window).on('resize', function() {
            checkAndHideSidebar();
        });

        // Xử lý click toggle
        $('.js-sidebar-right-toggle').on('click', function () {
            const button = $(this);
            const parent = $('.js-sidebar-right');
            const siteWrapper = $('.site-wrapper');
            parent.toggleClass('right-section-open right-section-close');
            parent.find('.js-sidebar-right-content').toggleClass('open close');
            button.toggleClass('open close');
            siteWrapper.toggleClass('right-section-open');
        });
    }

    initFooterMenu() {
        const $footerMenuGroups = $('.js-footer-menu-group');
        const $footerLogo = $('.js-footer-logo');

        if ($footerLogo.length) {
            $footerLogo.addClass('footer-fade');
        }

        $footerMenuGroups.on('click', function() {
            const $group = $(this);
            const $parentLi = $group.closest('li');
            const $allLis = $parentLi.parent().find('li');
            const $content = $group.next();

            // Kiểm tra nếu content đang hiển thị (đã active)
            const isContentVisible = $content.length && !$content.hasClass('hidden');

            if (isContentVisible) {
                // Nếu đang active thì reset lại tất cả
                $allLis.css('display', '');
                if ($content.length) {
                    $content.removeClass('flex').addClass('hidden');
                }
                if ($footerLogo.length) {
                    $footerLogo.removeClass('hidden');
                }
            } else {
                // Nếu chưa active thì xử lý như cũ
                $allLis.not($parentLi).css('display', 'none');

                if ($content.length) {
                    $content.removeClass('hidden');
                    setTimeout(() => {
                        $content.addClass('flex');
                    }, 10);
                }

                if ($footerLogo.length) {
                    $footerLogo.addClass('hidden');
                }
            }
        });

        // Thêm sự kiện click vào document để reset trạng thái
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.js-footer-menu-group-list').length) {
                $('.js-footer-menu-group-list li').css('display', '');
                $('.js-footer-menu-group-content').removeClass('flex').addClass('hidden');
                if ($footerLogo.length) {
                    $footerLogo.removeClass('hidden');
                }
            }
        });
    }
}

// Khởi tạo khi document ready
$(function() {
    new MainInit();
});
