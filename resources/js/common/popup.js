const API_BASE_PATH = '/api-promotion/v1';

const API_ENDPOINTS = {
    POPUP: '/popup',
    CLOSE_POPUP: '/popup/close'
};

const POPUP_METHODS = {
    JACKPOT_3S: 'jackpot_3s'
};

const JACKPOT_ORIGIN_TYPES = {
    SPORT: 'sport',
    LIVE_CASINO: 'live_casino',
};

const JACKPOT_3S_TYPES = {
    LARGE: 1,
    SMALL: 2,
};

const JACKPOT_TYPES = {
    LARGE_SPORTS: 'LARGE_JACKPOT_SPORTS',
    SMALL_SPORTS: 'SMALL_JACKPOT_SPORTS',
    LARGE_CASINO: 'LARGE_JACKPOT_CASINO',
    SMALL_CASINO: 'SMALL_JACKPOT_CASINO'
};

const JACKPOT_MESSAGES = {
    LARGE: 'Bạn đã trúng hũ lớn',
    SMALL: 'Bạn đã trúng hũ nhỏ'
};

const INVEST_LEGEND_MESSAGES = {
    NAME_SPORTS: 'Hũ Thể Thao từ chương trình',
    TITLE_SPORTS: 'HUYỀN THOẠI ÔM HŨ - SPORTS!',
    NAME_CASINO: 'Hũ Sòng Bài từ chương trình',
    TITLE_CASINO: 'HUYỀN THOẠI ÔM HŨ - SÒNG BÀI!'
};

const SELECTORS = {
    LARGE: {
        ICON: '.large-jackpot-icon',
        AMOUNT: '.large-jackpot-amount',
        VALUE: '.large-jackpot-amount .amount-value'
    },
    SMALL: {
        ICON: '.small-jackpot-icon',
        AMOUNT: '.small-jackpot-amount',
        VALUE: '.small-jackpot-amount .amount-value'
    },
    COMMON: {
        TEXT: '.jackpot-text',
        AMOUNT_VALUE: '.jackpot-amount-value',
        NAME: '.invest-legend-name',
        TITLE: '.invest-legend-title'
    }
};

let eventId = '';

const updateElementText = (element, text) => {
    if (element) {
        element.textContent = text;
    }
};

// Queue system for popups
let popupQueue = [];
let isProcessingQueue = false;

const processNextPopup = () => {
    if (popupQueue.length === 0) {
        isProcessingQueue = false;
        return;
    }

    isProcessingQueue = true;
    const nextPopup = popupQueue.shift();
    showPopup(nextPopup);
};

const showPopup = (popupData) => {
    // Jackpot 3s
    if (popupData.method === POPUP_METHODS.JACKPOT_3S) {
        window.updateEventInvestLegendPopup(popupData);
        openModal(eventInvestLegendModal, false, "event-invest-legend-modal");
        console.info($('#invest-legend-closeModalBtn'))
        $('#invest-legend-closeModalBtn').on('click', () => {
            console.info('zxczxc')
            window.closeEventInvestLegendPopup();
            processNextPopup();
        })
    }
};

const handleShowPopup = (popupData) => {
    popupQueue.push(popupData);

    if (!isProcessingQueue) {
        processNextPopup();
    }
};

const closePopupApi = async (id, callback) => {
    callback?.();
    window.closeModal();
    processNextPopup();
    await submitData(`${API_BASE_PATH}${API_ENDPOINTS.CLOSE_POPUP}/${id}`, {}, {});
};

const getPopupData = async () => {
    let { status, data } = await fetchData(`${API_BASE_PATH}${API_ENDPOINTS.POPUP}`, {}, {});
    if (status !== 'OK' && (!data || !data[0])) return;

    data.forEach(popup => {
        if (popup.is_show) {
            handleShowPopup(popup);
        }
    });
};

const initializePopup = () => {
    if (isLoggedIn) {
        setTimeout(getPopupData, 2000);
    }
};

window.addEventListener('DOMContentLoaded', () => {
    initializePopup();
    window.closePopupApi = closePopupApi;
    window.getPopupData = getPopupData;
});

// popup function event
const isSportsType = (type) => type === JACKPOT_ORIGIN_TYPES.SPORT;
const isLargeType = (jackpot) => jackpot === JACKPOT_3S_TYPES.LARGE;

const updateJackpotDisplay = ({type, jackpot, amount}) => {
    const selectors = isLargeType(jackpot) ? SELECTORS.LARGE : SELECTORS.SMALL;
    const isSports = isSportsType(type);

    // Show icon
    const icon = document.querySelector(selectors.ICON);
    if (icon) {
        icon.classList.remove('hidden');
    }

    // Show amount container
    const amountContainer = document.querySelector(selectors.AMOUNT);
    if (amountContainer) {
        amountContainer.classList.add('flex');
        amountContainer.classList.remove('hidden');

            // Update amount value
            const amountValue = amountContainer.querySelector('.amount-value');
        updateElementText(amountValue,'+' + formatNumberWithComma(amount) + ' VND');
    }

    // Update text and common value
    const jackpotText = document.querySelector(SELECTORS.COMMON.TEXT);
    const jackpotAmountValue = document.querySelector(SELECTORS.COMMON.AMOUNT_VALUE);
    const investLegendName = document.querySelector(SELECTORS.COMMON.NAME);
    const investLegendTitle = document.querySelector(SELECTORS.COMMON.TITLE);

    updateElementText(jackpotText, isLargeType(jackpot) ? JACKPOT_MESSAGES.LARGE : JACKPOT_MESSAGES.SMALL);
    updateElementText(jackpotAmountValue, formatNumberWithComma(amount));
    updateElementText(investLegendName, isSports ? INVEST_LEGEND_MESSAGES.NAME_SPORTS : INVEST_LEGEND_MESSAGES.NAME_CASINO);
    updateElementText(investLegendTitle, isSports ? INVEST_LEGEND_MESSAGES.TITLE_SPORTS : INVEST_LEGEND_MESSAGES.TITLE_CASINO);
};

const updateEventInvestLegendPopup = (data) => {
    if (!data?.type || !data?.jackpot || !data?.amount) return;

    // Update event id
    eventId = data.id;

    setTimeout(() => {
        updateJackpotDisplay({type: data.type, jackpot: data.jackpot, amount: data.amount});
    }, 100);
};

const closeEventInvestLegendPopup = () => {
    window.closePopupApi(eventId);
};

// Expose functions to window
window.updateEventInvestLegendPopup = updateEventInvestLegendPopup;
window.closeEventInvestLegendPopup = closeEventInvestLegendPopup;
