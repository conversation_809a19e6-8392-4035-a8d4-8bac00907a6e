const showWithdrawErrorModal = (message, isReloadOnError = false) => {
    const action = {
        confirmButtonFunc: () => {
            openLiveChatNewTab();
            window.location.reload();
        },
        cancelButtonFunc: () => window.location.reload(),
        clickClose: () => window.location.reload(),
        closePopupFunc: () => window.location.reload(),
    };

    const modalConfig = {
        title: "TẠO PHIẾU RÚT THẤT BẠI",
        html: message,
        icon: "success",
        customClass: {
            icon: "withdraw-error",
        },
        confirmButtonText: "<PERSON>ên hệ CSKH",
        cancelButtonText: "Thử Lại",
        ...action
    };

    window.dispatchEvent(
        new CustomEvent("swal:confirm", { detail: modalConfig })
    );
};
window.showWithdrawErrorModal = showWithdrawErrorModal;