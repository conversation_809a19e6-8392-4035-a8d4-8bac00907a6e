// Tạo module BroadcastManager để quản lý các chức năng broadcast
const BroadcastManager = {
    channel: null,

    init() {
        // Khởi tạo kênh broadcast
        this.channel = new BroadcastChannel('reload_channel');

        // Thi<PERSON><PERSON> lập listener
        this.setupListeners();
    },

    setupListeners() {
        // Lắng nghe tín hiệu từ các tab khác
        this.channel.onmessage = (event) => {
            if (event.data === 'reload') {
                location.reload();
            }
        };
    },

    triggerReloadForAllTabs() {
        this.channel.postMessage('reload');
        location.reload();
    }
};

// Khởi tạo BroadcastManager
BroadcastManager.init();

// Expose function ra global scope để có thể gọi từ bên ngoài
window.triggerReloadForAllTabs = BroadcastManager.triggerReloadForAllTabs.bind(BroadcastManager);