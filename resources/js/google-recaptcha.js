function getGoogleRecaptchaToken() {
    return new Promise((resolve, reject) => {
        try {
            grecaptcha.ready(async function () {
                try {
                    const token = await grecaptcha.execute(globalGoogleRecaptchaSiteKey, { action: 'submit' });
                    resolve(token);
                } catch (error) {
                    console.error(error);
                    reject(error);
                }
            });
        } catch (error) {
            console.error(error);
            reject(error);
        }
    });
}

window.getGoogleRecaptchaToken = getGoogleRecaptchaToken;