@use "components/swiper";
@use "components/layouts/footer";
@use "swiper/swiper-bundle.css";
@use "common";
@use "base/sweetAlert2";
@use "_text";
@use "_button";
@use "_input";
@use "_dropdown";
@use "_animation";
@use "components/left-side-bar-mb";
@use "components/_header";
@use "components/desktop/_games";
@use "components/index";
@use "pages/promotion";
@use "pages/news";
@use "pages/account";
@use "pages/home";
@use "layout";

@tailwind base;
@tailwind components;
@tailwind utilities;
html {
    touch-action: manipulation;
}
:root {
    color-scheme: dark;
}

body {
    background: #001111;
    color: #fff;

    @media (max-width: 1199px) {
        --menu-mobile-bottom: 80px;
        --header-height: 62px;

        &:has(.header-back) {
            header.header, .header-space {
                display: none;
            }
        }
    }
    &.swal2-shown {
        overflow: hidden;
        max-height: 100vh;
    }
}

html {
    &:has(.modal-open) {
        overflow: hidden;
        max-height: 100vh;
    }
}

.is-loading {
    pointer-events: none;
    cursor: not-allowed;
}

.loader-image-transparent {
    background: transparent url("/resources/img/spinner.svg") center center
        no-repeat;
}

.empty-game {
    &.show {
        display: block;
    }
}

.bg-active {
    @apply bg-[linear-gradient(102.35deg,_#FDF3E0_3.63%,_#E9C98C_96.31%)] text-neutral-800 font-normal;
}
.bg-inactive {
    @apply bg-neutral-150 text-neutral-500;
    i {
        @apply text-neutral-800;
        background: -webkit-linear-gradient(
            169.21deg,
            #b1b9cb 6.81%,
            #717589 163.63%
        );
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

.border-active {
    @apply border-[#b01e46] text-black font-semibold;
}
.border-inactive {
    @apply border-grey-200 text-grey;
}

.btn-amount {
    @apply cursor-pointer rounded-md border border-grey-300 bg-white p-2 text-center text-sm font-medium text-grey-900 hover:border-y-yellow-400 hover:bg-yellow-400;
}

.container {
    @media (min-width: 1200px) {
        max-width: 940px;
    }
    @media (min-width: 1400px) {
        max-width: 1140px;
    }
    @media (min-width: 1508px) {
        max-width: 1248px;
    }
    @media (min-width: 1768px) {
        max-width: 1248px;
    }
}
#chat-widget-minimized {
    display: none !important;
}

.account-active-button {
    @apply transition-colors;
    background: radial-gradient(
        77.89% 100% at 50% 0%,
        #447a5c 0%,
        #164137 100%
    );
    &.bg-card {
        background: #23413a;
    }
    &.js-amount-item:hover {
        background: radial-gradient(
            100% 78.57% at 0% 50%,
            rgba(131, 199, 149, 0.37) 0%,
            rgba(131, 199, 149, 0) 100%
        );
    }
    &.packages__item {
        &:hover {
            box-shadow: inset 0 0 0 1px #6cfe00;
        }
    }
    &.active {
        background: radial-gradient(
            77.89% 100% at 50% 0%,
            #197a5f 0%,
            #12472a 100%
        );
        box-shadow: inset 0 0 0 1px #6cfe00;
        .promotions-label {
            @apply text-primary;
        }

        &::before {
            @apply absolute bottom-[8px] right-[1px];
            content: "";
            z-index: 2;
            width: 11px;
            height: 11px;
            color: #121314;
            background: url("/public/asset/icons/check.svg");
            bottom: 2px;
            right: 1px;
            top: auto;
            left: auto;
        }

        &::after {
            @apply absolute bottom-0 right-0;
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 0 21px 21px;
            border-color: transparent transparent #6cfe00 transparent;
            border-bottom-right-radius: inherit;
        }
    }
    @media (min-width: 1200px) {
        &.js-amount-item:hover {
            background: radial-gradient(
                100% 78.57% at 0% 50%,
                rgba(131, 199, 149, 0.37) 0%,
                rgba(131, 199, 149, 0) 100%
            );
        }
        &.packages__item {
            &:hover {
                box-shadow: inset 0 0 0 1px #6cfe00;
            }
        }
        &:hover {
            .bg-card-amount {
                background: linear-gradient(0deg, #153429, #153429);
            }
        }
    }
}
.bg-card-amount {
    position: relative;
    background: linear-gradient(0deg, #22463a, #22463a);
    &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
        border-radius: inherit;
        background: radial-gradient(
            100% 78.57% at 0% 50%,
            rgba(131, 199, 149, 0.37) 0%,
            rgba(131, 199, 149, 0) 100%
        );
    }
}
.account-ticket-bg {
    background: linear-gradient(110.82deg, #234934 16.15%, #203d34 83.85%),
        radial-gradient(
            107.87% 107.87% at 96.47% -5.39%,
            rgba(1, 178, 120, 0.1) 0%,
            rgba(1, 178, 120, 0) 100%
        );
}

.account-withdraw-active-button {
    @apply transition-colors relative;
    box-shadow: inset 0 0 0 1px #23413a;

    &:hover {
        background: radial-gradient(
            100% 78.57% at 0% 50%,
            rgba(131, 199, 149, 0.37) 0%,
            rgba(131, 199, 149, 0) 100%
        );
    }
    &.packages__item {
        &:hover {
            box-shadow: inset 0 0 0 1px #6cfe00;
        }
    }
    &.active,
    &.selected {
        box-shadow: inset 0 0 0 1px #6cfe00;
        .promotions-label {
            @apply text-primary;
        }

        &::before {
            @apply absolute bottom-[8px] right-[1px];
            content: "";
            z-index: 2;
            width: 11px;
            height: 11px;
            color: #121314;
            background: url("/public/asset/icons/check.svg");
            bottom: 2px;
            right: 1px;
            top: auto;
            left: auto;
        }

        &::after {
            @apply absolute bottom-0 right-0;
            content: "";
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 0 21px 21px;
            border-color: transparent transparent #6cfe00 transparent;
            border-bottom-right-radius: inherit;
        }
    }
}
#chat-widget-container {
    pointer-events: none;
    @apply sm:!invisible;
    &.open {
        pointer-events: auto;
    }
}

.grecaptcha-badge {
    @apply hidden;
}

c2-minigame {
    div[draggable="true"]:has(img.game-text-mini) {
        @apply aspect-[90/112.34] max-xl:aspect-[70/87.38];
    }
}
