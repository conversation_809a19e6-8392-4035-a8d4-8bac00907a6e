.mini-game {
    cursor: pointer;
    transition: display 0.2s ease 0s;
    width: 90px;
    height: 113px;
    z-index: 105;
    inset: 400px 0px auto auto;
    position: fixed;

    @media (max-width: 1024px) {
        width: 70px;
        height: 88px;
        inset: auto 15px 200px auto;
    }
}

.mini-game-icon-box {
    width: 90px;
    height: 113px;
    @media (max-width: 768px) {
        width: 70px;
        height: 88px;
    }
    .mini-game-bg {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    .btn-close-mini-game {
        height: 26px;
        z-index: 100;
        position: absolute;
        top: 4%;
        right: 4%;
        object-fit: contain;
        &:hover {
            filter: brightness(1.25);
            scale: 105%;
        }
        &:active {
            scale: 100%;
        }
    }
    .ping {
        animation: ping 5s cubic-bezier(0.15, 0.55, 0.15, 0.5) infinite;
    }
    .mini-text {
        width: 90px;
        object-fit: contain;
        position: absolute;
        top: 35%;
        left: -5%;
        pointer-events: none;
    }
    .game-text {
        width: 90px;
        object-fit: contain;
        position: absolute;
        top: 60%;
        left: 5%;
        pointer-events: none;
        animation-delay: 800ms;
    }

    @keyframes ping {
        0% {
            transform: scale(1);
        }
        12.5% {
            transform: scale(1.2);
        }
        25% {
            transform: scale(1);
        }
        27.5% {
            transform: scale(1);
        }
        50% {
            transform: scale(1);
        }
        62.5% {
            transform: scale(1);
        }
        75% {
            transform: scale(1);
        }
        87.5% {
            transform: scale(1);
        }
        100% {
            transform: scale(1);
        }
    }
}
