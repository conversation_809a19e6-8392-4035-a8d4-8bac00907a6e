body {
    --breadcrumb-height: 48px;
    &.hide-header-mobile {
        header.header, .header-space {
            @apply max-xl:hidden;
        }
        .header-mobile-custom {
            @apply max-xl:flex;
        }
    }
    &:has(.page-events) {
        @apply xl:bg-[#F1F3F9];
    }
}

body[class*="route-iframe"],
body[class*="iframe-page"]{
    .site-content {
        margin: 0 !important;
        width: 100% !important;
        .page-content {
            .container {
                @media screen and (min-width: 1200px) {
                    padding: 0 88px;
                    margin: 0 auto;
                    max-width: 100%;
                }
            }
        }
    }
    .site-wrapper {
        .js-sidebar-right {
            &.right-section-close {
                @apply pointer-events-none select-none invisible;
            }
            &.right-section-open {
                @apply pointer-events-auto select-auto visible;
            }
            .js-sidebar-right-content {
                @apply translate-x-80;
                &.close {
                    @apply translate-x-80;
                }
                &.open {
                    @apply translate-x-0;
                }
            }
        }
        .js-sidebar-right-toggle {
            @apply -scale-x-[1];
            &.close {
                @apply -scale-x-[1];
            }
            &.open {
                @apply scale-x-[1];
            }
        }
    }

    @media screen and (max-width: 1199px) {
        #float-bottom-btn,
        #bottom-nav-menu {
            display: none;
        }
    }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

.stretched-link::after {
    @apply content-[''] absolute inset-0 z-[1];
}
.text-gradient {
    --bg: linear-gradient(143.12deg, #ffe4b5 5.35%, #d9ab56 94.65%);
    background: var(--bg);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.page-container {
    @apply size-full overflow-x-hidden overflow-y-auto pb-[80px] max-xl:pb-6 max-xl:pt-4;
}
.scrollbar {
    &::-webkit-scrollbar {
        @apply w-1;
        &-thumb {
            @apply bg-[#465F59] rounded-[100px];
        }
    }
}
.no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}

.main-mobile__content {
    &:has(.layout-account) {
        @apply bg-[#F8F9FC] max-xl:bg-[#F1F3F9];
    }
}

@media (max-width: 1199px) {
    body {
        &:has(.menu__left) {
            &:has(.page-breadcrumb) {
                .main-mobile__content {
                    @apply h-[calc(100svh-var(--header-height)-var(--breadcrumb-height))];
                }
                .left-side-bar-mb {
                    @apply max-h-[calc(100svh-var(--header-height)-var(--breadcrumb-height))] top-[var(--breadcrumb-height)+var(--header-height)] overflow-y-auto;
                }
                .page-container {
                    padding-top: 0;
                }
            }
        }
    }

    .main-mobile {
        &__content {
            @apply xl:pb-[80px] max-xl:pb-[var(--menu-mobile-bottom)] max-xl:h-[calc(100svh_-_var(--header-height))] max-xl:overflow-y-auto max-xl:overflow-x-hidden;
        }
        &:has(.menu__left #sideNavContent) {
            .main-mobile__content .page-container .container {
                padding-left: 0;
                padding-right: 10px;
            }
        }
    }

    .menu {
        &__left {
            @apply h-[calc(100svh_-_var(--header-height))] pb-[100px];
        }
    }
}

#iframe-progress {
    @apply relative;
    #iframe-progress-bar {
        animation: iframe-progress-bar 6s linear forwards;
    }
}
@keyframes iframe-progress-bar {
    0% {
        width: 0;
    }
    100% {
        width: 90%;
    }
}

.scrollbar-type2 {
    &::-webkit-scrollbar {
        @apply w-1.5 h-1.5 rounded-full;
    }

    &::-webkit-scrollbar-thumb {
        @apply bg-[#465F59] rounded-full;
    }
}

.hide-scrollbar-mb {
    &::-webkit-scrollbar {
        @apply max-xl:hidden;
    }
}

.game-card__thumb {
    video {
        @apply w-full h-full object-cover;
    }
}

.icon-favorite-gradient {
    position: relative;
    &::before {
        content: "\e94e";
        font-family: "icomoon";
        background: linear-gradient(180deg, #FFE100 0%, #FFCC00 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}

.icon-unfavorite {
    &::before {
        background: #fff;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
}


@media (max-width: 1199px) {
    .mb-show-shadow {
        &::before {
            content: '';
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 102;
        }
    }
}

.sportlist-item {
    .player {
        @apply transition-all duration-300
    }
    .button-bet {
        @apply inset-[auto_0_0_0] mx-auto absolute opacity-0 invisible transition-all duration-300;
    }
    .button-bet-vi {
        @apply bottom-0 top-[auto] absolute opacity-0 invisible transition-all duration-300;
    }
    &:hover {
        .player {
            @apply scale-[1.05] brightness-[1.05] transition-all duration-300
        }
        .button-bet {
            @apply bottom-5 top-[auto] opacity-100 visible;
        }
        .button-bet-vi {
            @apply bottom-5 top-[auto] opacity-100 visible;
        }
    }
    @media (max-width: 1399px) {
        &:hover .button-bet-vi {
            @apply h-[36px] bottom-3;
        }
    }
}



.filter-active {
    @apply border-[#80D59C] bg-[linear-gradient(180deg,_#0A2A1C_0%,_#1A4D1D_100%)] !text-primary;
    i {
        @apply text-primary;
    }
}
.filter-inactive {
    @apply border-[#12312B] bg-[#12312B] xl:hover:bg-[#12312B] xl:hover:text-primary;
    i {
        @apply text-[#8C9D98]
    }
    &:hover {
        i {
            @apply text-primary;
        }
    }
}


.video-wrapper {
    > video, > iframe {
        color-scheme: light;
        @apply pointer-events-none object-cover w-full h-[102%];
    }
    &:has(video), &:has(iframe) {
        > img {
            @apply hidden;
        }
    }

    img {
        transform: unset!important;
        top: 0!important;
        left: 0!important;
        object-fit: cover;
    }
    @media (max-width: 1199px) {
        > iframe {
            transform: scaleX(1.05) scaleY(1.05) !important;
        }
    }
}

.event-tab-active {
    @apply text-primary bg-[linear-gradient(180deg,_#0A2A1C_0%,_#1A4D1D_100%);] border border-[#80D59C];
    i {
        @apply text-primary;
    }
}

.event-tab {
    @apply text-white bg-[#12312B] border border-transparent;
    i {
        @apply text-neutral-300;
    }
    &:hover {
        @apply text-primary;
        i {
            @apply text-primary;
        }
    }
}



.border-gradient-custom {
    background: radial-gradient(77.89% 100% at 50% 0%, #197A3F 0%, #203732 70.39%, #1E2D2C 100%) padding-box,linear-gradient(180deg, #05E093 0%, rgba(5, 224, 147, 0.24) 100%) border-box;
    border: 1px solid transparent;
    &:hover {
        background: radial-gradient(77.89% 100% at 50% 0%, #0EB353 0%, #1E412E 100%) padding-box,linear-gradient(180deg, #05E093 0%, rgba(5, 224, 147, 0.24) 100%) border-box;
    }
}

#game-sort {
    .base-dropdown {
        .dropdown-icon {
            @apply max-xl:hidden;
        }
        .dropdown-value {
            .dropdown-option-item-content {
                i {
                    @apply max-xl:hidden;
                }
            }
        }
    }
}

.static-page-content {
    img {
        @apply mb-1 mt-1;
    }
    &.huong-dan-giao-dich-p2p,
    &.huong-dan-nap-tien,
    &.huong-dan-rut-tien {
        img {
            @apply mb-1 mt-1;
        }
    }
}


.is-item-loading {
    position: relative;
    pointer-events: none;
    &::before {
        content: '';
        position: absolute;
        inset: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1;
    }
    &::after {
        content: '';
        position: absolute;
        inset: 0;
        background: url('/public/asset/images/spinner.svg') no-repeat center center;
        background-size: 24px;
        z-index: 2;
    }
}

.js-jackpot-value, .js-viewers {
    font-variant-numeric: tabular-nums;
}

.tabular-number {
    font-variant-numeric: tabular-nums;
}

.popup-amount-bg {
    background: url('/public/modules/jackpotlegends/images/amount-bg.avif') no-repeat;
    background-size: 100% 100%;
}

.popup-amount-gradient {
    background: linear-gradient(90deg, #EFE399 0%, #FDF7D0 31.5%, #FBF2B6 91%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}