@php
    $brandName = config('app.brand_name');
    $currentRoute = Route::current()->getName();
    $footerItems = config('footer.footerItems');
    $contactItems = config('footer.contactItems');
    $bankList = config('footer.bankList');
    $providerLinkList = config('footer.providerLinkList');

    $isShortFooter =
        in_array($currentRoute, config('footer.isShortFooter')) ||
        preg_match(
            '/^(iframe|cockfight|lode|lo-de|quayso|ca-do-bong-da|ca-cuoc-the-thao|account\/)/',
            request()->path(),
        );
@endphp
<footer class="footer w-full max-xl:hidden">
    <div class="container">

        @if ($isShortFooter)

            <div class="flex items-center gap-y-4 border-t-2 border-white/5 pt-[48px]">
                <a href="/" class="js-footer-logo mr-[50px] inline-flex shrink-0 items-center justify-center">
                    <div class="relative h-[32px]">
                        <img src="{{ asset('asset/images/' . strtolower($brandName) . '-logo.svg') }}"
                            class="h-full w-full object-contain" alt="logo" />
                    </div>
                </a>

                <div>
                    <ul class="js-footer-menu-group-list flex gap-[50px]">
                        @foreach ($footerItems as $item)
                            <li class="flex items-center justify-start">
                                <p
                                    class="js-footer-menu-group cursor-pointer text-[18px] font-bold capitalize leading-[22px] text-white">
                                    {{ __($item['title']) }}</p>
                                <div
                                    class="js-footer-menu-group-content relative hidden gap-3 pl-[19px] transition-all before:absolute before:left-2 before:top-1/2 before:h-[22px] before:w-[3px] before:-translate-y-1/2 before:bg-primary">
                                    @foreach ($item['items'] as $subItem)
                                        @php
                                            $link = isset($subItem['link']) ? $subItem['link'] : '';
                                            $onclick = isset($subItem['onclick']) ? $subItem['onclick'] : null;
                                        @endphp
                                        <x-kit.link :link="$link" :onclick="$onclick"
                                            class="text-[14px] font-normal leading-[18px] text-neutral hover:text-primary">
                                            {{ __($subItem['title']) }}
                                        </x-kit.link>
                                    @endforeach
                                </div>
                            </li>
                        @endforeach
                    </ul>
                </div>

                <ul class="ms-auto flex gap-2">
                    @foreach ($contactItems as $contactItem)
                        @php
                            $title = isset($contactItem['title']) ? $contactItem['title'] : '';
                            $link = isset($contactItem['link']) ? $contactItem['link'] : '';
                            $icon = isset($contactItem['icon']) ? $contactItem['icon'] : '';
                            $onclick = isset($contactItem['onclick']) ? $contactItem['onclick'] : null;
                            $isNewTab = isset($contactItem['isNewTab']) ? $contactItem['isNewTab'] : false;
                        @endphp
                        @if ($title === 'Live Chat' || (!empty($link) && $title !== 'Live Chat'))
                            <li>
                                <x-kit.link :link="$link" :onclick="$onclick"
                                    class="inline-flex w-full items-center rounded-lg bg-white/10 px-3 py-2 transition-all hover:bg-white/20"
                                    :isNewTab="$isNewTab">
                                    <img src="{{ asset($icon) }}" alt="{{ $title }}" class="size-[30px]">
                                </x-kit.link>
                            </li>
                        @endif
                    @endforeach
                </ul>
            </div>
        @else
            <div class="footer__menu grid grid-cols-5 gap-x-4 border-t-2 border-white/5 pt-[48px] 3xl:gap-x-8">
                @foreach ($footerItems as $item)
                    <div>
                        <div class="mb-6 text-[16px] font-bold capitalize leading-[22px] text-white 3xl:text-[18px]">
                            {{ __($item['title']) }}
                        </div>
                        <ul class="flex flex-col gap-y-3">
                            @foreach ($item['items'] as $subItem)
                                @php
                                    $title = isset($subItem['title']) ? $subItem['title'] : '';
                                    $link = isset($subItem['link']) ? $subItem['link'] : '';
                                    $onclick = isset($subItem['onclick']) ? $subItem['onclick'] : null;
                                @endphp
                                <li class="flex items-center">
                                    <x-kit.link :link="$link" :title="$title"
                                        class="line-clamp-1 break-all text-[14px] font-normal leading-[18px] text-white hover:text-primary"
                                        :onclick="$onclick" />
                                </li>
                            @endforeach
                        </ul>
                    </div>
                @endforeach
                <div>
                    <div class="mb-6 text-[16px] font-bold capitalize leading-[22px] text-white 3xl:text-[18px]">Trung
                        tâm hỗ trợ</div>
                    <ul class="flex flex-col gap-y-2">
                        @foreach ($contactItems as $contactItem)
                            @php
                                $title = isset($contactItem['title']) ? $contactItem['title'] : '';
                                $link = isset($contactItem['link']) ? $contactItem['link'] : '';
                                $icon = isset($contactItem['icon']) ? $contactItem['icon'] : '';
                                $onclick = isset($contactItem['onclick']) ? $contactItem['onclick'] : null;
                                $isNewTab = isset($contactItem['isNewTab']) ? $contactItem['isNewTab'] : false;
                                $isLiveChat = isset($contactItem['type']) ? $contactItem['type'] : false;
                            @endphp

                            @if ($title === 'Live Chat' || (!empty($link) && $title !== 'Live Chat'))
                                <li>
                                    <x-kit.link :link="$link" :isLiveChat="$isLiveChat"
                                        class="inline-flex h-10 w-full items-center justify-start gap-2 rounded-lg bg-white/10 px-3 py-2 transition-all hover:bg-white/20 3xl:gap-3 3xl:px-7 3xl:pr-1"
                                        :isNewTab="$isNewTab">
                                        <img src="{{ asset($icon) }}" alt="{{ $title }}" class="size-6">
                                        <div class="text-[13px] leading-[18px] text-white 2xl:text-[14px]">
                                            {{ $title }}
                                        </div>
                                    </x-kit.link>
                                </li>
                            @endif
                        @endforeach
                    </ul>
                </div>
            </div>
        @endif

        <div
            class="footer__divider my-5 h-[1px] w-full bg-[linear-gradient(90deg,rgba(5,54,39,0)_0%,#053627_50%,rgba(5,54,39,0)_100%)]">
        </div>

        <div class="footer__banks pointer-events-none flex flex-col">
            <div
                class="footer-swiper-banks swiper swiper-container [&:not(.swiper-initialized)_.swiper-wrapper]:gap-6 [&:not(.swiper-initialized)_.swiper-wrapper]:overflow-auto">
                <div class="swiper-wrapper autoplay-swiper no-scrollbar">
                    @foreach ($bankList as $path)
                        <div class="swiper-slide !w-[24px]">
                            <img src="{{ asset('asset/images/footer/banks/' . $path . '?v=1.0.1') }}"
                                class="h-full w-full object-contain" alt="Payment method" loading="lazy" />
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div
            class="footer__divider my-5 h-[1px] w-full bg-[linear-gradient(90deg,rgba(5,54,39,0)_0%,#053627_50%,rgba(5,54,39,0)_100%)]">
        </div>
        <div class="flex flex-col">
            <div
                class="footer-swiper-providers swiper swiper-container [&:not(.swiper-initialized)_.swiper-wrapper]:gap-6 [&:not(.swiper-initialized)_.swiper-wrapper]:overflow-auto">
                <div class="swiper-wrapper autoplay-swiper no-scrollbar">
                    @foreach ($providerLinkList as $item)
                        <div class="swiper-slide !w-[116px] cursor-pointer">
                            <a href="{{ $item['url'] }}" class="block">
                                <img src="{{ asset('asset/images/footer/providers/' . $item['image']) }}"
                                    alt="provider" class="h-[40px] w-[116px] rounded-[4px] object-contain"
                                    loading="lazy" />
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        @if (!$isShortFooter)
            <div
                class="footer__divider my-5 h-[1px] w-full bg-[linear-gradient(90deg,rgba(5,54,39,0)_0%,#053627_50%,rgba(5,54,39,0)_100%)]">
            </div>

            <div class="text-center">
                <a href="/" class="mx-auto mb-4 inline-flex items-center justify-center">
                    <div class="relative h-[32px]">
                        <img src="{{ asset('asset/images/' . strtolower($brandName) . '-logo.svg') }}"
                            class="h-full w-full object-contain" alt="logo" />
                    </div>
                </a>
                <div class="text-xs font-normal text-neutral-200">
                    Khám phá nền tảng giải trí trực tuyến hàng đầu, nơi hội tụ cá cược thể thao, sòng bài đẳng cấp, lô
                    đề
                    hấp dẫn và hàng loạt trò chơi thú vị khác. <br />Giao diện tối ưu, bảo mật vượt trội, cùng nhiều ưu
                    đãi
                    cực khủng đang chờ bạn tham gia
                </div>
            </div>
        @endif

        <div
            class="footer__divider mt-5 h-[1px] w-full bg-[linear-gradient(90deg,rgba(5,54,39,0)_0%,#053627_50%,rgba(5,54,39,0)_100%)]">
        </div>

        <div class="footer__copyright py-6 text-center text-[14px] leading-[18px] text-[#6B8381]">
            Copyright {{ $brandName }} © 2025. All rights reserved.
        </div>
    </div>
    @push('scripts')
        {!! $localBusiness->toScript() !!}
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                new Swiper('.footer-swiper-banks', {
                    loop: true,
                    autoplay: {
                        delay: 1,
                        disableOnInteraction: false,
                        pauseOnMouseEnter: false,
                    },
                    freeMode: true,
                    spaceBetween: 24,
                    speed: 5000,
                    slidesPerView: 'auto',
                    touchMove: false,
                    allowTouchMove: false,
                    simulateTouch: false,
                    reverseDirection: true,
                });

                new Swiper('.footer-swiper-providers', {
                    loop: true,
                    autoplay: {
                        delay: 1,
                        disableOnInteraction: false,
                        pauseOnMouseEnter: false,
                    },
                    freeMode: true,
                    spaceBetween: 24,
                    speed: 5000,
                    slidesPerView: 'auto',
                    touchMove: false,
                    allowTouchMove: false,
                    simulateTouch: false,
                    reverseDirection: true,
                });

            });


            function handleClickOpenBankLink() {
                const modalBankList = `<x-ui.footer.bank-list-modal />`
                openModal(modalBankList, false, 'footer-banklist-modal')
            }
        </script>
    @endpush
</footer>
