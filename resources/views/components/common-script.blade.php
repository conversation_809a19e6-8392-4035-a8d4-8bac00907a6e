@php
    $liveChatUrl = config('constants.live_chat_url');
    $liveChatLicense = config('constants.live_chat_license') ?? 'null';
@endphp
<script>
    window.addEventListener("load", () => {
        const refreshInterval = "{{ env('VITE_REFRESH_INTERVAL', 5000) }}";
        let userInterval;

        async function scheduleNextRefresh() {
            if (!user || isLoggingOut) {
                clearInterval(userInterval);
                return;
            }

            if (isRefreshing) return;

            try {
                isRefreshing = true;
                await refreshUser();
            } finally {
                isRefreshing = false;
                userInterval = setTimeout(scheduleNextRefresh, refreshInterval);
            }
        }

        if (user) {
            scheduleNextRefresh();
        }

        window.__lc = window.__lc || {};
        window.__lc.license = {{ $liveChatLicense }};
        window.__lc.integration_name = "manual_onboarding";
        window.__lc.product_name = "livechat";
        window.__lc.chat_between_groups = false;
        (function(n, t, c) {
            function i(n) {
                return e._h ? e._h.apply(null, n) : e._q.push(n)
            }
            var e = {
                _q: [],
                _h: null,
                _v: "2.0",
                on: function() {
                    i(["on", c.call(arguments)])
                },
                once: function() {
                    i(["once", c.call(arguments)])
                },
                off: function() {
                    i(["off", c.call(arguments)])
                },
                get: function() {
                    if (!e._h) throw new Error("[LiveChatWidget] You can't use getters before load.");
                    return i(["get", c.call(arguments)])
                },
                call: function() {
                    i(["call", c.call(arguments)])
                },
                init: function() {
                    var n = t.createElement("script");
                    n.async = !0, n.type = "text/javascript", n.src = "https://cdn.livechatinc.com/tracking.js",
                        t.head.appendChild(
                            n)
                    checkLiveChatState();
                }
            };
            !n.__lc.asyncInit && e.init(), n.LiveChatWidget = n.LiveChatWidget || e
        }(window, document, [].slice))
    });

    async function refreshUser(updateEmailElement = '') {
        try {
            const {
                user = {},
                    status
            } = await fetchData(
                '/refresh', {}, {
                    useProxy: false
                },
            );

            if (status !== 'OK') {
                deleteCookie('user');
                window.location.href = '/?modal=session-expired';
            }

            const {
                balance_txt,
                email,
                package_id
            } = user;
            $('.js-user-balance').text(`${formatNumberWithComma(balance_txt) ?? 0} ` + currency);
            if (updateEmailElement && $(updateEmailElement).length) {
                $(updateEmailElement).val(email)
            }

            // Check user change package
            if (currentUserPackageId !== package_id) {
                triggerReloadForAllTabs();
                window.location.reload();
            }

        } catch (error) {
            console.error("Error fetching user data:", error);
            throw error;
        }
    }

    window.refreshUser = refreshUser;
    async function refreshBalance(event) {
        event.preventDefault();
        event.stopPropagation();
        try {
            if (isRefreshing) {
                return;
            }
            isRefreshing = true;
            event.target.classList.add('animate-spin');
            await refreshUser();
        } finally {
            setTimeout(() => {
                event.target.classList.remove('animate-spin');
                isRefreshing = false;
            }, 300);
        }
    }

    function navigateTo(route) {
        // Here you can implement your navigation logic.
        // For example, using window.location to navigate to a new route.
        const locale = "{{ app()->getLocale() === 'en' ? '' : app()->getLocale() }}"
        let newRoute = locale ? `/${locale}/${route}` : `/${route}`
        window.location.href = newRoute
    }

    function handleClick(action) {
        
        const listPreventLode = ['power655', 'mega645', 'sieu-toc-md5', 'lo-de-sieu-toc'];
        const actionName = action.replace(/^\//, '');
        if (!isLoggedIn) {
            login();
        } else {
            if(!isUpdatedFullname && !action.includes('account')) {
                const cb = () => { window.location.href = action }
                updateFullname(cb)
                return
            }

            if (listPreventLode.includes(actionName)) {
                window.dispatchEvent(
                    new CustomEvent("swal:confirm", {
                        detail: {
                            title: 'Game không chơi được',
                            html: 'Bạn không thể chơi game này do đang sử dụng khuyến mãi.',
                            icon: 'success',
                            confirmButtonText: 'Trang Chủ',
                            customClass: {
                                icon: 'game-error',
                            },
                            cancelButtonText: 'Xem Khuyến Mãi',
                            cancelButtonFunc: async () => {
                                window.location.href = '/account/promotion';
                            },
                            confirmButtonFunc: async () => {
                                window.location.href = '/';
                            },
                        },
                    })
                );
                return
            }

            window.location.href = action;
        }
    }
    const openNewTab = async (url, callback = async () => null, isHtmlDocument = false, isDesktopNewTab = true) => {
        if (!isDesktopNewTab && !isMobile()) {
            return
        }
        let newTab = isSafari ? window.open('about:blank', '_blank') : null

        const newUrl = await callback()
        if (newUrl) {
            url = newUrl
        }


        if (url) {
            if (newTab) {
                setTimeout(() => {
                    if (isHtmlDocument) {
                        newTab.document.open()
                        newTab.document.write(url)
                        newTab.document.close()
                    } else {
                        newTab.location.href = url
                    }
                })
            } else {
                if (isHtmlDocument) {
                    const newTab = window.open('about:blank', '_blank')
                    newTab?.document.open()
                    newTab?.document.write(url)
                    newTab?.document.close()
                } else {
                    window.open(url, '_blank')
                }
            }
        } else {
            newTab?.close()
        }
    }

    const openLiveChat = async (event = null) => {
        if (event) {
            event.preventDefault();
        }

        if (window.LiveChatWidget) {
            window.LiveChatWidget.call('maximize');
            const chatWidgetContainer = document.getElementById('chat-widget-container');
            chatWidgetContainer?.classList?.add('open');
        } else {
            console.warn('LiveChatWidget chưa được khởi tạo');
        }
    }
    const openLiveChatNewTab = async (event = null) => {
        if (event) {
            event.preventDefault();
        }

        window.open(liveChatUrl, '_blank');
    }

    function updateUrlParam(url, param, value) {
        let urlObj = new URL(url);
        let paramValue = urlObj.searchParams.get(param);
        if (paramValue === null || paramValue === "") {
            urlObj.searchParams.set(param, value);
        }

        return urlObj.toString();
    }
</script>
<x-scripts.auth-script />
<x-scripts.game-script />
<x-scripts.hotmatch-script />
<x-scripts.sport-script />
<!-- Start of LiveChat (www.livechat.com) code -->
<script>
    function checkLiveChatState(attempts = 0) {
        const MAX_ATTEMPTS = 5;

        if (attempts >= MAX_ATTEMPTS) {
            console.warn('Đã đạt giới hạn số lần thử');
            return;
        }

        if (window.LiveChatWidget && window.LiveChatWidget?._h) {
            try {
                const chatWidgetContainer = document.getElementById('chat-widget-container');
                const liveChatStatus = window.LiveChatWidget.get("state")?.visibility;
                chatWidgetContainer?.classList?.toggle('open', liveChatStatus === 'maximized');

                // Thành công, không cần thử lại
                return;
            } catch (error) {
                console.warn('LiveChat chưa sẵn sàng, thử lại lần:', attempts + 1);
            }
        }
        // Thử lại sau 3s
        setTimeout(() => {
            checkLiveChatState(attempts + 1);
        }, 3000);
    }


    window.__lc = window.__lc || {};
    window.__lc.license = {{ $liveChatLicense }};
    window.__lc.integration_name = "manual_onboarding";
    window.__lc.product_name = "livechat";
    window.__lc.chat_between_groups = false;
    (function(n, t, c) {
        function i(n) {
            return e._h ? e._h.apply(null, n) : e._q.push(n)
        }
        var e = {
            _q: [],
            _h: null,
            _v: "2.0",
            on: function() {
                i(["on", c.call(arguments)])
            },
            once: function() {
                i(["once", c.call(arguments)])
            },
            off: function() {
                i(["off", c.call(arguments)])
            },
            get: function() {
                if (!e._h) throw new Error("[LiveChatWidget] You can't use getters before load.");
                return i(["get", c.call(arguments)])
            },
            call: function() {
                i(["call", c.call(arguments)])
            },
            init: function() {
                var n = t.createElement("script");
                n.async = !0, n.type = "text/javascript", n.src = "https://cdn.livechatinc.com/tracking.js",
                    t.head.appendChild(
                        n)
                checkLiveChatState();
            }
        };
        !n.__lc.asyncInit && e.init(), n.LiveChatWidget = n.LiveChatWidget || e
    }(window, document, [].slice))
</script>
