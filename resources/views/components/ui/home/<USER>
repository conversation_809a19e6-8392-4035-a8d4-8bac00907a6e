@props(['livecasino'])
<x-ui.home.section-wrap id="home-livecasino" pathIcon="livecasino" title="Live casino" link="{{ route('casino') }}"
readmore>
    <div class="home-livecasino swiper swiper-loader swiper-container [&:not(.swiper-initialized)_.swiper-wrapper]:!flex [&:not(.swiper-initialized)_.swiper-wrapper]:!gap-3 xl:[&:not(.swiper-initialized)_.swiper-wrapper]:!gap-5 [&:not(.swiper-initialized)_.swiper-wrapper]:overflow-auto">
        <div class="swiper-wrapper no-scrollbar">
            @if (isset($livecasino?->items) && is_array($livecasino?->items))
                @foreach ($livecasino?->items as $idx => $item)
                    <div class="swiper-slide max-sms:w-full max-xl:w-[357px] xl:max-h-[233px] xl:w-[267px] 2xl:w-[333px] 3xl:w-[369px]">
                        <x-ui.game.card-horizontal
                            name="{{ $item->name ?? 'title' }}"
                            image="{{ $item->image ?? '' }}"
                            partner="{{ $item->partner ?? '' }}"
                            partner_provider="{{ $item->partner_provider ?? '' }}"
                            partner_txt="{{ $item->partner_txt ?? '' }}"
                            is_favorite="{{ $item->is_favorite ?? false }}"
                            data-api="{{ $item->api_url ?? '' }}"
                            id="{{ $item->partner_game_id ?? '' }}"
                            table_id="{{ $item->table_id ?? '' }}"
                            tags="{{ $item->tags ?? '' }}"
                            lobbyType="casino"
                            deny_info="{{ $item->deny_info ?? false }}"
                            jackpot="{{ $item->jackpot ?? 0 }}">
                        </x-ui.game.card-horizontal>
                    </div>
                @endforeach
            @endif
        </div>
    </div>
</x-ui.home.section-wrap>
@pushOnce('scripts')
<script>
    document.addEventListener('DOMContentLoaded', () => {
        const swiper = new Swiper('.home-livecasino', {
            spaceBetween: 20,
            slidesPerView: 1,
            navigation: {
                nextEl: '#home-livecasino-next',
                prevEl: '#home-livecasino-prev',
            },
            breakpoints: {
                480: {
                    slidesPerView: 1,
                },
                1200: {
                    slidesPerView: '3',
                },
            },
        });
    });
</script>
@endpushOnce
