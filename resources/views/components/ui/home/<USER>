@props(['banners'])
@php
    use <PERSON><PERSON><PERSON>\Agent\Agent;
    $swiperId = 'hero-banner-' . Str::random(8);
    $agent = new Agent();
    $user = Auth::user();
@endphp
<div class="max-xl:-mx-4">
    <div class="w-full" style="--swiper-navigation-size: 40px;--swiper-scrollbar-drag-bg-color: #fff">
        <div class="{{ $swiperId }} swiper home-swiper relative overflow-hidden text-[#fff] xl:rounded-lg">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <a href="/hu-song-bai">
                        <x-jackpotlegends::banner />
                    </a>
                </div>
                @foreach ($banners as $banner)
                    <div class="swiper-slide">
                        <a href="#" onclick="handleBannerClick(event, {{ json_encode($banner) }})">
                            <picture>
                                <source srcset="{{ $banner['imgSrcMobile'] }}.avif?v=1.0.4" media="(max-width: 1199px)">
                                <source srcset="{{ $banner['imgSrc'] }}.avif?v=1.0.4" media="(min-width: 1200px)">
                                <img src="{{ $banner['imgSrc'] . '.avif?v=1.0.4' }}" alt="{{ __($banner['title']) }}"
                                    class="block aspect-[390/150] w-full object-cover xl:aspect-[1216/300]">
                            </picture>
                        </a>
                    </div>
                @endforeach
            </div>
            <div class="{{ $swiperId }}-swiper-button-next swiper-button-next max-xl:!hidden"></div>
            <div class="{{ $swiperId }}-swiper-button-prev swiper-button-prev max-xl:!hidden"></div>
            <div class="{{ $swiperId }}-swiper-panigation swiper-panigation"></div>
        </div>
    </div>
</div>
@push('scripts')
    <script>
        const swiperId = `.{{ $swiperId }}`;
        document.addEventListener('DOMContentLoaded', () => {
            const swiper = new Swiper(swiperId, {
                slidesPerView: 1,
                loop: true,
                pagination: {
                    el: `${swiperId}-swiper-panigation`,
                    clickable: true,
                },
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                navigation: {
                    nextEl: `${swiperId}-swiper-button-next`,
                    prevEl: `${swiperId}-swiper-button-prev`,
                },
                spaceBetween: 0,
            });
        });

        function handleBannerClick(event, banner) {

            event.preventDefault();
            const isMobile = @json($agent->isMobile());
            let tpToken = '';
            const appUrl = "{{ config('app.url') }}";
            const vesocaoUrl = "{{ config('app.vesocao') }}";

            if (banner.required_login && banner.required_login == true) {
                const isLoggedIn = @json(Auth::check());

                if (!isLoggedIn) {
                    login();
                    return;
                }
            }

            if (banner.title === 'home.promo_vesocao') {
                if (isMobile) {
                    window.open(vesocaoUrl+'/?token='+tpToken, '_blank');
                } else {
                    window.location.href = appUrl + banner.url + '/?token=' + tpToken;
                }
                return;
            }
            window.location.href=banner.url;
        }
    </script>
@endpush
