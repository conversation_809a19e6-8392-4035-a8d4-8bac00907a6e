@php
    $gameTypeList = config('home.gameTypeList');
@endphp

<x-ui.home.section-wrap id="home-gametype" pathIcon="game-type" title="Thể loại game">
    <div class="grid grid-cols-2 gap-3 sm:grid-cols-3 md:grid-cols-4 xl:grid-cols-6 4xl:gap-5">
        @foreach ($gameTypeList as $item)
            <div class="game-type-item group relative aspect-[173/230] cursor-pointer overflow-hidden rounded-lg xl:aspect-[186/248]"
                onclick="onClickGameType({{ json_encode($item) }})">
                <p
                    class="absolute top-0 z-[1] w-full pt-3 text-center text-[16px] 2xl:text-[16px] 3xl:text-[18px] font-semibold uppercase leading-[22px] text-white xl:pt-5 xl:leading-[28px] 4xl:text-[20px]">
                    {{ $item['title'] }}</p>
                <img src="{{ asset('asset/images/home/<USER>/' . $item['imgSrc'] . '.avif') }}" alt=""
                        class="h-full w-full object-cover transition-all duration-300 xl:group-hover:scale-105 xl:group-hover:brightness-110"
                        {{ count($gameTypeList) > 6 ? 'loading=lazy' : '' }}>
            </div>
        @endforeach
    </div>
</x-ui.home.section-wrap>
