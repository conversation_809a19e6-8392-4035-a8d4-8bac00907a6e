@php
    $user = Auth::user();
@endphp
<div class="relative z-10 mx-auto">
    <div class="text-[18px] font-semibold uppercase leading-[22px] text-white">{{ __('auth.updateFullname') }}</div>
    <div class="mt-1 text-[14px] font-normal leading-[18px] text-white">
        {{ __('auth.updateFullnameText') }}
    </div>
    <div id="update-fullname-container" class="mt-4">
        <form id="update-fullname-form" method="POST" autocomplete="off">
            <input type="hidden" name="username" id="username" value="{{ $user->username ?? '' }}">
            <div class="mb-6">
                <x-kit.input id="fullname" type="text" name="fullname" maxlength="29"
                    label="{{ __('auth.fullname') }}" placeholder="{{ __('auth.usernamePlaceholder') }}" />
                <div class="mb-6 mt-5 text-[12px] leading-[18px] text-neutral-400">
                    {{ __('auth.chaneNameWarning') }}
                </div>
                <div id="auth-btn-submit" class="flex">
                    <x-kit.button type="submit" disabled class="btn btn-primary w-full h-10 capitalize">
                        {{ __('auth.saveBtnText') }}
                    </x-kit.button>
                </div>
                <div id="auth-btn-submit-loading" class="hidden text-center">
                    <img class="mx-auto" src="{{ asset('asset/images/spinner.svg') }}" alt="loading">
                </div>
            </div>
        </form>
    </div>
</div>

@pushOnce('scripts')
    <script>
        document.addEventListener('modalReady', function(event) {
            let debounceTimer;
            $('#fullname').off().on("change keyup input-paste", function(e) {
                const input = this;
                const currentValue = input.value;

                clearTimeout(debounceTimer);
                debounceTimer = setTimeout(() => {
                    let value = currentValue
                        .normalize("NFD")
                        .replace(/[\u0300-\u036f]/g, "")
                        .replace(/đ/gi, "dd")
                        .replace(/[^a-zA-Z0-9]/g, "");
                    input.value = value;
                }, 0);
            });
        });
    </script>
@endPushOnce
