@props([
    'title',
    'games',
    'activeFilter',
    'filters',
    'recommendGames',
    'totalGames',
    'currentProvider',
    'lobbyType' => 'game',
])
@php
    use App\Helpers\DetectDeviceHelper;
    $isMobile = DetectDeviceHelper::isMobile();
    $isFavorite = $activeFilter['sort'] === 'favorite';
    $isLobbyAll = array_key_exists('type', request()->all()) && request()->get('type') === 'all';
    $isShowRecommendGame =
        (!isset(request()->type) || (isset(request()->type) && request()->type === 'all') || $isLobbyAll) &&
        !$isFavorite;
    $keyword = request()->get('keyword');

    $isFavoriteEmpty = !str_contains(request()->fullUrl(), '?') && $isFavorite && empty($games);
    $isRecentEmpty = request()->get('filter') === 'recent' && empty($games);
    $isSearchEmpty = !empty($keyword) && empty($games);
@endphp
<div @class([
    $lobbyType,
    'max-xl:mb-[60px]',
    'max-xl:pt-[88px]' => $lobbyType === 'game',
])>
    {{ Breadcrumbs::render($lobbyType === 'game' ? 'games' : 'casino') }}
    <div class="flex flex-col gap-5 max-xl:gap-0">
        @if ($lobbyType === 'game')
            <div
                class="max-xl:absolute max-xl:left-0 max-xl:right-0 max-xl:top-0 max-xl:bg-[url('/public/asset/images/games/top-winner-bg-mb.jpg')] max-xl:bg-full max-xl:bg-no-repeat max-xl:py-2 max-xl:pl-4">
                <x-ui.game.top-winner />
            </div>
        @endif

        <x-ui.providers-filter :providers="$filters['typeOptions']" :$activeFilter id="provider-buttons" />

        <div class="flex w-full flex-col">
            <div class="order-none mb-6 flex flex-nowrap items-center justify-between gap-6 max-xl:mb-5 xl:order-1">
                <div @class([
                    'flex items-center flex-wrap gap-2 max-xl:hidden xl:shrink-0',
                    'hidden' => $activeFilter['sort'] === 'favorite',
                ])>
                    <x-ui.filter :filters="$filters['sortOptions']" :$activeFilter id="filter-buttons" />
                </div>
                <div
                    class="flex flex-nowrap items-center justify-end gap-[8px] max-xl:mt-[10px] max-xl:w-full xl:flex-1">
                    @if (!empty($filters['providerOptions']) && count($filters['providerOptions']) > 2)
                        <div id="game-provider" class="w-full max-xl:flex-1 xl:max-w-[200px]">
                            <x-kit.dropdown id="provider-filter" :options="!empty($filters['providerOptions']) ? $filters['providerOptions'] : []"
                                class="w-full !bg-neutral-700 !text-white max-xl:min-w-[178px] max-xl:max-w-[100%] max-[380px]:min-w-[148px] xl:max-w-[200px]"
                                :value="$currentProvider" placeholder="Nhà cung cấp" placeholder_icon="icon-provider-all" />
                        </div>
                    @endif
                    @if (!empty($filters['sortOptions']) && !$isFavorite)
                        <div id="game-sort"
                            class="w-full max-xl:flex-1 max-[380px]:max-w-[120px] xl:hidden xl:max-w-[200px]">
                            <x-kit.dropdown id="sort-filter" :options="!empty($filters['sortOptions']) ? $filters['sortOptions'] : []"
                                class="min-w-[200px] !bg-neutral-700 !text-white max-xl:min-w-[120px] max-[380px]:min-w-[110px]"
                                :value="!empty($activeFilter['filter']) ? $activeFilter['filter'] : 'all'" />
                        </div>
                    @endif
                    <div id="search-input" @class(['max-xl:shrink-0 xl:max-w-[200px] xl:flex-1'])>
                        <div class="relative">
                            <div id="searchToggle"
                                class="hidden h-[40px] w-[44px] cursor-pointer items-center justify-center gap-[6px] rounded-[8px] border border-[#23413A] bg-neutral-700 px-[12px] py-[8px] text-[#8C9D98] max-xl:flex">
                                <img src="{{ asset('asset/images/icons/icon-search.svg') }}" alt="icon"
                                    class="h-full w-full" />
                            </div>
                            <div id="searchKeywordWrapper" class="max-xl:hidden">
                                <div class="flex items-center justify-center gap-[8px]">
                                    <x-kit.search-input size="large" id="searchKeyword" showClose
                                        placeholder="{{ __('common.search') }}" suffixIconId="clearKeyword"
                                        prefixIcon="{{ asset('asset/images/icons/icon-search.svg') }}" maxLength="20" />
                                    <div
                                        class="close-search flex h-[40px] w-[40px] shrink-0 items-center justify-center gap-[10px] rounded-[8px] bg-[#23413A] p-[8px] xl:hidden">
                                        <i class="icon-close text-[24px] text-white"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="game-container-element" @class(['order-2', 'hidden' => empty($games)])>
                <div class="space-y-2 xl:pb-0">
                    <div class="w-full pb-5">
                        <div class="space-y-3">
                            <div class="space-y-[6px] xl:space-y-2">
                                <div id="game-container"
                                    class="grid lg:grid-cols-6 2xl:grid-cols-4 3xl:grid-cols-6 gap-3 4xl:gap-[20px] md:grid-cols-4 sm:grid-cols-3 grid-cols-2">
                                    @foreach ($games as $game)
                                        @if ($lobbyType === 'casino' && !$isFavorite && $loop->first && $isMobile && request()->limit % 2 !== 0)
                                            <div>
                                                <x-ui.card name="{{ $game->name ?? 'title' }}"
                                                    partner="{{ $game->partner ?? '' }}" tags="{{ $game->tags ?? '' }}"
                                                    partner_txt="{{ $game->partner_txt ?? '' }}"
                                                    image="{{ $game->image ?? '' }}"
                                                    partner_provider="{{ $game->partner_provider ?? '' }}"
                                                    is_favorite="{{ $game->is_favorite ?? false }}"
                                                    data-api="{{ $game->api_url ?? '' }}"
                                                    id="{{ $game->partner_game_id ?? '' }}"
                                                    lobbyType="{{ $lobbyType }}"
                                                    deny_info="{{ $game->deny_info ?? false }}" isInlineInfo="true"
                                                    maintain="{{ $game->maintain ?? false }}"
                                                    jackpot="{{ $game->jackpot ?? 0 }}"
                                                    imageClass="max-xl:aspect-[306/164] max-xl:min-w-full"
                                                    class="loader-image-transparent flex flex-col items-center text-marron"
                                                    table_id="{{ $game->table_id ?? '' }}"
                                                    />
                                            </div>
                                        @else
                                            <x-ui.card name="{{ $game->name ?? 'title' }}"
                                                partner="{{ $game->partner ?? '' }}"
                                                partner_txt="{{ $game->partner_txt ?? '' }}"
                                                image="{{ $game->image ?? '' }}" tags="{{ $game->tags ?? '' }}"
                                                partner_provider="{{ $game->partner_provider ?? '' }}"
                                                is_favorite="{{ $game->is_favorite ?? false }}"
                                                data-api="{{ $game->api_url ?? '' }}"
                                                id="{{ $game->partner_game_id ?? '' }}"
                                                lobbyType="{{ $lobbyType }}"
                                                deny_info="{{ $game->deny_info ?? false }}"
                                                maintain="{{ $game->maintain ?? false }}"
                                                jackpot="{{ $game->jackpot ?? 0 }}"
                                                class="loader-image-transparent flex flex-col items-center text-marron"
                                                table_id="{{ $game->table_id ?? '' }}"
                                                />
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div @class([
                    'hidden' => count($games) >= $totalGames,
                    'flex justify-center text-center',
                ])>
                    <button id="loadmore" type="button"
                        class="mx-auto flex h-[40px] min-w-[180px] items-center justify-center gap-[6px] rounded-[8px] bg-[#6CFE00] px-[18px] py-[10px] text-[14px] font-[500] leading-[18px] text-[#00120C]">
                        {{ __('common.loadmore') }}
                        <span>(<span id="loadmore-counter">{{ count($games) }}/{{ $totalGames }}</span>)</span>
                    </button>
                </div>
            </div>
            <div id="empty-game-element" @class(['order-2', 'hidden' => !empty($games)])>
                <x-ui.empty-game :$keyword :isFavoriteEmpty="$isFavoriteEmpty" :isRecentEmpty="$isRecentEmpty" :isSearchEmpty="$isSearchEmpty" />
            </div>
            <div id="loading-spinner-element" class="loading-iframe order-2 hidden">
                <x-ui.loading-iframe />
            </div>
            <div id="loading-loadmore" class="order-2 hidden h-[48px]">
                <div class="flex items-center justify-center">
                    <img src="{{ asset('asset/images/spinner.svg') }}" alt="loading">
                </div>
            </div>
        </div>
        @vite(['resources/js/game-filters.js'])
    </div>

</div>

@push('scripts')
    @once
        <script src="{{ asset('asset/js/lib/nanoplayer.4.min.js') }}"></script>
    @endonce
    <script>
        const filters = @json($filters);
        const games = @json($games);
        const activeFilter = {
            ...@json($activeFilter),
            limit: {{ $activeFilter['limit'] }}
        };
        const casinoLiveStream = @json(config('constants.gameLiveStreamCasino'));
        const gameIdCasinoLiveStream = casinoLiveStream.map(item => item.gameId);

        window.addEventListener("load", (event) => {
            const createElement = (game, isLobbyCasinoMbFirstItem = false) => {

                const jackpotId = game.partner === 'rik' || game.partner === 'b52' || game.partner === 'go' ||
                    game.partner === '789club' ?
                    game.partner + '_' + game.partner_game_id :
                    game.partner_game_id;

                if (isLobbyCasinoMbFirstItem) {
                    if (game.is_favorite) {
                        return `
                        <div>
                            <x-ui.card
                                name="${game.name}"
                                partner="${game.partner}"
                                partner_txt="${game.partner_txt}"
                                is_favorite="${Number(game.is_favorite)}"
                                partner_provider="${game.partner_provider}"
                                image="${game.image}" data-api="${game.api_url}"
                                id="${game.partner_game_id}"
                                lobbyType="{{ $lobbyType }}"
                                isRenderLiveCasino
                                liveCasinoGameId="${game.partner_provider}_${game.partner_game_id}"
                                tags="${game.tags}"
                                deny_info="${game.deny_info}"
                                jsJackpotId="${jackpotId}"
                                data-maintain="${game.maintain ?? ''}"
                                isInlineInfo
                                imageClass="max-xl:aspect-[306/164] max-xl:min-w-full"
                                class="flex flex-col items-center text-marron loader-image-transparent"
                                table_id="{{ $game->table_id ?? '' }}"
                            ></x-ui.card>
                        </div>
                        `
                    }
                    return `
                    <div>
                        <x-ui.card
                            name="${game.name}"
                            partner="${game.partner}"
                            partner_txt="${game.partner_txt}"
                            partner_provider="${game.partner_provider}"
                            image="${game.image}" data-api="${game.api_url}"
                            id="${game.partner_game_id}"
                            lobbyType="{{ $lobbyType }}"
                            isRenderLiveCasino
                            liveCasinoGameId="${game.partner_provider}_${game.partner_game_id}"
                            tags="${game.tags}"
                            deny_info="${game.deny_info}"
                            jsJackpotId="${jackpotId}"
                            data-maintain="${game.maintain ?? ''}"
                            isInlineInfo
                            imageClass="max-xl:aspect-[306/164] max-xl:min-w-full"
                            class="flex flex-col items-center text-marron loader-image-transparent"
                            table_id="{{ $game->table_id ?? '' }}"
                        ></x-ui.card>
                    </div>
                    `
                }
                // live stream game
                if (gameIdCasinoLiveStream.includes(`${game.partner_provider}_${game.partner_game_id}`)) {
                    game.tags = 'live';
                    if (game.is_favorite) {
                        return `
                        <x-ui.card
                            name="${game.name}"
                            partner="${game.partner}"
                            partner_txt="${game.partner_txt}"
                            is_favorite="${Number(game.is_favorite)}"
                            partner_provider="${game.partner_provider}"
                            image="${game.image}"
                            data-api="${game.api_url}"
                            id="${game.partner_game_id}"
                            lobbyType="{{ $lobbyType }}"
                            tags="${game.tags}"
                            deny_info="${game.deny_info}"
                            jsJackpotId="${jackpotId}"
                            data-maintain="${game.maintain ?? ''}"
                            isRenderLiveCasino
                            liveCasinoGameId="${game.partner_provider}_${game.partner_game_id}"
                            class="flex flex-col items-center text-marron loader-image-transparent">
                            table_id="{{ $game->table_id ?? '' }}"
                        </x-ui.card>`;
                    }
                    return `
                    <x-ui.card
                        name="${game.name}"
                        partner="${game.partner}"
                        partner_txt="${game.partner_txt}"
                        partner_provider="${game.partner_provider}"
                        image="${game.image}"
                        data-api="${game.api_url}"
                        id="${game.partner_game_id}"
                        lobbyType="{{ $lobbyType }}"
                        tags="${game.tags}"
                        deny_info="${game.deny_info}"
                        jsJackpotId="${jackpotId}"
                        data-maintain="${game.maintain ?? ''}"
                        isRenderLiveCasino
                        liveCasinoGameId="${game.partner_provider}_${game.partner_game_id}"
                        class="flex flex-col items-center text-marron loader-image-transparent">
                        table_id="{{ $game->table_id ?? '' }}"
                    </x-ui.card>`;
                }
                if (game.is_favorite) {
                    return `<x-ui.card name="${game.name}" partner="${game.partner}" partner_txt="${game.partner_txt}" is_favorite="${Number(game.is_favorite)}" partner_provider="${game.partner_provider}"
                image="${game.image}" data-api="${game.api_url}" id="${game.partner_game_id}" lobbyType="{{ $lobbyType }}" tags="${game.tags}" deny_info="${game.deny_info}" jsJackpotId="${jackpotId}" data-maintain="${game.maintain ?? ''}" class="flex flex-col items-center text-marron loader-image-transparent" table_id="${game.table_id}" ></x-ui.card>`;
                }
                return `<x-ui.card name="${game.name}" partner="${game.partner}" partner_txt="${game.partner_txt}" partner_provider="${game.partner_provider}"
            image="${game.image}" data-api="${game.api_url}" id="${game.partner_game_id}" lobbyType="{{ $lobbyType }}" tags="${game.tags}" deny_info="${game.deny_info}" jsJackpotId="${jackpotId}" data-maintain="${game.maintain ?? ''}" class="flex flex-col items-center text-marron loader-image-transparent" table_id="${game.table_id}" ></x-ui.card>`;
            }

            const onCheckLiveStreamCasino = (game) => {
                const liveCasinoData = casinoLiveStream.find(item => item.gameId === game.partner_provider +
                    '_' + game.partner_game_id);
                if (liveCasinoData) {
                    const payload = {
                        divId: liveCasinoData?.gameId,
                        groupId: liveCasinoData?.id,
                        streamName: liveCasinoData?.key,
                        // site: 'z24cvb.s2z.mooo.com',
                        site: window.location.hostname,
                        options: {
                            playback: {
                                autoplay: true,
                                automute: true,
                                muted: true,
                                faststart: true,
                            },
                            style: {
                                controls: false,
                                fullScreenControl: false,
                                interactive: true,
                                poster: game?.image,
                                buttonCursor: 'pointer',
                                scaling: 'crop',
                                view: false,
                                width: 'auto',
                                height: 'auto',
                            },
                        },
                    }
                    window.verifyToken(payload)
                }
            }

            handlePageEvent({
                games,
                activeFilter,
                onCreateElement: createElement,
                onCheckLiveStreamCasino: onCheckLiveStreamCasino,
            })

            new Swiper('#recommend-games', {
                slidesPerView: 4,
                spaceBetween: 16,
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                navigation: {
                    nextEl: '#recommend-games-next',
                    prevEl: '#recommend-games-prev',
                },
                breakpoints: {
                    1200: {
                        slidesPerView: 4,
                        spaceBetween: 16,
                    },
                    768: {
                        slidesPerView: 4,
                        spaceBetween: 8,
                        grid: {
                            rows: 1,
                            fill: 'row',
                        },
                    },
                    360: {
                        slidesPerView: 2,
                        spaceBetween: 6,
                        grid: {
                            fill: 'row',
                            rows: 2,
                            loopedSlides: 8,
                        },
                    }
                },
            });
        });
    </script>
@endpush
