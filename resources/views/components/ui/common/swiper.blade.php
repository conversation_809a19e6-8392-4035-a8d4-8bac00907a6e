@props([
    'swiperClass' => '',
    'swiperContainerClass' => '',
    'swiperRequiredClass' => '',
    'swiperWrapperClass' => '',
    'swiperConfig' => [],
    'isHiddenNavigation' => false,
    'isOverflowVisible' => false,
    'paginationClass' => ''
])


@php
    // Simplifying class name handling for pagination and navigation
    $paginationClass = str_replace('.', '', $swiperConfig['pagination']['el'] ?? '');
    $nextNavigationClass = str_replace('.', '', $swiperConfig['navigation']['nextEl'] ?? '');
    $prevNavigationClass = str_replace('.', '', $swiperConfig['navigation']['prevEl'] ?? '');
@endphp

<div class="swiper-section relative {{ $swiperClass }}">
    <div class="swiper-container {{ $swiperContainerClass }} {{ $isOverflowVisible ? '' : 'overflow-hidden' }} {{ $swiperRequiredClass }}">
        <div class="swiper-wrapper {{ $swiperWrapperClass }}">
            {{ $slot }}
        </div>

        @if (!empty($swiperConfig['pagination']))
            <div class="swiper-pagination {{ $paginationClass }}"></div>
        @endif
    </div>
</div>

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const swiperClass = "{{ $swiperRequiredClass }}";
            const paginationClass = "{{ $paginationClass }}";
            const swiperConfig = {!! json_encode($swiperConfig) !!};

            const swiper = new Swiper(`.swiper-container.${swiperClass}`, {
                ...swiperConfig,
            });

            if (swiper) {
                switch (swiperClass) {
                    case 'filter-swiper':
                        const slides = swiper.slides;
                        slides.forEach((slide, index) => {
                            if ($(slide).hasClass('slide-actived')) {
                                swiper.slideTo(index);
                            }
                        });
                        break;
                    case 'overview-games-swiper':
                        window.overviewGamesSwiper = swiper;
                    case 'overview-category-mb-swiper':
                        window.overviewCategoryMbSwiper = swiper;
                    break;
                }
            }
        });
    </script>
@endpush
