@props([
    'key',
    'name',
    'image',
    'id',
    'table_id' => '',
    'partner',
    'partner_txt',
    'is_favorite',
    'partner_provider' => '',
    'isShowGameName' => true,
    'lobbyType' => 'game',
    'isInlineInfo' => false,
    'isHiddenGameName' => true,
    'imageClass' => '',
    'tags' => '',
    'favoriteOnTop' => false,
    'isRenderLiveCasino' => false,
    'liveCasinoGameId' => '',
    'deny_info' => false,
    'jsJackpotId' => '',
    'maintain' => false,
    'jackpot' => 0,
])

@php
    $gameLiveStreamCasino = collect(config('constants.gameLiveStreamCasino'));
    $isGameLiveStreamCasino = $gameLiveStreamCasino->pluck('gameId')->contains($partner_provider . '_' . $id);
    $liveCasinoData = null;
    $providerLogo = null;
    if ($isGameLiveStreamCasino) {
        $liveCasinoData = $gameLiveStreamCasino->where('gameId', $partner_provider . '_' . $id)->first();
        $providerLogo = '/asset/images/icons/' . strtolower($partner_provider) . '.avif';
    }

    $jackpotId = in_array($partner, config('games.jackpotPartner')) ? $partner . '_' . $id : $id;
    $jackpotId = $jsJackpotId ? $jsJackpotId : $jackpotId;
    $maintain = $maintain ?? false ? 'true' : '';
    $image = html_entity_decode($image);
    $specialJackpot = config('constants.special_jackpot');
    $isSpecialJackpot = collect(array_keys($specialJackpot))->contains($id);
@endphp
<div class="game-item js-game-item group relative z-[10] cursor-pointer overflow-hidden rounded-xl bg-black-800"
    data-key="{{ $partner }}_{{ $id }}" data-jackpotId="{{ $jackpotId }}" data-name="{{ $name }}"
    data-image="{{ $image }}" data-partner-provider="{{ $partner_provider }}" data-id="{{ $id }}"
    data-lobby-type="{{ $lobbyType }}" data-deny-info="{{ $deny_info }}" data-maintain="{{ $maintain }}"
    onclick="window.handleGetGameUrl(this, event)">

    <div class="video-wrapper relative z-0 aspect-[358/204] xl:aspect-[392/233] size-full"
        id="{{ isset($liveCasinoData) ? $liveCasinoData->gameId : $liveCasinoGameId }}">
        <img src="{{ $image }}" alt="" class="absolute inset-0 z-[-1] size-full !object-cover"
            onerror="this.src ='/asset/images/game-default.png'">
    </div>

    <div class="left-2 top-2 absolute flex items-center gap-2">
        <div class="grid size-[26px] xl:size-[30px] cursor-pointer place-content-center rounded bg-[#00000080] text-[26px] xl:text-[30px] leading-[1]"
            onclick="event.stopPropagation();window.toggleFavorite('{{ $id }}', '{{ $table_id }}', '{{ $partner_provider }}', '{{ $name }}', '{{ empty($is_favorite) ? 'false' : 'true' }}', '{{ $lobbyType }}', this, event)">
            <i @class([
                empty($is_favorite) ? 'icon-unfavorite' : 'icon-favorite-gradient',
                'hover:text-[#FFE100] transition-all duration-300 block',
            ])></i>
            <div class="game-item__favorite-loading hidden">
                <img @class(['size-5 max-xl:size-[14px]']) src="{{ asset('asset/images/spinner.svg') }}" alt="loading">
            </div>
        </div>
        <div class="js-toggle-sound grid size-[26px] xl:size-[30px] cursor-pointer place-content-center rounded bg-[#00000080]">
            <i class="icon-volume-mute text-[20px] xl:text-[24px] font-normal text-white"></i>
        </div>
    </div>
    <div class="right-2 top-2 absolute">
        <div
            class="flex h-6 items-center gap-4 rounded-full bg-[#00000080] px-2 text-xs font-medium leading-6 text-neutral backdrop-blur-sm">
            <div class="flex items-center gap-1">
                <div class="live-indicator">
                    <div class="live-dot">
                        <span class="wave"></span>
                        <span class="wave"></span>
                    </div>
                </div>
                <icon class="text-xs font-medium leading-6 text-white uppercase">Live</icon>
            </div>
            <div @class([
                'game-item__viewers flex hidden items-center gap-2 leading-[1] text-xs font-medium text-neutral relative',
                "before:content-[''] before:absolute before:w-[1px] before:h-4 before:bg-neutral-180 before:-left-2 before:top-1/2 before:-translate-y-1/2",
            ])>
                <i class="icon-people text-[14px] leading-5"></i>
                <span class="js-viewers js-{{ $partner }}-viewers leading-6"
                    data-partner-provider="{{ $partner_provider }}"
                    data-id="{{ $id }}">{{ $gameList[0]->viewers ?? 0 }}</span>
            </div>

            <div @class([
                'js-viewers-wrapper game-item__viewers flex hidden items-center gap-1 text-xs font-medium text-neutral relative',
                "before:content-[''] before:absolute before:w-[1px] before:h-4 before:bg-[#3E4049] before:-left-2 before:top-1/2 before:-translate-y-1/2",
            ])>
                <i class="icon-people text-[14px] leading-5"></i>
                <span class="js-viewers js-{{ $partner }}-viewers"
                    data-partner-provider="{{ $partner_provider }}" data-id="{{ $id }}">0</span>
            </div>


        </div>
        <div class="mt-1">
            <img class="object-contain w-auto h-6 ml-auto" src="{{ $providerLogo }}"
                alt="{{ $partner_provider }}" onerror="this.parentElement.style.display='none'">
        </div>
    </div>

    <div
        class="game-item__bottom absolute bottom-0 left-0 right-0 flex justify-between p-2 text-[14px] leading-[18px] text-white">
        <div class="game-item__left mt-auto">
            <div class="game-item__title mb-1.5 text-[14px] xl:text-[16px] font-medium leading-[18px] xl:leading-5">
                {{ $name }}
            </div>
            @if ($isSpecialJackpot)
                <div
                    class="inline-flex h-[26px] w-[128px] items-center gap-1.5 rounded-lg bg-[#00000080] px-1.5 whitespace-nowrap backdrop-blur-sm">
                    <span class="text-xs font-medium text-functional-yellow">Cơ hội thắng</span>
                    <div class="animate-brightness h-[18px]">
                        <img src="{{ asset('asset/images/livecasino/'.($specialJackpot[$id] ?? 'x999') .'.avif') }}" alt="x30"
                            class="h-full w-auto object-cover">
                    </div>
                </div>
            @else
                <div
                    class="game-item__jackpot js-jackpot-value-{{ $jackpotId }} {{ $jackpot ? 'flex' : 'hidden' }} mt-1.5 flex w-max min-w-max items-center gap-1.5 rounded-lg bg-[#00000080] px-3 py-1 text-[14px] font-medium leading-[18px]">
                    <img src="{{ asset('asset/images/icons/icon-jackpot.png') }}" alt="jackpot"
                        class="size-4 object-contain">
                    <span class="js-jackpot-value text-functional-yellow">
                        {{ formatAmount(roundDownToNearest($jackpot)) }}
                    </span>
                </div>
            @endif
        </div>
    </div>

    <div class="bottom-2 right-2 absolute">
        <x-kit.button class="!mt-0 h-[28px] w-[87px] rounded-lg text-xs font-medium capitalize">
            Chơi ngay
        </x-kit.button>
    </div>
</div>
@push('scripts')
    @once
        <script src="{{ asset('asset/js/lib/nanoplayer.4.min.js') }}"></script>
    @endonce
    @if ($liveCasinoData)
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const payload = {
                    divId: @json($liveCasinoData).gameId,
                    groupId: @json($liveCasinoData).id,
                    streamName: @json($liveCasinoData).key,
                    // site: 'z17vb.s2z.chickenkiller.com',
                    site: window.location.hostname,
                    options: {
                        playback: {
                            autoplay: true,
                            automute: true,
                            muted: true,
                            faststart: true,
                        },
                        style: {
                            controls: false,
                            fullScreenControl: false,
                            interactive: true,
                            poster: @json($image),
                            buttonCursor: 'pointer',
                            scaling: 'crop',
                            view: false,
                            width: 'auto',
                            height: 'auto',
                        },
                    },
                }
                window.verifyToken(payload)
            });
        </script>
    @endif
@endpush