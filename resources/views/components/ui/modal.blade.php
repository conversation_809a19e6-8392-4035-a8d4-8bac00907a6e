@props([
    'id' => '',
    'header' => '',
    'modalClass' => '',
    'modalContainerClass' => '',
    'modalContentClass' => '',
    'showCloseButton' => true,
    'actionCloseModal' => '',
])

<div
    id="{{ $id }}"
    @class([
        'fixed inset-0 z-[200] flex items-center justify-center bg-black bg-opacity-70 overflow-y-auto overscroll-contain',
        $modalClass
    ])
>
    <div
        id="{{ $id }}-container"
        @class([
            'bg-gradient-auth-stroke p-[24px] rounded-2xl overflow-hidden shadow-lg max-w-[586px] w-full relative h-max',
            "before:absolute before:bg-[url('/public/asset/images/backgrounds/popup-bg.avif')] before:inset-[1px] max-xl:before:inset-[1px] before:bg-neutral before:content-[''] before:rounded-[inherit] before:pointer-events-none before:bg-full before:bg-no-repeat before:bg-center",
            "max-xl:px-5 max-xl:py-[32px]",
            $modalContainerClass
        ])
    >
        @if ($showCloseButton)
        <x-kit.icon-button
            id="{{ $id }}-closeModalBtn"
            type="button"
            class="absolute right-[10px] top-[10px] xl:w-[32px] xl:h-[32[px] z-[11] bg-neutral-600 xl:hover:bg-neutral-500"
            onclick="{{ $actionCloseModal ? $actionCloseModal : 'closeModal(\'' . $id . '-closeModalBtn\', ' . ($id == 'login-signup-modal' ? 'true' : 'false') . ')' }}"
        >
            <i class="icon-close text-[20px] text-neutral"></i>
        </x-kit.icon-button>
        @endif
        <div>
            {{ $slot }}
        </div>
    </div>
    @stack('modalScripts')
</div>
