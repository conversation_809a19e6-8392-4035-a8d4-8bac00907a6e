@php
    $brandName = config('app.brand_name');
@endphp
<div class="flex flex-col gap-[24px] max-xl:gap-[16px] [&_strong]:font-semibold">
    <div>
        <div class="mb-[20px] text-[16px] font-[600] leading-[20px] text-white capitalize">Ch<PERSON>i thử rút thật - Nhận ngay 58K miễn phí cho tân thủ</div>
        <p class="text-[14px] font-[600]"><span class="font-[400] text-[#AFBBB7]">Thời hạn áp dụng:</span> Từ 09/06/2025 đến khi có thông báo mới.</p>
        <p class="text-[14px] font-[600]">
            <span class="font-[400] text-[#AFBBB7]"><PERSON><PERSON><PERSON> tượng áp dụng:</span> Tất cả các khách hàng của {{ $brandName }}
        </p>
        <div class="mt-[30px] mb-[8px] text-[16px] font-[600] leading-[20px] text-white capitalize">
        Tr<PERSON><PERSON> nghiệm cá cược cực dễ – Rút tiền nhanh gọn, không cần đợi!</div>
        <ul class="list-disc text-[14px] font-[400] leading-[18px] text-[#AFBBB7]">
            <li class="ml-[25px] pb-[4px]">Nhận code miễn phí 58K chỉ với vài bước đơn giản, không tốn chi phí – cơ hội vàng để thử vận may!</li>
            <li class="ml-[25px] pb-[4px]">
                Nhận code hàng ngày tại 
                  <span onclick="openLiveChat()" class="text-[#5FBCFF] cursor-pointer">Livechat</span>

                 hoặc   <a href="https://t.me/CSKH_FO88" target="_blank" class="text-[#5FBCFF]"> Telegram</a>
            </li>
        </ul>
    </div>
    <div>
        <div class="mb-[8px] text-[16px] font-[600] leading-[20px] text-white capitalize">Code to - rút lớn</div>
        <ul class="list-disc text-[14px] font-[400] leading-[18px] text-[#AFBBB7]">
            <li class="ml-[25px] pb-[4px] [&_strong]:text-white">
                Áp dụng cho tất cả các game trên trang.
            </li>
            <li class="ml-[25px] pb-[4px] [&_strong]:text-white">
                Áp dụng với hoàn trả 1,6%.
            </li>
        </ul>
    </div>
    <div>
        <div class="mb-[8px] text-[16px] font-[600] leading-[20px] text-white capitalize">hướng dẫn nhận code 58K</div>
        <div class="w-full bg-[#00221B] text-white rounded-lg overflow-hidden border border-[#465F59]">
          <table class="w-full text-sm text-left border border-[#465F59] rounded-lg border-collapse p-4">
            <thead class="bg-[#12312B]">
              <tr>
                <th colspan="2" class="text-left font-semibold text-white p-3 border border-[#465F59]">
                  Mỗi Người Chơi Chỉ Được Nhận 1 Code Duy Nhất – Hoàn Toàn MIỄN PHÍ!
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="py-4 px-3 font-medium border border-[#465F59] w-[200px]">Bước 1</td>
                <td class="py-4 px-3 border border-[#465F59]">
                  Follow fanpage: <a href="https://fb.me/Fo88official" class="text-[#5FBCFF]">fb.me/Fo88official</a>.
                </td>
              </tr>
              <tr>
                <td class="py-4 px-3 font-medium border border-[#465F59] w-[200px]">Bước 2</td>
                <td class="py-4 px-3 border border-[#465F59]">
                  Đăng ký tài khoản + Xác thực số điện thoại & tài khoản ngân hàng chính chủ.
                </td>
              </tr>
              <tr>
                <td class="py-4 px-3 font-medium border border-[#465F59] w-[200px]">Bước 3</td>
                <td class="py-4 px-3 border border-[#465F59]">
                  Gửi ảnh chụp đã hoàn tất các bước trên qua <span onclick="openLiveChat()" class="text-[#5FBCFF] cursor-pointer">Livechat</span> – <a href="https://fb.me/Fo88official" class="text-[#5FBCFF]">Fanpage</a> hoặc <a href="https://t.me/CSKH_FO88" target="_blank" class="text-[#5FBCFF]">Telegram</a>.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
    </div>

    <div>
        <div class="mt-[12px] mb-[8px] text-[16px] font-[600] leading-[20px] text-white capitalize">điều kiện rút tiền</div>
        <ul class="list-disc text-[14px] font-[400] leading-[18px] text-[#AFBBB7]">
            <li class="ml-[25px] pb-[4px] [&_strong]:text-white">
                Hoàn thành 3 vòng cược.
            </li>
            <li class="ml-[25px] pb-[4px] [&_strong]:text-white">
                Tổng cược = Tiền thưởng x 3.
            </li>
            <li class="ml-[25px] pb-[4px] [&_strong]:text-white">
                Bắt buộc xác thực SĐT và TKNH chính chủ.
            </li>
            <li class="ml-[25px] pb-[4px] [&_strong]:text-white">
                Không thay đổi tài khoản ngân hàng trong suốt thời gian tham gia khuyến mãi.
            </li>
        </ul>
        <p class="my-[20px] text-[14px] font-[400] leading-[18px] text-[#AFBBB7]">Lưu ý: {{ $brandName }} có quyền điều chỉnh hoặc hủy bỏ ưu đãi nếu phát hiện hành vi gian lận hoặc vi phạm quy định.</p>
    </div>

    <div>
        <div class="mb-[8px] text-[16px] font-[600] leading-[20px] text-white capitalize">thưởng miễn phí 58K - ngại gì không nhận?</div>
        <p class="my-[20px] text-[14px] font-[400] leading-[18px] text-[#AFBBB7] capitalize">Đăng ký ngay – Cơ hội thử vận may không thể dễ hơn!</p>
    </div>

    <div class="max-w-[839px] text-center">
                        <button role="button"
                            class="h-[56px] min-w-[202px] gap-[4px] rounded-[8px] bg-[#6CFE00] px-[18px] py-[6px] hover:opacity-90"
                            onclick="handleButtonClick()">
                            <div>
                                <div class="text-[18px] font-[700] leading-[21.78px] text-[#00120C]">
                                    @if(Auth::check())
NẠP TIỀN NGAY
                                    @else
ĐĂNG KÝ NGAY
                                    @endif
                                </div>
                                <div class="mt-[4px] text-[14px] font-[400] leading-[18px] text-[#00120C]">
                                   @if(Auth::check())
Nhận khuyến mãi khủng
                                    @else
Nhận code khủng
                                    @endif
                                </div>
                            </div>
                        </button>
                    </div>

    @push('scripts')
        <script>
            function handleButtonClickJoin() {
                @if (Auth::check())
                    window.location.href = '/account/deposit';
                @else
                    signup();
                @endif
            }
        </script>
    @endpush
</div>
