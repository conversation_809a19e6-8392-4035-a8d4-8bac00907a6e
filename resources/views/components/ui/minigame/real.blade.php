@php
    $user = Auth::user();
    $token = $user?->tp_token;
    $partner = config('app.brand_name');
    $KM_100 = 'WELCOME';
    $minigameToken = '';
    $minigameUrl = env('MINIGAME_URL') ?? 'https://assets.vgjt.info/js/mn.js';

    if ($token) {
        $minigameToken = $token;
    }
@endphp

<div class="relative z-[105]">
    <c2-minigame token="{{ $minigameToken }}" partner="{{ $partner }}" pos />
</div>

@push('scripts')
    <script>
        function getPosition() {
            const width = window.innerWidth;
            if (width >= 1600) {
                return 'auto 10px 30% auto';
            } else if (width >= 1200) {
                return 'auto 10px 20% auto';
            }
            return 'auto 10px 280px auto';
        }

        function forceRerender() {
            const scriptId = 'vergopjt-script';
            const scriptList = document.querySelectorAll("script[type='text/javascript']");
            const convertedNodeList = Array.from(scriptList);
            const testScript = convertedNodeList.find(script => script.id === scriptId);
            if (testScript !== undefined) {
                testScript.parentNode.removeChild(testScript);
            }

            const script = document.createElement('script');
            script.id = scriptId;
            script.type = 'text/javascript';
            script.src = '{{ $minigameUrl }}';
            document.body.appendChild(script);

            setTimeout(() => {
                const listHead = document.getElementsByTagName('head');
                if (listHead.length < 0) {
                    return;
                }
                const head = listHead[0];
                const viewport = head.querySelector("[name='viewport']");
                head.removeChild(viewport);
            }, 5000);
        }

        document.addEventListener('DOMContentLoaded', function() {
            const minigameElement = document.querySelector('c2-minigame');
            if (minigameElement) {
                minigameElement.setAttribute('pos', getPosition());

                const addGameBoxClickListener = () => {
                    const gameBox = document.querySelector('.gamebox');
                    if (gameBox) {
                        gameBox.addEventListener('click', function(e) {
                            if (!isUpdatedFullname) {
                                const hoverElement = e.target.closest('.c2-mn-hover');
                                if (hoverElement) {
                                    e.stopImmediatePropagation();
                                    e.preventDefault();
                                    updateFullname();
                                }
                            }
                        }, true);
                        observer.disconnect();
                    } 
                }
                const observerConfig = {
                    childList: true, // Quan sát việc thêm/xóa các phần tử con trực tiếp
                    subtree: true,   // Quan sát các thay đổi trong toàn bộ cây con của minigameElement
                };
                const observer = new MutationObserver((mutationsList, observer) => {
                    addGameBoxClickListener();
                });

                observer.observe(minigameElement, observerConfig);
            }

            forceRerender();
        });
        // Overight draggable minigame
        window.addEventListener("load", () => {
            const targetNodes = document.querySelectorAll('div[draggable="true"]');
            targetNodes.forEach(targetNode => {
                const elementHeight = targetNode?.clientHeight ?? 112;
                if (targetNode) {
                    const config = {
                        attributes: true,
                        attributeFilter: ['style']
                    };

                    const callback = function(mutationsList, observer) {
                        const rect = targetNode.getBoundingClientRect();
                        let headerHeight = 56;
                        let leftOffset = 0;
                        if (isMobile()) {
                            leftOffset = 0;
                        }
                        if (rect.left >= window.innerWidth - (rect?.width ?? 100)) {
                            const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
                            targetNode.style.left = `${window.innerWidth - rect.width - scrollbarWidth}px`;
                        }
                        if (rect.left <= leftOffset) {
                            if (rect?.width + leftOffset > window.innerWidth) {
                                targetNode.style.left = `${(window.innerWidth - rect.width) / 2}px`;
                            } else {
                                targetNode.style.left = `${leftOffset}px`;
                            }
                        }
                        if (rect.top <= headerHeight) {
                            targetNode.style.top = `${headerHeight}px`;
                        }
                        if (isMobile() && rect.top + rect.height >= window.innerHeight - 56) {
                            targetNode.style.top = `${window.innerHeight - rect.height - 56}px`;
                        } else if (rect.top + rect.height >= window.innerHeight) {
                            targetNode.style.top = `${window.innerHeight - rect.height}px`;
                        }
                    };

                    const observer = new MutationObserver(callback);
                    observer.observe(targetNode, config);
                } else {
                    console.error("Target node not found");
                }
            })
        });
    </script>
@endpush
