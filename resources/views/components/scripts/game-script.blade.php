<script>
    const isWelcomePromotionDenyInfo = [2, 6] // id 6 km 200%
    const isTechPlayGame = (partner_game_id) => {
        const MIN_BALANCE_FOR_TECHPLAY = {{ config('games.minBalanceForTechPlay') }}
        const TECHPLAY_GAMES = @json(config('games.techPlayGames'));
        return (
            user?.balance < MIN_BALANCE_FOR_TECHPLAY &&
            TECHPLAY_GAMES.includes(partner_game_id)
        )
    }

    async function handleGetGameUrl(target, event = null) {
        event?.stopPropagation();
        event?.preventDefault();

        const itemCard = $(target)?.closest('.js-game-item');

        if (event?.target?.classList?.contains('js-toggle-sound') || event?.target?.closest('.js-toggle-sound')) {
            return;
        }
        const partner_provider = target?.partner_provider || target?.dataset?.partnerProvider;
        const partner_game_id = target?.partner_game_id || target?.dataset?.id || (() => {
            const apiUrl = target?.dataset?.apiUrl;
            if (!apiUrl) return null;
            return new URLSearchParams(apiUrl.split('?')[1]).get('gId');
        })();
        const name = target?.name || target?.dataset?.name;
        const image = target?.image || target?.dataset?.image;
        const lobbyType = target?.lobbyType || target?.dataset?.lobbyType;
        const denyInfo = target?.denyInfo || (target?.dataset?.denyInfo ?? '');
        const maintain = target?.maintain || (target?.dataset?.maintain ?? '');

        // If target has class disabled, return
        if ($(target)?.hasClass('disabled')) {
            return;
        }

        const PROVIDER_OPEN_HTML_TYPE = @json(config('games.providerOpenHtmlType'));

        if (!isLoggedIn) {
            login()
            return
        }

        if (denyInfo && denyInfo !== 'false') {
            showGameDenyModal()
            return
        }

        const fullname = user.fullname;
        const masked = fullname.substring(0, 4) + "****";

        if(!isUpdatedFullname && isLoggedIn) {
            updateFullname()
            return
        }

        if (maintain === 'true') {
            showGameMaintenanceModal(lobbyType);
            return;
        }

        const toSend = lobbyType === 'casino' ? {
            partnerGameId: partner_game_id,
            partnerCode: partner_provider,
            partnerGameName: name,
        } : {
            partnerProvider: partner_provider,
            partnerGameId: partner_game_id,
            name,
            image,
        };

        const handler = () => {
            try {
                setTimeout(() => {
                    openNewTab(
                        '',
                        async () => {
                                itemCard.addClass('is-item-loading');
                                const {
                                    data
                                } = await getGameUrl(toSend, lobbyType)
                                itemCard.removeClass('is-item-loading');

                                if (
                                    PROVIDER_OPEN_HTML_TYPE.includes(toSend.partnerProvider) &&
                                    (data?.url?.status && data?.url?.status !== 'OK')
                                ) {
                                    showGameMaintenanceModal(lobbyType)
                                    return ''
                                }
                                if (data?.url) {
                                    return data?.url
                                }
                                return ''
                            },
                            PROVIDER_OPEN_HTML_TYPE.includes(toSend.partnerProvider)
                    )
                })
            } catch (error) {
                Toast({
                    title: 'Lỗi',
                    message: error.message ?? 'Có lỗi xảy ra khi mở game, vui lòng thử lại sau',
                    type: 'error',
                })
            }
        }

        if (!isUpdatedFullname) {
            updateFullname(handler);
            return;
        }

        handler()
    }
    window.handleGetGameUrl = handleGetGameUrl

    async function showDepositPlay() {
        window.dispatchEvent(
            new CustomEvent("swal:confirm", {
                detail: {
                    title: 'Số dư không đủ',
                    html: `Số dư hiện tại của bạn không đủ.<br/> Vui lòng nạp thêm tiền để trải nghiệm trò chơi tại ${globalBrandName}`,
                    icon: 'success',
                    confirmButtonText: 'Trang Chủ',
                    customClass: {
                        icon: 'deposit-error',
                    },
                    cancelButtonText: 'Nạp Tiền',
                    cancelButtonFunc: async () => {
                        window.location.href = '/account/deposit';
                    },
                    confirmButtonFunc: async () => {
                        window.location.href = '/';
                    },
                },
            })
        );
    }

    function showGameDenyModal(message = 'Bạn không thể chơi game này do đang sử dụng khuyến mãi.') {
        window.dispatchEvent(
            new CustomEvent("swal:confirm", {
                detail: {
                    title: 'Game không chơi được',
                    html: message,
                    icon: 'success',
                    confirmButtonText: 'Trang Chủ',
                    customClass: {
                        icon: 'game-error',
                    },
                    cancelButtonText: 'Xem Khuyến Mãi',
                    cancelButtonFunc: async () => {
                        window.location.href = '/account/promotion';
                    },
                    confirmButtonFunc: async () => {
                        window.location.href = '/';
                    },
                },
            })
        );
    }

    function showGameMaintenanceModal(lobbyType = 'game') {
        window.dispatchEvent(
            new CustomEvent("swal:confirm", {
                detail: {
                    title: 'Game đang bảo trì',
                    html: `Game này hiện đang được bảo trì. Vui lòng chọn trò chơi khác.`,
                    icon: 'success',
                    confirmButtonText: 'Trang Chủ',
                    customClass: {
                        icon: 'game-warning',
                    },
                    cancelButtonText: lobbyType === 'sport' ? 'Thể Thao' : 'Sảnh Game',
                    cancelButtonFunc: async () => {
                        window.location.href = lobbyType === 'casino' ? '/live-casino' :
                            lobbyType === 'sport' ? '/ca-cuoc-the-thao' :
                            '/cong-game';
                    },
                    confirmButtonFunc: async () => {
                        window.location.href = '/';
                    },
                },
            })
        );
    }

    async function getGameUrl(queryParams, lobbyType = 'game') {
        let urlApi = lobbyType === 'casino' ? '/api/v1/casinoUrl' : '/api/v1/gameUrl'

        const fetchUrl = `${urlApi}?${new URLSearchParams(queryParams).toString()}`
        const res = await fetchData(fetchUrl);
        if (res?.status === 'SHOW_MESSAGE') {
            showGameDenyModal()
            return {
                data: null,
            }
        }
        if (!res?.data?.url) {
            showGameMaintenanceModal(lobbyType)
            return {
                data: null,
            }
        }
        return {
            data: res?.data,
        }
    }

    window.addEventListener("load", () => {

        async function openGame(apiUrl) {
            if (!user) {
                login()
                return
            }

            const openGameHandler = async () => {
                try {
                    const fetchUrl = `/api/v1${apiUrl}`
                    const res = await fetchData(fetchUrl)

                    if (res.status === 'OK') {
                        return window.open(res.data.url, "_blank")
                    }

                    if (res.status === 'SHOW_MESSAGE') {
                        showGameDenyModal(res.data?.message)
                        return
                    }

                    return console.log('Game not found')

                } catch (error) {
                    console.log('Game not found', error)
                }
            }

            if (!isUpdatedFullname) {
                updateFullname(openGameHandler)
                return
            }
            openGameHandler();
        }

        async function openSubLinkV2(item) {
            if (item?.loginRequired && !user) {
                login()
                return
            }

            if (!isLoggedIn) {
                login()
                return
            }

            const openSubLinkV2Handler = async () => {
                if (!['game', 'link'].includes(item?.type)) {
                    const link = (item?.url ?? item?.link)?.trim();
                    if (!link) return;

                    const needsToken = link.includes('token=&') || link.endsWith('token=');
                    const finalLink = needsToken ? updateUrlParam(link, 'token', user?.tp_token ??
                            '') :
                        link;
                    if (item?.new_tab && isMobile()) {
                        openNewTab('', () => Promise.resolve(finalLink));
                    } else {
                        window.location.href = finalLink;
                    }
                    return;
                }
                if (item?.type === 'game') {
                    openGame(item?.link)
                    return
                }
                const frameUrl = await getFrameUrl(item?.link)
                if (!frameUrl) {
                    showGameMaintenanceModal('game')
                    return
                }
                if (item?.new_tab && isMobile()) {
                    openNewTab('', () => Promise.resolve(frameUrl));
                } else {
                    window.open(frameUrl, "_blank")
                }
            }

            if (!isUpdatedFullname) {
                updateFullname(openSubLinkV2Handler)
                return
            }
            openSubLinkV2Handler();
        }

        async function getFrameUrl(link) {
            const iframeUrlConfig = {!! json_encode(config('games.iframeApiEndpoint')) !!};
            const apiEndpoint = iframeUrlConfig?.[link]
            if (apiEndpoint) {
                const {
                    status,
                    data
                } = await fetchData(`/api/v1${apiEndpoint}`)
                if (status === 'OK') {
                    return data?.url
                }
            }
            return null
        }

        function updateProviderDropdown(favoriteProvider) {
            const currentProvider = $('#provider-filter')?.val();
            const providerDropdown = $('#game-provider');
            const providerToggleBtn = providerDropdown.find('#button-provider-filter');
            const optionsList = providerDropdown.find(
                '#dropdown-provider-filter #options-list-provider-filter');
            if (favoriteProvider.find(item => item?.key === currentProvider)) {
                return
            }
            const providerKeys = favoriteProvider.map(item => item?.key)
            optionsList.find('li.dropdown-option-item')?.each((index, item) => {
                const providerKey = $(item).data('value');
                if (!providerKeys.includes(providerKey) && providerKey !== 'all') {
                    $(item).remove();
                    if (currentProvider === providerKey) {
                        providerToggleBtn.find('#dropdown-value-provider-filter').html(
                            `
                            <div class="flex font-normal items-center text-[#494C55] text-xs">
                                <i class="icon-all max-xl:mr-1 mr-2 text-[20px]"></i>
                                <span class="line-clamp-1 max-xl:hidden text-left break-all"> Nhà cung cấp </span>
                                <span class="line-clamp-1 xl:hidden text-left break-all"> NCC </span>
                            </div>
                            `
                        )
                    }
                }
            })
            if (favoriteProvider.length <= 1) {
                providerDropdown?.addClass('hidden');
            }
        }

        async function getProviderList(lobbyType = 'game') {
            const {
                status,
                data
            } = await fetchData(`/api/v1/${lobbyType}/provider`)
            if (status === 'OK') {
                const favoriteProvider = data?.favorite
                updateProviderDropdown(favoriteProvider)
            }
            return []
        }

        async function toggleFavorite(gameId, table_id, partner_provider, name, is_favorite, lobbyType = 'game',
            element,
            event) {
            event?.stopPropagation();
            event?.preventDefault();
            const iconFavorite = $(element)?.find('i[class*="favorite"]')?.[0];
            const loading = $(element)?.find('.game-item__favorite-loading')?.[0];
            const parentElement = $(element)?.closest('.game-item')?.[0];

            if (!user) {
                login();
                return;
            }

            try {
                $(iconFavorite)?.toggle(false);
                $(loading)?.toggle(true);
                $(element)?.addClass('pointer-events-none');
                $(parentElement)?.addClass('disabled');
                const isFavorite = is_favorite === 'true';
                const endpoint = isFavorite ? 'unfavorite' : 'favorite';
                const fetchUrl = `/api/v1/${lobbyType}/${endpoint}`;
                const payload = {
                    p: partner_provider,
                    gId: gameId,
                    name: name,
                    tId: table_id,
                };

                const {
                    status,
                    message
                } = await submitData(fetchUrl, payload);
                if (status !== 'OK') {
                    window.dispatchEvent(
                        new CustomEvent("swal:confirm", {
                            detail: {
                                title: 'Thêm game thất bại',
                                html: message,
                                icon: 'success',
                                customClass: {
                                    icon: 'game-error',
                                    confirmButton: '!hidden'
                                },
                                confirmButtonText: 'Đóng',
                                cancelButtonText: 'Đóng'
                            }
                        })
                    );
                    return
                }
                if (status === 'OK' && iconFavorite) {
                    if (window.location.href.includes('/favorite')) {
                        const currentPage = lobbyType === 'game' ? '/cong-game/favorite' :
                            '/live-casino/favorite';
                        getProviderList(lobbyType);
                        const gameItem = $(event.target).closest('.game-item');
                        if (gameItem.length > 0) {
                            gameItem.remove();
                            const [current, total] = $('#loadmore-counter').text()?.split('/');

                            $('#loadmore-counter').text(`${Number(current) - 1}/${Number(total) - 1}`);
                            if (total % 21 === 0) {
                                $('#loadmore').hide();
                                window.location.reload();
                            }
                        }
                        if ($('#game-container').find('.game-item').length === 0) {
                            $('#empty-game-element').removeClass('hidden').toggle(true);
                            window.location.href = currentPage;
                        }
                        return;
                    }
                    const newState = !isFavorite;
                    iconFavorite.classList.toggle('icon-favorite-gradient', !isFavorite);
                    iconFavorite.classList.toggle('icon-unfavorite', isFavorite);
                    element.setAttribute('onclick',
                        `window.toggleFavorite('${gameId}', '${table_id}', '${partner_provider}', '${name}', '${newState}', '${lobbyType}', this, event)`
                    );
                }

            } catch (error) {
                console.error('Toggle favorite error:', error);
            } finally {
                $(iconFavorite)?.toggle(true);
                $(loading)?.toggle(false);
                $(element)?.removeClass('pointer-events-none');
                $(parentElement)?.removeClass('disabled');
            }
        }
        window.openGame = openGame
        window.toggleFavorite = toggleFavorite
        window.openSubLinkV2 = openSubLinkV2
    })
</script>
