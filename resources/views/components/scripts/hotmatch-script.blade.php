<script>
    async function loadSportLink(data) {
        const loadSportHandler = async () => {
            if (!isMobile()) {
                window.location.href = `/ca-cuoc-the-thao?leagueId=${data.league_id}&matchId=${data.match_id}`;
            } else {
                const sportlink = `/tp/ksportUrl?loginPath=ksports&login=true&leagueId=${data.league_id}&matchId=${data.match_id}`;
                openNewTab('', async () => {
                    const url = await getSportUrl(sportlink);
                    if (!url) {
                        showGameMaintenanceModal('sport')
                        return ''
                    }
                    return url
                })
            }
        }
        
        if(!isUpdatedFullname) {
            updateFullname(loadSportHandler)
            return
        }

        loadSportHandler()
    }
    window.loadSportLink = loadSportLink;
</script>
