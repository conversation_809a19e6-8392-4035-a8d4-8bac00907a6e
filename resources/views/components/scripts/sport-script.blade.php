<script>
    async function getSportUrl(apiUrl) {
        const fetchUrl = `/api/v1${apiUrl}`
        const { status, data } = await fetchData(fetchUrl);
        if (status === 'OK') {

            // Xử lý URL
            const url = new URL(data.url);
            const currentDomain = window.location.origin;
            const modalParams = {
                loginUrl: `${currentDomain}/?modal=login`,
                registerUrl: `${currentDomain}/?modal=register`
            };
            // Cập nhật searchParams
            ['loginUrl', 'registerUrl'].forEach(param => {
                url.searchParams.delete(param);
                // url.searchParams.append(param, modalParams[param]);
            });

            return url;
        }
        return '';
    }

    async function onClickSport(item) {
        const itemData = item
        const isSport = itemData.isSport || false
        const needCheckName = itemData.type == 'sport' || item.needCheckName
        const cb = () => {
            if (itemData?.isLink || !isMobile()) {
                window.location.href = item.to
            } else {
                openNewTab('', async () => {
                    const url = await getSportUrl(item.apiUrl)
                    if (!url) {
                        showGameMaintenanceModal(isSport ? 'sport' : 'game')
                        return ''
                    }
                    return url
                })
            }
        }


        // if (itemData.loginRequired && !isLoggedIn) {
        //     login()
        //     return
        // }
        // if (!isLoggedIn) {
        //     login()
        //     return
        // }
        
        // if((isSport || needCheckName) && !isUpdatedFullname) {
        //     updateFullname(cb)
        //     return
        // }

        if (itemData?.isLink) {
            window.location.href = item.to
            return
        }

        if (!isMobile() ) {
            window.location.href = item.to
            return
        }

        openNewTab('', async () => {
            const url = await getSportUrl(item.apiUrl)
            if (!url) {
                showGameMaintenanceModal(isSport ? 'sport' : 'game')
                return ''
            }
            return url
        })
    }

    async function onClickGameType(item) {
        const itemData = item
        const isSport = itemData.isSport || false
        const needCheckName = itemData.type == 'sport' || item.needCheckName

        if (itemData?.isLink) {
            window.location.href = item.to
            return
        }

        const cb = () => {
            if (itemData?.isLink || !isMobile()) {
                window.location.href = item.to
            } else {
                openNewTab('', async () => {
                    const url = await getSportUrl(item.apiUrl)
                    if (!url) {
                        showGameMaintenanceModal(isSport ? 'sport' : 'game')
                        return ''
                    }
                    return url
                })
            }
        }

        if (itemData.loginRequired && !isLoggedIn) {
            login()
            return
        }

        if (!isLoggedIn) {
            login()
            return
        }
        
        if((isSport || needCheckName) && !isUpdatedFullname) {
            updateFullname(cb)
            return
        }

        

        if (!isMobile() ) {
            window.location.href = item.to
            return
        }

        openNewTab('', async () => {
            const url = await getSportUrl(item.apiUrl)
            if (!url) {
                showGameMaintenanceModal(isSport ? 'sport' : 'game')
                return ''
            }
            return url
        })
    }
    window.getSportUrl = getSportUrl;
    window.onClickSport = onClickSport;
    window.onClickGameType = onClickGameType;
</script>
