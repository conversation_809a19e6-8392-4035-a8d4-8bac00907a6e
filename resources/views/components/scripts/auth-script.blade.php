<script>
    const loginModal = `
    <x-ui.modal
        :id="'login-signup-modal'"
        modalClass=""
        modalContainerClass="max-xl:border-x-none xl:!py-8 max-xl:rounded-[12px_12px_0_0] rounded-lg max-xl:mt-auto before:bg-[url('/public/asset/images/auth/bg-auth-mb.jpg')] xl:before:bg-[url('/public/asset/images/auth/bg-auth-desktop.jpg')]"
    >
        <x-ui.modals.login-signup-modal />
    </x-ui.modal>`

    const signupModal = `
    <x-ui.modal
        :id="'login-signup-modal'"
        modalClass=""
        modalContainerClass="max-xl:border-x-none xl:!py-8 max-xl:rounded-[12px_12px_0_0] rounded-lg max-xl:mt-auto before:bg-[url('/public/asset/images/auth/bg-auth-mb.jpg')] xl:before:bg-[url('/public/asset/images/auth/bg-auth-desktop.jpg')]"
    >
        <x-ui.modals.login-signup-modal activeTab="register" />
    </x-ui.modal>`

    const notiSignupModal = `
    <x-ui.modal
        :id="'noti-signup-modal'"
        modalClass=""
        modalContainerClass="!px-4 !py-5 xl:!px-8 xl:!py-8 max-w-[366px] xl:max-w-[440px] min-h-[296px] xl:min-h-[306px] rounded-[12px] before:bg-[url('/public/asset/images/auth/popup-bg.jpg')]"
    >
        <x-ui.modals.noti-signup></x-ui.modals.noti-signup>
    </x-ui.modal>`;

    const forgotPasswordModal = `
    <x-ui.modal
        :header="__('auth.forgotPasswordText.title')"
        :id="'forgot-password-modal'"
        modalClass=""
        modalContainerClass="max-xl:rounded-[12px_12px_0_0] xl:p-8 max-xl:mt-auto !max-w-[440px] before:bg-[url('/public/asset/images/auth/popup-bg.jpg')]"
    >
        <x-ui.modals.forgot-password-modal />
    </x-ui.modal>
    `
    const verifyEmailModal = `
    <x-ui.modal
        :header="__('auth.verifyEmailText.title')"
        :id="'verify-email-modal'"
        modalClass=""
        modalContainerClass="max-xl:rounded-[12px_12px_0_0] !p-6 xl:!p-8 max-xl:mt-auto !max-w-[440px] before:bg-[url('/public/asset/images/auth/popup-bg.jpg')]"
    >
        <x-ui.modals.verify-email-modal />
    </x-ui.modal>
    `
    const verifyOtpModal = `
    <x-ui.modal
        :header="__('auth.verifyEmailText.title')"
        :id="'verify-otp-modal'"
        modalClass=""
        modalContainerClass="max-xl:rounded-[12px_12px_0_0] !p-6 xl:!p-8 max-xl:mt-auto !max-w-[440px] before:bg-[url('/public/asset/images/auth/popup-bg.jpg')]"
    >
        <x-ui.modals.verify-otp-modal />
    </x-ui.modal>
    `
    const verifyOtpFailedModal = `
    <x-ui.modal
        :header="__('auth.verifyEmailText.title')"
        :id="'verify-otp-failed-modal'"
        modalClass=""
        modalContainerClass="!max-w-[440px] max-xl:rounded-[24px_24px_0_0] max-xl:mt-auto"
    >
        <x-ui.modals.verify-otp-failed-modal />
    </x-ui.modal>
    `

    const createNewPasswordModal = `
    <x-ui.modal
        :header="__('auth.createNewPassword')"
        :id="'create-new-password-modal'"
        modalClass=""
        modalContainerClass="max-xl:rounded-[12px_12px_0_0] xl:p-8 max-xl:mt-auto !max-w-[440px] before:bg-[url('/public/asset/images/auth/popup-bg.jpg')]"
    >
        <x-ui.modals.create-new-password />
    </x-ui.modal>
    `

    const updateFullnameModal = `
    <x-ui.modal
        :header="__('auth.updateFullname')"
        :id="'update-fullname-modal'"
        modalClass=""
        modalContainerClass="!px-4 !pt-5 !pb-[64px] xl:!p-8 max-xl:rounded-[24px_24px_0_0] max-xl:mt-auto !max-w-[440px] before:bg-[url('/public/asset/images/auth/popup-bg.jpg')]"
    >
        <x-ui.modals.update-fullname />
    </x-ui.modal>
    `

    const eventInvestLegendModal = `<x-ui.modals.events.invest-legend />`;

    function authFormShowLoading() {
        $('#auth-btn-submit-loading')?.show()
        $('#auth-btn-submit')?.hide()
    }

    function authFormHideLoading() {
        $('#auth-btn-submit-loading')?.hide()
        $('#auth-btn-submit')?.show()
    }

    function changeAuthTab(tab, element) {

        if (tab === $('#login-signup-tab-container').data('tab')) {
            return;
        }
        $('#login-signup-tab-container').data('tab', tab)
        if (tab === 'login') {
            $('#login-form').validate().resetForm()
            $('#login-form-container .input-field').each(function() {
                $(this).val('');
            });
            $('#login-form-container').removeClass('hidden')
            $('#register-form-container').addClass('hidden')
            $('#login-form').find('button[type="submit"]').attr('disabled', true)

        } else {
            $('#signup-form').validate().resetForm()
            $('#signup-container .input-field').each(function() {
                $(this).val('');
            });
            $('#login-form-container').addClass('hidden')
            $('#register-form-container').removeClass('hidden')
            $('#signup-submit-btn').attr('disabled', true)
        }
        $(element).find('.tab-title').addClass('text-neutral-800').removeClass('text-neutral')
        $(element).siblings().find('.tab-title').addClass('text-neutral').removeClass('text-neutral-800')
        $(element).addClass('bg-gradient-btn-primary')
        $(element).siblings().removeClass('bg-gradient-btn-primary')

        // Reset password eye icon
        const passwordEyeIcon = $('.js-password-eye-icon')
        passwordEyeIcon.find('.eye-show').addClass('hidden')
        passwordEyeIcon.find('.eye-hide').removeClass('hidden')
    }

    function bindLoginForm() {
        $(document).ready(function() {
            const savedUsername = localStorage.getItem('username')
            const saveAccount = localStorage.getItem('saveAccount') === '1'
            if (saveAccount && savedUsername) {
                $('#login-form #remember_id').prop('checked', true)
                $('#login-form #username').val(savedUsername)
            }
            $("#login-form").validate({
                rules: {
                    username: {
                        required: true,
                        alphanumbericOnly: true,
                        minlength: formRules.username.min,
                        maxlength: formRules.username.max,
                    },
                    password: {
                        required: true,
                        minlength: formRules.password.min,
                        maxlength: formRules.password.max
                    }
                },
                messages: {
                    username: {
                        required: validationMessages.username.required,
                        minlength: validationMessages.username.min,
                        maxlength: validationMessages.username.max,
                    },
                    password: {
                        required: validationMessages.password.required,
                        minlength: validationMessages.password.min,
                        maxlength: validationMessages.password.max,
                    }
                },
                errorClass: 'form-input-error',
                errorPlacement: function(error, element) {
                    element?.closest('.base-input')?.append(error);
                },
                errorElement: 'span'
            })
            $('#login-form #username, #login-form #password').on("change input paste", function() {
                $(this).valid();
                checkShowSubmit($("#login-form"), $("#login-form").find('button[type="submit"]'));
            });
            $('#login-form').submit(async function(e) {
                e.preventDefault();
                authFormShowLoading()
                try {
                    if ($(this).valid()) {
                        const username = $('#login-form #username').val();
                        const password = $('#login-form #password').val();
                        const token = isGoogleRecaptchaEnabled ?
                        await getGoogleRecaptchaToken() : null;

                        const payload = {
                            username: username,
                            password: password,
                            token: token || '',
                        };
                        // Check affiliate params
                        const affParams = JSON.parse(localStorage.getItem("affParams")) || {};
                        if (affParams) {
                            Object.assign(payload, affParams);
                        }

                        // Đẩy sự kiện vào DataLayer trước khi gửi form
                        window.dataLayer = window.dataLayer || [];
                        window.dataLayer.push({
                            event: "formSubmitted",
                            formName: "Form_Login"
                        });

                        res = await submitData(url = '/login', params = payload);
                        if (res?.status === 'OK') {
                            Toast({
                                title: 'Thành công!',
                                text: 'Đăng nhập tài khoản thành công'
                            })
                            const saveAccount = $('#login-form #remember_id').is(':checked') ?
                                true :
                                false;
                            if (saveAccount) {
                                localStorage.setItem('username', payload.username)
                                localStorage.setItem('saveAccount', '1')
                            } else {
                                localStorage.setItem('username', '')
                                localStorage.setItem('saveAccount', '0')
                            }
                            closeModal()
                            window.storeSafeUserData(res.data);

                            localStorage.removeItem('mini-opened');
                            setTimeout(() => {
                                triggerReloadForAllTabs();
                                window.location.reload();
                            }, 300);
                            return;
                        } else if (res?.message?.toLowerCase()?.includes(
                                'không tìm thấy người dùng') || res?.message
                            ?.toLowerCase()?.includes('User not found')) {
                            Notify({
                                title: 'Lỗi đăng nhập',
                                html: 'Không tìm thấy người dùng',
                                customClass: {
                                    icon: 'user-error'
                                },
                                cancelButtonText: 'Liên hệ CSKH',
                                confirmButtonText: 'Trang Chủ',
                                cancelButtonFunc: () => {
                                    openLiveChat();
                                },
                                confirmButtonFunc: () => {
                                    window.location.href = '/';
                                }
                            })
                            return;
                        }

                        if (res?.code === 210) {
                            Notify({
                                title: 'Tài khoản bị khóa',
                                html: res?.message ||
                                    ' Vui lòng liên hệ CSKH để được hướng dẫn',
                                icon: 'success',
                                customClass: {
                                    icon: 'user-lock',
                                    confirmButton: '!hidden'
                                },
                                cancelButtonText: 'Liên hệ CSKH',
                                confirmButtonText: 'xxxx',
                                cancelButtonFunc: () => {
                                    openLiveChat();
                                },
                                confirmButtonFunc: () => {
                                    window.location.href = '/';
                                }
                            })
                        } else {
                            Notify({
                                title: 'Lỗi đăng nhập',
                                html: res?.message || 'Đã có lỗi xảy ra, vui lòng thử lại!',
                                icon: 'success',
                                customClass: {
                                    icon: 'user-error'
                                },
                                cancelButtonText: 'Quên mật khẩu',
                                confirmButtonText: 'Trang Chủ',
                                cancelButtonFunc: () => {
                                    forgotPassword();
                                },
                                confirmButtonFunc: () => {
                                    window.location.href = '/';
                                }
                            })
                        }

                        return;
                    }
                } catch (error) {
                    Toast({
                        title: 'Thất bại!',
                        text: 'Đã có lỗi xảy ra, vui lòng thử lại!',
                        type: 'error'
                    })
                } finally {
                    authFormHideLoading()
                }
            })
        })
    }

    function bindSignupForm() {
        $(document).ready(function() {
            $("#signup-form").validate({
                rules: {
                    username: {
                        required: true,
                        alphanumbericOnly: true,
                        minlength: formRules.username.min,
                        maxlength: formRules.username.max,
                    },
                    password: {
                        required: true,
                        minlength: formRules.password.min,
                        maxlength: formRules.password.max,
                        passwordNoSpace: true
                    },
                    phone: {
                        required: true,
                        onlyNumber: true,
                        phoneStartWithZero: true,
                        minlength: formRules.phone.min,
                        maxlength: formRules.phone.max,
                    },
                },
                messages: {
                    username: {
                        required: validationMessages.username.required,
                        minlength: validationMessages.username.min,
                        maxlength: validationMessages.username.max,
                    },
                    password: {
                        required: validationMessages.password.required,
                        minlength: validationMessages.password.min,
                        maxlength: validationMessages.password.max,
                    },
                    phone: {
                        required: validationMessages.phone.required,
                        onlyNumber: validationMessages.phone.only_number,
                        minlength: validationMessages.phone.min,
                        maxlength: validationMessages.phone.max,
                    }
                },
                errorClass: 'form-input-error',
                errorPlacement: function(error, element) {
                    element?.closest('.base-input')?.append(error);
                },
                errorElement: 'span',
            });

            // Thêm xử lý sự kiện keypress cho form
            $("#signup-form input").keypress(function(e) {
                if (e.which == 13) { // 13 là mã phím Enter
                    e.preventDefault();
                    if ($("#signup-form").valid()) {
                        $("#signup-form").submit();
                    }
                }
            });

            $("#signup-username, #signup-password, #signup-phone").on("change input paste", function() {
                $(this).valid();
                checkShowSubmit($("#signup-form"), $("#signup-submit-btn"));
            });
            $("#signup-form").submit(async function(e) {
                $('#signup-submit-btn').addClass('is-loading')
                e.preventDefault();

                // Get the form data
                if ($(this).valid()) {
                    const username = $("#signup-username").val();
                    const password = $("#signup-password").val();
                    const phone = $("#signup-phone").val();
                    try {
                        const token = isGoogleRecaptchaEnabled ?
                        await getGoogleRecaptchaToken() : null;
                        // Create a JSON payload
                        const payload = {
                            username: username,
                            password: password,
                            confirmPassword: password,
                            phone: phone,
                            token: token || '',
                        };

                    // Check affiliate params
                    const affParams = JSON.parse(localStorage.getItem("affParams")) || {};
                    if (affParams) {
                        Object.assign(payload, affParams);
                    }

                    // Đẩy sự kiện vào DataLayer trước khi gửi form
                    window.dataLayer = window.dataLayer || [];
                    window.dataLayer.push({
                        event: "formSubmitted",
                        formName: "Form_Register"
                    });

                        res = await submitData((url = "/register"), (params = payload));
                        if (res?.status) {
                            closeModal()
                            Toast({
                                title: 'Thành công',
                                text: 'Đăng ký tài khoản thành công'
                            })
                            localStorage.removeItem('mini-opened');
                            setTimeout(() => {
                                triggerReloadForAllTabs();
                                window.location.reload();
                            }, 1000);
                            return;
                        }
                        Toast({
                            title: 'Thất bại!',
                            text: res?.message ?? 'Đã có lỗi xảy ra, vui lòng thử lại!',
                            type: 'error'
                        })
                    } catch (error) {
                        Toast({
                            title: 'Thất bại!',
                            text: 'Đã có lỗi xảy ra, vui lòng thử lại!',
                            type: 'error'
                        })
                    } finally {
                        $('#signup-submit-btn').removeClass('is-loading')
                    }
                }
            });

        });
    }

    function bindCreateNewPasswordForm() {
        $(document).ready(function() {
            $("#create-new-password-form").validate({
                rules: {
                    otp: {
                        required: true,
                        minlength: formRules.otp.min,
                        maxlength: formRules.otp.min,
                        number: true
                    },
                    password: {
                        required: true,
                        minlength: formRules.password.min,
                        maxlength: formRules.password.max,
                        passwordNoSpace: true
                    },
                    confirmPassword: {
                        required: true,
                        equalTo: "#create-new-password-form #password"
                    }
                },
                messages: {
                    otp: {
                        required: validationMessages.otp.required,
                        minlength: validationMessages.otp.min,
                        maxlength: validationMessages.otp.max,
                        number: validationMessages.otp.min
                    },
                    password: {
                        required: validationMessages.password.required,
                        minlength: validationMessages.password.min,
                        maxlength: validationMessages.password.max,
                    },
                    confirmPassword: {
                        required: validationMessages.password.required,
                        equalTo: validationMessages.confirm_password.not_match
                    }
                },
                errorClass: 'form-input-error',
                errorPlacement: function(error, element) {
                    element?.closest('.base-input')?.append(error);
                },
                errorElement: 'span'
            })
            $("#create-new-password-form #otp, #create-new-password-form #password, #create-new-password-form #confirmPassword")
                .on("change input paste", function() {
                    $(this).valid();
                    checkShowSubmit($("#create-new-password-form"), $("#create-new-password-form").find(
                        'button[type="submit"]'));
                });
            $("#create-new-password-form").submit(async function(e) {
                try {
                    e.preventDefault();
                    authFormShowLoading()
                    if ($(this).valid()) {
                        const otp = $("#create-new-password-form #otp").val();
                        const password = $("#create-new-password-form #password").val();
                        const confirmPassword = $("#create-new-password-form #confirmPassword")
                            .val();
                        const payload = {
                            otp: otp,
                            password: password,
                            confirmPassword: confirmPassword
                        }
                        if (localStorage.getItem('email')) {
                            payload.email = localStorage.getItem('email')
                        }
                        if (localStorage.getItem('username')) {
                            payload.username = localStorage.getItem('username')
                        }
                        const res = await submitData((url = "/resetPasswordByOtp"), (params =
                            payload));
                        if (res?.data) {
                            localStorage.removeItem('email')
                            localStorage.removeItem('username')
                            closeModal()
                            Toast({
                                title: 'TẠO MẬT KHẨU MỚI THÀNH CÔNG',
                                text: 'Mật khẩu mới đã được tạo thành công, vui lòng đăng nhập lại.'
                            })
                            const username = typeof res?.data === 'string' ? JSON.parse(res?.data)
                                ?.username : res?.data?.username
                            if (username) {
                                localStorage.setItem('autofillUsername', username.replace(/^\d+_/,
                                    ''))
                            }
                            login()
                            return;
                        }
                        Toast({
                            title: 'Thất bại!',
                            text: res?.message ?? 'Đã có lỗi xảy ra, vui lòng thử lại!',
                            type: 'error'
                        })
                    }
                } catch (error) {
                    Toast({
                        title: 'Thất bại!',
                        text: 'Đã có lỗi xảy ra, vui lòng thử lại!',
                        type: 'error'
                    })
                } finally {
                    authFormHideLoading()
                }
            })

            const container = $('#create-new-password-form')
            const resendOtp = container.find(".btn-send-again");
            let remainingTime = 60;
            let isCountdown = false;
            let interval;

            countdown();

            function countdown() {
                if (interval) {
                    clearInterval(interval);
                }
                if (isCountdown) {
                    return;
                }
                remainingTime = 60;
                interval = setInterval(function() {
                    isCountdown = true;
                    remainingTime--;
                    resendOtp.text(`Gửi lại OTP (${remainingTime}s)`);
                    resendOtp.addClass('text-neutral-500 pointer-events-none');
                    resendOtp.removeClass('cursor-pointer text-primary');
                    if (remainingTime <= 0) {
                        clearInterval(interval);
                        interval = null;
                        isCountdown = false;
                        resendOtp.text('Gửi lại OTP');
                        resendOtp.removeClass('text-neutral-500 pointer-events-none');
                        resendOtp.addClass('cursor-pointer text-primary');
                    }
                }, 1000);
            }

            resendOtp.on("click", async function() {
                resendOtp.addClass('is-loading')
                try {
                    if (!localStorage.getItem('email') && !localStorage.getItem('username')) {
                        Toast({
                            title: 'Thất bại!',
                            text: 'Đã có lỗi xảy ra, vui lòng thử lại!',
                            type: 'error'
                        })
                        return;
                    }
                    let endpoint = '/api/v1/user/sendOtp'
                    let messageSuccess = ''
                    if (localStorage.getItem('email')) {
                        endpoint = `${endpoint}?email=${localStorage.getItem('email')}`
                        messageSuccess =
                            'Gửi email thành công! Vui lòng kiểm tra Email để đặt lại mật khẩu.'
                    } else if (localStorage.getItem('username')) {
                        endpoint = `${endpoint}?username=${localStorage.getItem('username')}`
                        messageSuccess =
                            'Gửi OTP thành công! Vui lòng kiểm tra OTP để đặt lại mật khẩu.'
                    }
                    const res = await fetchData(endpoint)
                    if (res.status === 'OK') {
                        countdown()
                    }
                    const type = res.status !== 'OK' ? 'error' : 'success';
                    const text = res.status !== 'OK' ?
                        res.message ?? 'Đã có lỗi xảy ra, vui lòng thử lại!' :
                        messageSuccess;
                    Toast({
                        text,
                        type
                    })
                    resendOtp.removeClass('is-loading')
                } catch (error) {
                    Toast({
                        title: 'Thất bại!',
                        text: 'Đã có lỗi xảy ra, vui lòng thử lại!',
                        type: 'error'
                    })
                }

            })
        })
    }

    function bindForgotPasswordForm() {
        $(document).ready(function() {
            $("#forgot-password-by-email").validate({
                rules: {
                    email: {
                        required: true,
                        emailValidate: true
                    }
                },
                messages: {
                    email: {
                        required: validationMessages.email.required,
                        emailValidate: validationMessages.email.invalid
                    }
                },
                errorClass: 'form-input-error',
                errorPlacement: function(error, element) {
                    element?.closest('.base-input')?.append(error);
                },
                errorElement: 'span'
            });

            $("#forgot-password-input-email").on("change input paste", function() {
                $(this).valid();
                checkShowSubmit($("#forgot-password-by-email"), $("#forgot-password-by-email").find(
                    'button[type="submit"]'));
            });

            $("#forgot-password-by-email").submit(async function(e) {
                e.preventDefault();
                $('#forgot-password-mail-btn-submit-loading').show()
                $('#forgot-password-mail-submit-btn').hide()
                if ($(this).valid()) {
                    const email = $("#forgot-password-input-email").val();
                    res = await fetchData((url = `/api/v1/user/sendOtp?email=${email}`));
                    const type = res.status !== 'OK' ? 'error' : 'success';
                    const title = res.status !== 'OK' ? 'Thất bại!' : 'Gửi Email thành công';
                    const text = res.status !== 'OK' ? res.message ??
                        'Đã có lỗi xảy ra, vui lòng thử lại!' :
                        'Gửi email thành công! Vui lòng kiểm tra Email để đặt lại mật khẩu.';
                    window.Toast({
                        title,
                        text,
                        type
                    });
                    if (res.status === 'OK') {
                        localStorage.removeItem('username')
                        localStorage.setItem('email', email)
                        closeModal();
                        createNewPassword();
                    }
                }
                $('#forgot-password-mail-btn-submit-loading').hide()
                $('#forgot-password-mail-submit-btn').show()
            });
        })
    }

    function bindVerifyOtpForm() {
        $(document).ready(function() {
            const form = $("#verify-otp-form");
            const btnSubmit = form.find("#auth-btn-submit");
            const container = $('#verify-otp-container')
            const resendOtp = container.find("#resend-otp");
            let remainingTime = 60;
            let isCountdown = false;
            let interval;

            countdown();

            async function handleResendOtp() {
                if (localStorage.getItem('verifyEmail')) {
                    const res = await fetchData((url =
                        `/api/v1/user/getOTP?email=${localStorage.getItem('verifyEmail')}`));
                    if (res.status === 'OK') {
                        window.Toast({
                            title: 'Thành công!',
                            text: res?.message ?? 'Gửi lại OTP thành công',
                        })
                        isCountdown = false;
                        countdown();
                        return;
                    } else {
                        window.Toast({
                            title: 'Thất bại!',
                            text: res?.message ?? 'Đã có lỗi xảy ra, vui lòng thử lại!',
                            type: 'error'
                        })
                    }
                }
            }
            resendOtp.on('click', async function() {
                handleResendOtp();
            });

            function countdown() {
                if (interval) {
                    clearInterval(interval);
                }
                if (isCountdown) {
                    return;
                }
                remainingTime = 60;
                interval = setInterval(function() {
                    isCountdown = true;
                    remainingTime--;
                    resendOtp.text(`Gửi lại OTP (${remainingTime}s)`);
                    resendOtp.addClass('text-neutral-300 pointer-events-none');
                    resendOtp.removeClass('cursor-pointer text-primary');
                    if (remainingTime <= 0) {
                        clearInterval(interval);
                        interval = null;
                        isCountdown = false;
                        resendOtp.text('Gửi lại OTP');
                        resendOtp.removeClass('text-neutral-300 pointer-events-none');
                        resendOtp.addClass('cursor-pointer text-primary');
                    }
                }, 1000);
            }

            $("#verify-otp-form").validate({
                rules: {
                    otp: {
                        required: true,
                        minlength: formRules.otp.min,
                        maxlength: formRules.otp.min,
                        number: true
                    },
                },
                messages: {
                    otp: {
                        required: validationMessages.otp.required,
                        minlength: validationMessages.otp.min,
                        maxlength: validationMessages.otp.max,
                        number: validationMessages.otp.min
                    },
                },
                errorClass: 'form-input-error',
                errorPlacement: function(error, element) {
                    element?.closest('.base-input')?.append(error);
                },
                errorElement: 'span'
            })

            $("#verify-otp-input-otp").on("change input paste", function() {
                $(this).valid();
                checkShowSubmit(form, btnSubmit);
            });

            $("#verify-otp-form").submit(async function(e) {
                e.preventDefault();
                authFormShowLoading()
                if ($(this).valid()) {
                    const otp = $("#verify-otp-input-otp").val();
                    const res = await submitData('/api/v1/user/emailOTPVerification', {
                        code: otp
                    }, '', '');
                    if (res.status === 'OK') {
                        localStorage.removeItem('verifyEmail')
                        closeModal()
                        window.Toast({
                            title: 'Thành công!',
                            text: 'Xác minh email thành công'
                        })
                        $('.js-verify-email').addClass(
                            'verified text-[#0D8E01] pointer-events-none');
                        $('.js-verify-email').html(`
                            <i class="icon-check text-[#0D8E01]"></i>
                        `)
                        $('#email-input').removeClass('pr-[75px]');
                        $('#email-input').addClass('pr-[30px]');
                        $('#email-input').attr('disabled', true);
                        await refreshUser();
                        window.location.reload();
                        return;
                    }
                    window.dispatchEvent(
                        new CustomEvent("swal:confirm", {
                            detail: {
                                title: 'Thông báo',
                                html: res?.message || 'Có lỗi xảy ra, vui lòng thử lại',
                                icon: 'success',
                                confirmButtonText: 'Đóng',
                                customClass: {
                                    icon: 'mail-error'
                                },
                                cancelButtonText: 'Gửi Lại Mã',
                                cancelButtonFunc: () => {
                                    $("#verify-otp-input-otp").val('');
                                    $("#verify-otp-input-otp").focus();
                                    handleResendOtp();
                                },
                            }
                        })
                    );
                }
                authFormHideLoading()
            })
        })
    }

    function bindVerifyEmailForm() {
        const form = $('#verify-email-form');
        const fields = form?.find('.input-field');
        const btnSubmit = form?.find('#auth-btn-submit');
        if (form) {
            fields?.each((index, element) => {
                $(element).on("change input", function() {
                    checkShowSubmit(form, btnSubmit);
                });
            });

            form.validate({
                rules: {
                    email: {
                        required: true,
                        emailValidate: true
                    }
                },
                messages: {
                    email: {
                        required: validationMessages.email.required,
                        emailValidate: validationMessages.email.invalid
                    }
                },
                errorClass: 'form-input-error',
                errorPlacement: function(error, element) {
                    element?.closest('.base-input')?.append(error);
                },
                errorElement: 'span'
            })

            form.on("change input paste", function() {
                $(this).valid();
                checkShowSubmit(form, btnSubmit);
            });

            form.submit(async function(e) {
                e.preventDefault();
                try {
                    authFormShowLoading()
                    if ($(this).valid()) {
                        const email = $("#verify-email-input-email").val();
                        const res = await fetchData((url = `/api/v1/user/getOTP?email=${email}`));
                        const type = res.status !== 'OK' ? 'error' : 'success';
                        const title = res.status !== 'OK' ? 'THẤT BẠI!' : 'THÀNH CÔNG';
                        const text = res.message ?? 'Vui lòng kiểm tra email để xác nhận';
                        if (res.status === 'OK') {
                            closeModal()
                            openModal(verifyOtpModal, true, 'verify-otp-modal')
                            localStorage.setItem('verifyEmail', email)
                            bindVerifyOtpForm()
                            refreshUser('#email-input')
                        } else {
                            window.dispatchEvent(
                                new CustomEvent("swal:confirm", {
                                    detail: {
                                        title: 'Thông báo',
                                        html: res?.message ?? 'Đã có lỗi xảy ra, vui lòng thử lại!',
                                        icon: 'success',
                                        customClass: {
                                            icon: 'mail-error',
                                            confirmButton: '!hidden'
                                        },
                                        confirmButtonText: 'Đóng',
                                        cancelButtonText: 'Thử Lại'
                                    }
                                })
                            );
                        }
                    }
                } finally {
                    authFormHideLoading()
                }
            });
        }
    }

    function bindUpdateFullnameForm(callback) {
        const $submitBtn = $("#auth-btn-submit button");
        $(document).ready(function() {
            $("#update-fullname-form").validate({
                rules: {
                    fullname: {
                        required: true,
                        alphanumbericOnlyFullName: true,
                        minlength: formRules.fullname.min,
                        maxlength: formRules.fullname.max,
                        notEqualTo: '#update-fullname-form #username'
                    }
                },
                messages: {
                    fullname: {
                        required: validationMessages.fullname.required,
                        minlength: validationMessages.fullname.min,
                        maxlength: validationMessages.fullname.max,
                        notEqualTo: validationMessages.fullname.equal_username
                    }
                },
                errorClass: 'form-input-error',
                errorPlacement: function(error, element) {
                    element?.closest('.base-input')?.append(error);
                },
                errorElement: 'span',
                submitHandler: function(form) {
                    handleSubmitForm(form);
                },
            })
            $("#update-fullname-form #fullname").on("change keyup input-paste", function() {
                if ($(this).valid()) {
                    $("#auth-btn-submit button").prop("disabled", false);
                } else {
                    $("#auth-btn-submit button").prop("disabled", true);
                }
            });
            const handleSubmitForm = async (form) => {
                $submitBtn.prop("disabled", true).addClass("is-loading");
                const fullname = $("#update-fullname-form #fullname").val();
                const res = await submitData((url = "/updateUserFullname"), (params = {
                    fullname: fullname
                }));
                if (res?.status) {
                    $("input#fullname").val(fullname);
                    $(".js-update-fullname-btn").remove();
                    await refreshUser();
                    closeModal();
                    Toast({
                        title: 'Thành công',
                        text: 'Cập nhật tên hiển thị thành công'
                    });
                    isUpdatedFullname = true;
                    if (typeof callback === 'function') callback();
                    return;
                }
                Toast({
                    title: 'Thất bại!',
                    text: res?.message ?? 'Đã có lỗi xảy ra, vui lòng thử lại!',
                    type: 'error'
                })
                $submitBtn.prop("disabled", false).removeClass("is-loading");
            }
        })
    }

    function login() {
        openModal(loginModal, true, 'login-modal');
        bindLoginForm();
        bindSignupForm();
        handleInput();
    }

    function signup() {
        openModal(signupModal, true, 'signup-modal')
        bindLoginForm()
        bindSignupForm()
        handleInput();
    }

    function notiSignup() {
        openNotiModal(notiSignupModal, true, 'noti-signup-modal');
    }

    function forgotPassword() {
        openModal(forgotPasswordModal, true, 'forgot-password-modal')
        bindForgotPasswordForm()
        handleInput();
    }

    function verifyEmail() {
        openModal(verifyEmailModal, true, 'verify-email-modal')
        bindVerifyEmailForm()
        handleInput();
    }

    function createNewPassword() {
        openModal(createNewPasswordModal, true, 'create-new-password-modal')
        bindCreateNewPasswordForm()
        handleInput();
    }

    function updateFullname(callback) {
        openModal(updateFullnameModal, true, 'update-fullname-modal')
        bindUpdateFullnameForm(callback)
        handleInput();
    }

    function openLogoutModal() {
        window.dispatchEvent(
            new CustomEvent("swal:confirm", {
                detail: {
                    title: 'ĐĂNG XUẤT TÀI KHOẢN',
                    html: 'Bạn muốn thoát tài khoản?',
                    icon: 'success',
                    confirmButtonText: 'Đăng xuất',
                    customClass: {
                        icon: 'logout',
                    },
                    cancelButtonText: 'Bỏ qua',
                    cancelButtonFunc: async () => {

                    },
                    confirmButtonFunc: async () => {
                        logout()
                    },
                    allowOutsideClick: false,
                },
            })
        );
    }

    async function logout() {
        if (isRefreshing) {
            await new Promise(resolve => {
                const checkRefresh = setInterval(() => {
                    if (!isRefreshing) {
                        clearInterval(checkRefresh);
                        resolve();
                    }
                }, 100);
            });
        }

        try {
            isLoggingOut = true;
            const response = await submitData('/logout');

            if (response?.status !== 'success') {
                window.Toast({
                    title: 'Đăng xuất thất bại',
                    text: response?.message || 'Đã có lỗi xảy ra, vui lòng thử lại sau.',
                    type: 'error',
                });
                return;
            }
            window.location.href = '/';
        } finally {
            isLoggingOut = false;
        }
    }

    // Lắng nghe sự kiện modalReady
    document.addEventListener('modalReady', function(e) {
        if (e.detail.modalId === 'login-modal') {
            $('#login-form #username')?.focus()
        }
        if (e.detail.modalId === 'signup-modal') {
            $('#signup-username')?.focus()
        }
    });
</script>
