@pushOnce('scripts')
    <script>
        // handle when user direct access by url
        window.addEventListener('DOMContentLoaded', function(event) {
            document.addEventListener('modalClosed', function(event) {
                if (!isLoggedIn || (event.detail.id === "update-fullname-modal-closeModalBtn" && !isUpdatedFullname)) {
                    window.location.href = '/';
                    return
                }
            });
            if (!isLoggedIn) {
                login();
                return;
            }
            if (!isUpdatedFullname) {
                updateFullname()
            }
        });
    </script>
@endpushOnce
