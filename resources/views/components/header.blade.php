@php
    use App\Helpers\DetectDeviceHelper;
    $isLoggedIn = Auth::check();
    $user = Auth::user();
    $headerMenu = config('menu.headerMenu');
    $isMobile = DetectDeviceHelper::isMobile();
    $brandName = config('app.brand_name');
@endphp
<header class="header fixed top-0 z-[104] w-full bg-neutral-1000">
    <div @class([
        'flex items-center',
        'h-[56px] xl:px-[10px] xl:gap-[16px] 2xl:gap-[24px]',
        'px-3 max-xl:border-b max-xl:border-neutral-600',
    ])>
        {{-- toggle btn & logo --}}
        <div class="flex shrink-0 items-center gap-2 xl:gap-[10px]">
            <div class="flex h-[32px] w-[32px] shrink-0 items-center justify-center rounded-lg bg-neutral-650 xl:h-[40px] xl:w-[44px]">
                <x-kit.icon-button class="btn-toggle-menu js-btn-toggle-menu" type="rectangle">
                    <img @class([
                        'js-icon-close absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[20px] h-[20px] object-contain',
                        'hidden' => $isMobile,
                    ]) src="{{ asset('asset/icons/menu-icons/menu-close.svg') }}" alt="close menu">
                    <img @class([
                        'js-icon-open absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[20px] h-[20px] object-contain',
                        'hidden' => !$isMobile,
                    ]) src="{{ asset('asset/icons/menu-icons/menu-open.svg') }}" alt="open menu">
                </x-kit.icon-button>
            </div>
            <a href="/" class="block h-[25px] shrink-0 xl:h-[32px]">
                <img src="{{ asset('/asset/images/' . strtolower($brandName) . '-logo.svg') }}" class="h-full object-contain"
                    alt="brand logo">
            </a>
        </div>
        {{-- header menu --}}
        <div class="hidden shrink-0 items-center gap-6 max-2xl:gap-8 xl:flex">
            <div class="flex items-center">
                @foreach ($headerMenu as $item)
                    @php
                        $isActive = request()->url() === $item['url'] || preg_match($item['urlRegex'], request()->getPathInfo());
                    @endphp
                    <a href="{{ $item['url'] }}" @class([
                        'flex items-center relative gap-1 px-3 h-[40px] group rounded-lg hover:bg-primary-1000',
                        'max-2xl:px-3',
                        'active' => $isActive,
                    ])>
                        <div class="h-[20px] w-[20px]">
                            <img class="h-full w-full object-contain"
                                src="{{ asset('asset/icons/menu-icons/' . $item['icon'] . ($isActive ? '-active' : '-default') . '.svg') }}"
                                alt="Menu {{ $item['title'] }}" />
                        </div>
                        <span @class([
                            'text-sm font-medium text-neutral-200 truncate uppercase',
                            'text-primary' => $isActive,
                        ])>
                            {{ $item['title'] }}
                        </span>

                        @if (isset($item['label']) && $item['label'] !== '')
                            <span
                                class="menu-badge-{{ $item['label'] }} absolute right-0 top-[-5px] grid h-[15px] min-w-[30px] place-items-center rounded bg-[linear-gradient(263.15deg,#FF3838_3.1%,#DB0505_96.9%)] px-1 text-[10px] font-medium capitalize leading-[15px] text-white">
                                {{ $item['label'] }}
                            </span>
                        @endif
                    </a>
                @endforeach
            </div>
            <a class="group relative block h-[40px] shrink-0 transition-all duration-300 hover:drop-shadow-[0_0_4px_#fff]"
                href="/khuyen-mai?tab=promotions">
                <img src="{{ asset('asset/images/header/btn-header-promotion.avif') }}" alt="bg-promotion" class="h-full object-contain" />
                <div class="absolute left-0 top-1/2 -translate-y-1/2 pl-[18px] text-sm whitespace-nowrap font-medium text-black">
                    Khuyến Mãi
                </div>
            </a>
        </div>
        {{-- header auth --}}
        <div class="ml-auto">
            @if ($isLoggedIn)
                <div class="flex w-full items-center gap-2">
                    {{-- open live chat --}}
                    <x-kit.icon-button class="max-xl:hidden" onclick="openLiveChat(event)">
                        <i class="icon-247 text-[24px] text-neutral"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span><span class="path6"></span><span class="path7"></span><span class="path8"></span><span class="path9"></span></i>
                    </x-kit.icon-button>
                    {{-- notification --}}
                    <x-ui.notification.index />
                    <div class="hidden h-[40px] items-center overflow-hidden rounded-lg xl:flex">
                        <div
                            class="flex h-full items-center rounded-l-[inherit] border border-r-0 border-[#80D59C] bg-[#FFFFFF0D] px-2 py-1">
                            {{-- refresh --}}
                            <div class="group flex aspect-square h-[24px] cursor-pointer items-center justify-center"
                                onclick="refreshBalance(event)">
                                <i class="icon-refresh text-[24px] text-neutral-350 group-hover:text-neutral"></i>
                            </div>
                            <div class="line mx-2 h-full w-[1px] bg-neutral-350"></div>
                            {{-- user info --}}
                            <div class="flex min-w-[40px] max-w-[100px] flex-col items-end gap-[3px] overflow-hidden">
                                <p class="line-clamp-1 w-full break-all text-[10px] leading-[10px] text-neutral-350 text-right">
                                    {{ $user->fullname ?? ($user->username ?? '') }}
                                </p>
                                <p id="balance"
                                    class="js-user-balance line-clamp-1 text-[14px] font-semibold leading-[14px] text-neutral">
                                    {{ formatToCurrency($user->balance ?? 0) }}
                                </p>
                            </div>
                        </div>
                        {{-- deposit --}}
                        <x-kit.button link="/account/deposit" class="w-[122px] rounded-none xl:leading-[18px]"
                            prefixIcon="icon-deposit-header">
                            Nạp Tiền
                        </x-kit.button>
                    </div>
                    {{-- avatar --}}
                    <a href="/account/overview" class="size-10 overflow-hidden rounded-lg grid place-items-center max-xl:hidden relative cursor-pointer group duration-300 transition-all bg-neutral-650 backdrop-blur-[2px] disabled:opacity-50 disabled:cursor-not-allowed [&.disabled]:opacity-50 [&.disabled]:cursor-not-allowed xl:hover:bg-neutral-600 xl:w-[40px] xl:h-[40px] w-[32px] h-[32px]">
                        <img src="{{ asset('asset/icons/user.svg') }}" class="size-[24px]" alt="avatar">
                    </a>
                    <a class="flex h-[32px] items-center gap-2 overflow-hidden rounded-lg bg-neutral-650 p-1 pl-[10px] xl:hidden"
                        href="/account/deposit">
                        <p class="max-w-[84px] truncate text-right text-xs text-neutral">{{ formatToCurrency($user->balance ?? 0) }}</p>
                        <div class="flex aspect-square h-[24px] shrink-0 items-center justify-center rounded-lg bg-primary">
                            <i class="icon-plus text-[16px] text-neutral-800"></i>
                        </div>
                    </a>
                </div>
            @else
                <div class="flex w-full items-center gap-2">
                    <x-kit.icon-button class="max-xl:hidden" onclick="openLiveChat()" aria-label="Support">
                        <i class="icon-247 text-[24px] text-neutral"><span class="path1"></span><span class="path2"></span><span class="path3"></span><span class="path4"></span><span class="path5"></span><span class="path6"></span><span class="path7"></span><span class="path8"></span><span class="path9"></span></i>
                    </x-kit.icon-button>
                    <x-kit.button variant="secondary" class="w-[128px] max-xl:h-[32px] max-xl:w-[90px]" onclick="login()">
                        Đăng Nhập
                    </x-kit.button>
                    <x-kit.button class="w-[128px] max-xl:h-[32px] max-xl:w-[90px]" onclick="signup()">
                        Đăng Ký
                    </x-kit.button>
                </div>
            @endif
        </div>
    </div>
</header>
<div class="header-space h-[56px]"></div>
@pushOnce('scripts')
    @vite('resources/js/layout.js')
@endPushOnce
