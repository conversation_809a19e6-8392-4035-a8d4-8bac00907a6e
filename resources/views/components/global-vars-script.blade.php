@php
    $currentUser = Auth::user();
    $allowedCookieKeys = array_diff(config('user.all_user_cookie'), config('user.private_user_cookie'));
    $config = [
        'isLoggedIn' => Auth::check(),
        'brandName' => config('app.brand_name'),
        'limitUserBank' => config('constants.limitUserBank'),
        'currency' => config('app.currency'),
        'liveChatUrl' => config('constants.live_chat_url'),
        'formRules' => config('validation.rules'),
        'validationMessages' => config('validation.messages'),
        'allowedCookieKeys' => array_values($allowedCookieKeys),
    ];

    if ($currentUser) {
        $config['user'] = $currentUser;
        $config['defaultPackageId'] = config('constants.PROMOTION_PLAN_TYPE.COMMISSION');
        $config['currentUserPackageId'] = $currentUser->package_id ?? null;
        $config['isWelcomePromotion'] = $currentUser->package_id === config('constants.PROMOTION_PLAN_TYPE.WELCOME') ? 1 : 0;
        $config['isUpdatedFullname'] = $currentUser->is_updated_fullname ?? ($currentUser->fullname !== $currentUser->username);
    }
@endphp

<script>
    // Browser detection
    const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    var globalGoogleRecaptchaSiteKey = "{{ config('google.recaptcha.site_key') }}";
    // App state
    let isRefreshing = false;
    let isLoggingOut = false;

    // Config variables
    const appConfig = @json($config);

    // Destructure config for global use
    const {
        isLoggedIn,
        brandName: globalBrandName,
        limitUserBank,
        liveChatUrl,
        formRules,
        validationMessages,
        user,
        defaultPackageId,
        currentUserPackageId,
        isWelcomePromotion,
        currency,
        allowedCookieKeys,
    } = appConfig;
    let isUpdatedFullname = appConfig.isUpdatedFullname;
    const isGoogleRecaptchaEnabled = @json(config('google.recaptcha.enabled'));
</script>
