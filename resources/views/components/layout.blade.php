@props(['class' => ''])
@php
    $brandName = config('app.brand_name');
    use App\Helpers\DetectDeviceHelper;
    use <PERSON><PERSON><PERSON>\Agent\Agent;

    $agent = new Agent();
    $isMobile = $agent->isMobile();
    $isAccountPage = request()->is('account*');
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name='dmca-site-verification' content='bzJTTURMbk9NSkMzK2VCZVdwV29sdz090' />
    <meta name="google-site-verification" content="p-b0diS2neNOXgPfNktPbeycU5amAFZai0j3Xii1cYg" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&display=swap">
    <link rel="stylesheet" href="{{ asset('asset/fonts/icomoon/style.css?v=1.0.2') }}">
    {{-- Icon --}}
    <link rel="icon" href="{{ asset(strtolower($brandName) . '-favicon.ico?v=1.0.1') }}" type="image/x-icon">
    @include('components.seo-header')
    @include('components.global-vars-script')
    <!-- Styles -->
    @vite(['resources/sass/app.scss', 'resources/js/app.js', 'resources/js/lib/sweetAlert2.js', 'resources/js/common/popup.js'])
    @stack('css')
    @stack('head')
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer', '{{ env('GTM_ID') }}');</script>
    <!-- End Google Tag Manager -->
    @if (config('google.recaptcha.enabled'))
        <!-- Google Recaptcha -->
        <script async src="https://www.google.com/recaptcha/api.js?render={{config('google.recaptcha.site_key')}}" defer></script>
    @endif

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-QSS5HSJPSB"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', '{{ env('GA_ID') }}');
    </script>
    <!-- End Google Tag (gtag.js) -->
</head>

<body class="{{ \App\Helpers\BodyClass::make() }} {{ $class }}">
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id={{ env('GTM_ID') }}"
            height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->

    @if (!empty($seo->h1Content))
        <h1 class="hidden">{{ $seo->h1Content }}</h1>
    @endif


    @hasSection('promotionDetail')
        <div class="hidden max-xl:block header-back">
            <div class="text-white">
                @yield('promotionDetail')
            </div>
        </div>
    @endif
    <x-header></x-header>
    @hasSection('account-header')
        @yield('account-header')
    @endif
    <div @class([
        'site-wrapper relative',
        'menu-open' => !$isMobile,
        'right-section-open' => !$isAccountPage
    ])>
        @if (!request()->is('vesocao'))
            <div class="left-menu fixed left-0 top-[56px] z-[100] h-full">
                <x-layout.left-menu></x-layout.left-menu>
            </div>
            <div id="leftSiteMenuOverlay" class="left-menu-overlay hidden xl:hidden"></div>
        @endif
        <div class="{{ request()->is('vesocao') ? '' : 'site-content' }}">
            @hasSection('breadcrumb')
                <div class="page-breadcrumb container relative z-[1]">
                    @yield('breadcrumb')
                </div>
            @endif
            @hasSection('breadcrumbPromotion')
                <div class="page-breadcrumb mx-auto w-full container max-xl:relative max-xl:z-[1]">
                    @yield('breadcrumbPromotion')
                </div>
            @endif
            <div class="page-content">
                <div class="{{ request()->is('vesocao') ? '' : 'container' }}">
                    {{ $slot }}
                </div>
            </div>
            <x-footer></x-footer>
        </div>
        @if (!$isAccountPage)
            <x-layout.right-home-section />
        @endif
    </div>
    <x-layout.float-menu />
    <x-ui.footer.menu-bottom-mb></x-ui.footer.menu-bottom-mb>
    <div id="generic-modal"></div>
    <div id="noti-modal"></div>
    <x-ui.minigame.index />
    <go-jackpot class="relative z-[110] block" pos="auto 10px 10% auto"></go-jackpot>
    @hasSection('beforeBodyClose')
        @yield('beforeBodyClose')
    @endif
    @include('components.common-script')
    @stack('scripts')
    @if (session('sessionExpired'))
        @php
            session()->forget('sessionExpired');
        @endphp
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                window.dispatchEvent(
                    new CustomEvent("swal:confirm", {
                        detail: {
                            title: 'HẾT HẠN ĐĂNG NHẬP',
                            html: 'Vui lòng đăng nhập lại để tiếp tục trải nghiệm.',
                            icon: 'success',
                            confirmButtonText: 'Đóng',
                            customClass: {
                                icon: 'user-warning',
                                confirmButton: '!hidden'
                            },
                            cancelButtonText: 'Đăng Nhập',
                            cancelButtonFunc: () => {
                                window.login()
                            },
                            confirmButtonFunc: () => {}
                        }
                    })
                );
                history.pushState(null, '', '/');
            });
        </script>
    @endif
    @if (session('TokenExpired'))
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                window.dispatchEvent(
                    new CustomEvent("swal:confirm", {
                        detail: {
                            title: 'LỖI ĐĂNG NHẬP',
                            html: 'Không tìm thấy người dùng',
                            icon: 'success',
                            confirmButtonText: 'Đóng',
                            customClass: {
                                icon: 'user-warning',
                                confirmButton: '!hidden'
                            },
                            cancelButtonText: 'Đăng Nhập',
                            cancelButtonFunc: () => {
                                window.login()
                            },
                            confirmButtonFunc: () => {}
                        }
                    })
                );
            });
        </script>
    @endif
    @if (session('showLogin'))
        @php
            session()->forget('showLogin');
        @endphp
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                history.pushState(null, '', '/');
                window.login()

            })
        </script>
    @endif
    @if (session('showRegister'))
        @php
            session()->forget('showRegister');
        @endphp
        <script>
            document.addEventListener('DOMContentLoaded', () => {
                window.signup()
                history.pushState(null, '', '/');
            })
        </script>
    @endif
    <script type="module"  src="{{ asset('asset/js/lib/float-go-icon1.js') }}"></script>
</body>
</html>
