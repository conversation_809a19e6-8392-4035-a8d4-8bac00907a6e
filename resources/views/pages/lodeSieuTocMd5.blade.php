@php
    $token = Auth::user()?->tp_token;
@endphp
<x-layout class="[&_.header]:max-xl:hidden [&_.header-space]:max-xl:hidden [&_.menu-bottom]:max-xl:hidden [&_.container]:p-0">
    <div>
        <div class="size-full min-h-[calc(100dvh-74px)]">
            <div class="loading-iframe">
                <x-ui.loading-iframe />
            </div>
            <div class="size-full hidden min-h-[calc(100dvh-74px)]" id="iframe-wrapper">
                <c2-ldmd5 token="{{ $token }}" color="#072C20" margin-bottom="0px" login="/sieu-toc-md5?modal=login" class="w-full"></c2-ldmd5>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            if (@json(!$token) && urlParams.get('modal') === 'login') {
                window.login();
            }

            const iframeWrapper = document.getElementById('iframe-wrapper');
            iframeWrapper.classList.remove('hidden');

            window.loadExternalScript('https://assets.vgjt.info/js/ldmd5.js', () => {
                hideLoadingIframe();
            }, 'module')
        })
    </script>
    <x-scripts.check-when-close-modal />
</x-layout>

