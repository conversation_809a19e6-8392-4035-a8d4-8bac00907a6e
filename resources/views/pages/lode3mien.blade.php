@php
    $token = Auth::user()?->tp_token;
@endphp
<x-layout>  
    <div>
        <div class="size-full min-h-[calc(100dvh-74px)]">
            <div class="loading-iframe">
                <x-ui.loading-iframe />
            </div>
            <div class="size-full hidden min-h-[calc(100dvh-74px)]" id="iframe-wrapper">
                <c2-lode3m token="{{ $token }}" color="#072C20"></c2-lode3m>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const iframeWrapper = document.getElementById('iframe-wrapper');
            iframeWrapper.classList.remove('hidden');
            
            window.loadExternalScript('https://assets.vgjt.info/js/ld3m.js', () => {
                hideLoadingIframe();
            }, 'module')
        })
    </script>
    <x-scripts.check-when-close-modal />
</x-layout>

