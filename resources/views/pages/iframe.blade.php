<x-layout>
    <div class="w-full h-full min-h-[calc(100dvh-74px)] flex items-center justify-center">
        @if(!empty($url))
        <div class="size-full min-h-[calc(100dvh-74px)]">
            <div class="loading-iframe">
                <x-ui.loading-iframe />
            </div>
            <div class="size-full hidden" id="iframe-wrapper">
                <iframe src="{{ $url }}" class="w-full h-full min-h-[calc(100dvh-74px)]"></iframe>
            </div>
        </div>
        @else
        <div class="flex flex-col items-center justify-center h-full">
            <img src="{{ asset('asset/images/maintenance.png') }}" alt="Bảo trì" class="w-[530px] h-auto mb-6 object-contain">
            <h1 class="text-[18px] leading-[22px] font-bold text-white mb-4"><PERSON><PERSON><PERSON> ch<PERSON>i đang đư<PERSON><PERSON> bảo trì</h1>
            <p class="text-white text-[14px] leading-[18px] text-center max-w-md">
                Chúng tôi đang nâng cấp hệ thống để mang đến trải nghiệm tốt hơn.
                Xin vui lòng quay lại sau.
            </p>
            <x-kit.button
                link="{{ route('home') }}"
                variant="primary"
                class="w-[128px] max-xl:w-[90px] max-xl:h-[32px] mt-6 mb-6"
            >
                Trang chủ
            </x-kit.button>

        </div>
        @endif
    </div>
    @pushOnce('scripts')
    <script>
        setTimeout(() => {
            hideLoadingIframe();
        }, 1000);
    </script>
    @endPushOnce
    <x-scripts.check-when-close-modal />
</x-layout>
