@php
    use App\Helpers\DetectDeviceHelper;
    $isMobile = DetectDeviceHelper::isMobile();
    $user = Auth::user();
@endphp

<x-layout>
    <div class="">

        @section('breadcrumbPromotion')
            {{ Breadcrumbs::render('events', __('events.tabs.' . $currentTab)) }}
        @endsection

        <div class="tabs">
            <div class="flex h-8 items-center gap-[8px] xl:h-[40px]">
                @foreach ($tabs as $index => $tab)
                    <button data-tab="{{ $index }}" data-key="{{ $tab['key'] }}"
                        class="tab-title {{ $tab['key'] === $currentTab ? 'event-tab-active' : 'event-tab' }} flex h-[40px] cursor-pointer items-center justify-center gap-[8px] rounded-[80px] text-[14px] font-[500] capitalize leading-[18px] max-xl:h-8 max-xl:gap-[6px] max-xl:text-[12px] max-xl:leading-[16px] xl:w-[131px]"
                        style="width: {{ $isMobile ? $tab['widthMb'] : '131' }}px;">
                        <i class="{{ $tab['icon'] }} text-[20px] leading-[0] max-xl:text-[16px]"></i>
                        {{ $tab['title'] }}
                    </button>
                @endforeach
            </div>
            <div class="events-tabs pb-5 xl:pb-[80px]">
                <div class="mx-auto w-full">
                    <div class="">
                        <div class="tab-contents mt-4 xl:mt-[24px]">
                            @foreach ($tabs as $index => $tab)
                                <div class="tab-content {{ $tab['key'] === $currentTab ? 'active' : '' }}"
                                    data-tab="{{ $index }}"
                                    style="{{ $tab['key'] === $currentTab ? 'display: block;' : 'display: none;' }}">
                                    @if (empty($tab['content']))
                                        <div
                                            class="flex h-full min-h-[calc(100svh_-_300px)] flex-col items-center justify-center text-center text-gray-500 xl:min-h-[528px]">
                                            <img class="mb-[8px] size-[70px]" src="/asset/images/events/empty-icon.svg"
                                                alt="Không có sự kiện">
                                            <p class="mb-1 text-[12px] font-[400] leading-[16px] text-[#AFBBB7]">Không
                                                có dữ liệu</p>
                                        </div>
                                    @else
                                        <div
                                            class="grid grid-cols-2 gap-[20px] pb-[40px] max-xl:gap-[12px] md:grid-cols-3 lg:grid-cols-4">
                                            @foreach ($tab['content'] as $item)
                                                <a @class([
                                                    'promotion-item',
                                                    'cursor-default relative' => !empty($item['ended']),
                                                ]) href="#"
                                                    onclick="handleBannerEventClick(event, {{ json_encode($item) }})">
                                                    @if (!empty($item['ended']))
                                                        <div
                                                            class="absolute right-1 top-1 z-10 rounded-full text-[12px] font-[400] uppercase leading-[16px] text-black bg-neutral-200 max-xl:overflow-hidden max-xl:truncate max-xl:text-[10px] max-xl:leading-[16px] max-xl:py-0.5 max-xl:px-1 py-1 px-2">
                                                            Đã kết thúc</div>
                                                    @endif

                                                    <picture>
                                                        <img src="{{ $item['imgUrlMb'] }}" alt="{{ $item['title'] }}"
                                                            class="hidden aspect-[1/1] w-full rounded-[12px] object-cover max-xl:block">
                                                        <img src="{{ $item['imgUrl'] }}" alt="{{ $item['title'] }}"
                                                            class="aspect-[1/1] w-full rounded-[12px] object-cover max-xl:hidden">
                                                    </picture>
                                                    <div class="mt-3 max-xl:mt-1">
                                                        <div
                                                            class="mb-1 text-[16px] font-[500] leading-[20px] text-white max-xl:mb-[2px] max-xl:overflow-hidden max-xl:truncate max-xl:text-[12px] max-xl:leading-[16px]">
                                                            {{ $item['title'] }}
                                                        </div>
                                                        <div
                                                            class="text-[14px] font-[400] leading-[18px] text-[#AFBBB7] max-xl:overflow-hidden max-xl:truncate max-xl:text-[10px] max-xl:leading-[16px] max-xl:text-neutral-300">
                                                            {{ $item['content'] }}
                                                        </div>
                                                    </div>
                                                </a>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            document.querySelectorAll('.tab-title').forEach(function(tabTitle) {
                tabTitle.addEventListener('click', function() {
                    if (this.classList.contains('event-tab-active')) {
                        return;
                    }
                    const tabKey = this.getAttribute('data-key');
                    const tabIndex = this.getAttribute('data-tab');

                    const url = new URL(window.location);
                    url.searchParams.set('tab', tabKey);
                    window.history.pushState({}, '', url);

                    document.querySelectorAll('.tab-title').forEach(function(title) {
                        title.classList.remove('event-tab-active');
                        title.classList.add('event-tab');
                    });
                    const tabTitle = this.innerText;
                    document.querySelector('.breadcrumb-item.active').innerText = tabTitle;

                    this.classList.remove('event-tab');
                    this.classList.add('event-tab-active');

                    document.querySelectorAll('.tab-content').forEach(function(content) {
                        content.style.display = 'none';
                    });
                    document.querySelector(`.tab-content[data-tab="${tabIndex}"]`).style.display = 'block';
                });
            });

            function handleBannerEventClick(event, banner) {
                event.preventDefault();

                if (banner.ended) {
                    return;
                };

                const isMobile = @json($isMobile);
                const appUrl = "{{ config('app.url') }}";

                if (banner.required_login && banner.required_login == true) {
                    const isLoggedIn = @json(Auth::check());
                    if (!isLoggedIn) {
                        login();
                        return;
                    }
                }

                window.location.href = banner.actionUrl;
            }
        </script>
    @endpush
</x-layout>
