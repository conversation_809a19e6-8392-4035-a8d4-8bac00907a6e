// Huyen thoai om hu
window.addEventListener("DOMContentLoaded", () => {
    let isTabSwitching = false;

    // Constants
    const SELECTORS = {
        tab: '.tab-item',
        tabDates: '.tab-dates',
        content: '.ranking-content',
        loading: '.ranking-loading',
        contentList: '.ranking-content-list',
        emptyData: '#ranking-empty-data',
        jackpotBox: {
            large: '.jackpot-box-large',
            small: '.jackpot-box-small'
        }
    };

    // API calls
    async function getNewWeeklyData(startDate, endDate) {
        try {
            const eventEndpoint = window.eventEndpoint;
            const { status, data, dataHtml } = await fetchData(
                "/jackpot-legends/ranking",
                { startTime: startDate, endTime: endDate, eventEndpoint },
                { useProxy: false }
            );
            return status === "OK" ? { data, dataHtml } : [];
        } catch (error) {
            console.error('Error fetching weekly data:', error);
            return [];
        }
    }

    // UI Updates
    function updateTabState(tabIndex) {
        document.querySelectorAll(SELECTORS.tab).forEach(tab => {
            const isActive = tab.dataset.tab == tabIndex;
            tab.classList.toggle("active", isActive);
            tab.classList.toggle("default", !isActive);
            tab.classList.toggle("pb-[4px]", isActive);
            tab.classList.toggle("pb-[6px]", !isActive);
        });
        const activeTab = $(`div.tab-item[data-tab="${tabIndex}"]`);
        const parent = activeTab?.parent();
        if (activeTab && parent) {
            parent.animate({
                scrollLeft: activeTab.offset().left - parent.offset().left
            }, 300);
        }
    }

    function updateContentState(isFetching) {
        const tabDates = document.querySelector(SELECTORS.tabDates);
        const content = document.querySelector(SELECTORS.content);
        const loading = content.querySelector(SELECTORS.loading);
        const contentList = content.querySelector(SELECTORS.contentList);

        loading.classList.toggle("hidden", !isFetching);
        loading.classList.toggle("flex", isFetching);
        contentList.classList.toggle("hidden", isFetching);
        tabDates.classList.toggle("pointer-events-none", isFetching);
    }

    // Jackpot Updates
    const updateJackpot = (largeJackpotList, smallJackpotList) => {
        const updateJackpotValue = (list, type) => {
            if (!list?.length) return;

            // Update main jackpot
            const mainValue = list[0]?.amount;
            const roundedMainValue = roundDownToNearest(mainValue);
            const mainElement = $(`${SELECTORS.jackpotBox[type]} .${type}-box-jackpot .js-number-animation`);

            if (mainElement.length) {
                mainElement.text(roundedMainValue);
                animateCounter(mainElement, roundedMainValue, mainValue, 3000);
            }

            // Update sub jackpots
            list.slice(1).forEach((item, index) => {
                const itemElement = $(`.${type}-jackpot-item-${index + 2}`);
                if (!itemElement.length) return;

                const roundedValue = roundDownToNearest(item.amount);
                const numberElement = itemElement.find(".js-number-animation");
                numberElement.text(roundedValue);
                animateCounter(numberElement, roundedValue, item.amount, 3000);
            });
        };

        updateJackpotValue(largeJackpotList, 'large');
        updateJackpotValue(smallJackpotList, 'small');
    };

    // Tab Switching
    const switchTab = async (tabIndex, startDate, endDate) => {
        if (isTabSwitching) return;

        try {
            isTabSwitching = true;
            $('.ranking-table-header').addClass('hidden');
            $('.current-user-wrapper').addClass('hidden');
            updateContentState(true);
            updateTabState(tabIndex);

            const newWeeklyData = await getNewWeeklyData(startDate, endDate);
            if (!newWeeklyData?.data) return;

            const newUserList = newWeeklyData.data.list;
            $(SELECTORS.contentList).empty().append(newWeeklyData.dataHtml);
            $(SELECTORS.emptyData).toggleClass("hidden", newUserList.length > 0);
            $('.ranking-table-header').toggleClass("hidden", newUserList.length <= 0);
            setTimeout(() => {
                $(SELECTORS.contentList).scrollTop(0);
            });

            // Update position
            const dataPosition = newWeeklyData?.data?.position
            $('.current-user-wrapper')?.toggleClass("hidden", isLoggedIn && (newUserList.length <= 0 || !dataPosition));
            if (dataPosition) {
                $('.current-user-rank').text(dataPosition?.index > 0 && dataPosition?.index <= 50 ? dataPosition?.index : '50+');
                $('.current-user-turnover').text(formatAmount(dataPosition?.turnover) || '-');
                $('.current-user-amount').text(formatAmount(dataPosition?.amount) || '-');
            }

            // Update jackpot with sliced data
            const largeJackpotList = newUserList.slice(0, 10);
            const smallJackpotList = newUserList.slice(10, 21);
            updateJackpot(largeJackpotList, smallJackpotList);
        } catch (error) {
            console.error('Error switching tab:', error);
        } finally {
            updateContentState(false);
            isTabSwitching = false;
        }
    };

    // Export function
    window.eventSwitchTab = switchTab;

    // Tooltip
    $('.tooltip-container').on('mouseenter', function(event) {
        const tooltip = $(this).find('.tooltip');
        const { top } = $(this)?.get(0)?.getBoundingClientRect()
        tooltip.toggleClass('tooltip-bottom', top <= 112 + tooltip.innerHeight());
    });
});
