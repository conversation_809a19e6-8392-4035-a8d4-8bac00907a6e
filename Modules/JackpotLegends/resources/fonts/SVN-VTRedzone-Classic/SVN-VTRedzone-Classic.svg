<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata>
Created by FontForge 20201107 at Fri May 30 04:57:59 2025
 By 
Copyright (c) 2020 by VarsityType. All rights reserved. Viet hoa boi STYLEno.1 Fonts
</metadata>
<defs>
<font id="SVN-VTRedzone-Classic" horiz-adv-x="1070" >
  <font-face 
    font-family="SVN-VT Redzone Classic"
    font-weight="400"
    font-stretch="normal"
    units-per-em="2048"
    panose-1="0 0 5 0 0 0 0 0 0 0"
    ascent="1676"
    descent="-372"
    x-height="1303"
    cap-height="1303"
    bbox="-400 -382 2108 1970"
    underline-thickness="93"
    underline-position="-278"
    unicode-range="U+0020-FB01"
  />
<missing-glyph horiz-adv-x="1024" 
d="M190 1676h650v-2048h-650v2048zM681 1471v67h-337v-67h133v-76h-134v-68h338v68h-134v76h134zM547 1130v143h-204v-212h338v69h-134zM410 1205h67v-75h-67v75zM681 938v67h-338v-67h134v-76h-134v-67h204v143h134zM681 678v205h-67v-135h-271v-70h338zM681 402v231h-204
v-114h70v47h67v-97h-204v164h-67v-231h338zM681 48v231h-338v-231h338zM410 212h204v-97h-204v97zM681 -67v67h-338v-67h143l-143 -97v-67h338v67h-208l143 97h65z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1489" 
d="M950 1303l-149 -270h-373v-232h428l-149 -270h-279v-531h-307v1164l-65 139h894zM1015 1303h372v-1303h-307v1164z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1024" 
d="M190 1676h650v-2048h-650v2048zM681 1471v67h-337v-67h133v-76h-134v-68h338v68h-134v76h134zM547 1130v143h-204v-212h338v69h-134zM410 1205h67v-75h-67v75zM681 938v67h-338v-67h134v-76h-134v-67h204v143h134zM681 678v205h-67v-135h-271v-70h338zM681 402v231h-204
v-114h70v47h67v-97h-204v164h-67v-231h338zM681 48v231h-338v-231h338zM410 212h204v-97h-204v97zM681 -67v67h-338v-67h143l-143 -97v-67h338v67h-208l143 97h65z" />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" horiz-adv-x="682" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="512" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="493" 
d="M93 521v782h307v-782l-9 -139h-289zM93 0v270h307v-270h-307z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="819" 
d="M84 884l-37 419h325l-37 -419h-251zM484 884l-37 419h326l-38 -419h-251z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="1202" 
d="M214 307h-140v233h140v223h-140v233h140v196h270v-196h233v196h272v-196h139v-233h-139v-223h139v-233h-139v-195h-272v195h-233v-195h-270v195zM484 763v-223h233v223h-233z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="921" 
d="M393 778h249q102 0 158 -56t56 -158v-210q0 -104 -55.5 -159t-158.5 -55h-37v-140h-270v140h-121l-130 232h463q39 0 39 39v95q0 40 -39 40h-249q-105 0 -159.5 54.5t-54.5 159.5v190q0 105 54.5 159.5t159.5 54.5h37v139h270v-139h270l-130 -233h-352q-39 0 -39 -39v-75
q0 -39 39 -39z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1558" 
d="M443 559h-225q-144 0 -144 143v458q0 143 144 143h225q143 0 143 -143v-458q0 -143 -143 -143zM365 0l624 1303h204l-623 -1303h-205zM251 1119v-376q0 -26 26 -26h107q26 0 26 26v376q0 26 -26 26h-107q-26 0 -26 -26zM1341 0h-226q-143 0 -143 143v458q0 144 143 144
h226q143 0 143 -144v-458q0 -143 -143 -143zM1149 560v-376q0 -26 26 -26h106q26 0 26 26v376q0 26 -26 26h-106q-26 0 -26 -26z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1135" 
d="M977 0h-633q-122 0 -186.5 65t-64.5 186v296l117 118l-117 117v270q0 122 64.5 186.5t186.5 64.5h652l-149 -270h-400q-47 0 -47 -46v-140q0 -46 47 -46h670l-140 -255v-546zM400 484v-167q0 -47 47 -47h223v261h-223q-47 0 -47 -47z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="418" 
d="M84 884l-37 419h325l-37 -419h-251z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="577" 
d="M512 1396v-232h-101q-39 0 -39 -39v-946q0 -39 39 -39h101v-233h-205q-102 0 -158 56t-56 158v1061q0 102 56 158t158 56h205z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="577" 
d="M166 1164h-101v232h205q102 0 158 -56t56 -158v-1061q0 -102 -56 -158t-158 -56h-205v233h101q39 0 39 39v946q0 39 -39 39z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="774" 
d="M646 1214l82 -259l-197 -15l152 -128l-219 -159l-79 185l-78 -186l-219 158l152 130l-193 15l80 259l169 -104l-43 193h268l-44 -193z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="819" 
d="M521 778h242v-214h-242v-242h-223v242h-242v214h242v242h223v-242z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="400" 
d="M205 0h-158v270h307v-270q0 -149 -149 -149v149z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="642" 
d="M65 555v233h512v-233h-512z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="400" 
d="M47 0v270h307v-270h-307z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="726" 
d="M0 -186l456 1675h270l-456 -1675h-270z" />
    <glyph glyph-name="zero" unicode="0" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="595" 
d="M47 1033v270h446v-1303h-307v1033h-139z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="1051" 
d="M838 0h-764v410q0 224 222 275l324 73q41 9 41 52v186q0 47 -47 47h-176q-47 0 -47 -47v-119h-307v184q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-275q0 -225 -221 -276l-324 -72q-41 -9 -41 -53v-115h605z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="1061" 
d="M84 242v184h307v-119q0 -46 47 -46h176q47 0 47 46v177q0 47 -47 47h-223v270h223q47 0 47 46v149q0 47 -47 47h-176q-47 0 -47 -47v-102h-307v167q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-279l-117 -117l117 -118v-305q0 -122 -65 -186.5t-186 -64.5h-382
q-121 0 -186 64.5t-65 186.5z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="1057" 
d="M596 270h-549v270l456 763h400v-763h117l-117 -214v-326h-307v270zM596 972l-250 -432h250v432z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="1064" 
d="M97 242v186h307v-119q0 -46 47 -46h176q47 0 47 46v229q0 47 -47 47h-530v718h903l-149 -270h-447v-177h326q122 0 186.5 -65t64.5 -186v-363q0 -122 -64.5 -186.5t-186.5 -64.5h-382q-122 0 -186.5 64.5t-64.5 186.5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="1061" 
d="M93 242v810q0 122 64.5 186.5t186.5 64.5h652l-149 -270h-400q-47 0 -47 -46v-131h326q122 0 186.5 -65t64.5 -186v-363q0 -122 -64.5 -186.5t-186.5 -64.5h-382q-122 0 -186.5 64.5t-64.5 186.5zM400 586v-279q0 -46 47 -46h177q46 0 46 46v233q0 46 -46 46h-224z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="940" 
d="M549 1033h-502v270h847v-270l-281 -581v-452h-308v488q0 60 28 115z" />
    <glyph glyph-name="eight" unicode="8" 
d="M93 242v305l117 118l-117 117v279q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-279l-117 -117l117 -118v-305q0 -122 -64.5 -186.5t-186.5 -64.5h-382q-122 0 -186.5 64.5t-64.5 186.5zM400 996v-149q0 -46 47 -46h177q46 0 46 46v149q0 47 -46 47h-177
q-47 0 -47 -47zM400 484v-177q0 -46 47 -46h177q46 0 46 46v177q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="1061" 
d="M968 1063v-810q0 -121 -65 -186t-186 -65h-484l-149 270h530q47 0 47 46v131h-326q-121 0 -186 64.5t-65 186.5v363q0 121 65 186t186 65h382q121 0 186 -65t65 -186zM661 719v279q0 46 -47 46h-176q-47 0 -47 -46v-233q0 -46 47 -46h223z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="493" 
d="M93 717v270h307v-270h-307zM93 279v270h307v-270h-307z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="493" 
d="M93 438v269h307v-269h-307zM251 0h-158v270h307v-270q0 -149 -149 -149v149z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="716" 
d="M624 300l-559 251v242l559 251v-242l-326 -130l326 -130v-242z" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="642" 
d="M65 741v214h505v-214h-505zM65 387v214h512v-217z" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="716" 
d="M652 793v-242l-559 -251v242l326 130l-326 130v242z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="996" 
d="M354 905h-307v156q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-326q0 -102 -56 -158t-158 -56h-112l-9 -139h-289l-9 139v88q0 88 47 135t135 47h97q47 0 47 47v158q0 47 -47 47h-177q-46 0 -46 -47v-91zM298 0v270h307v-270h-307z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1545" 
d="M93 169v963q0 82 44.5 126.5t126.5 44.5h1017q82 0 126.5 -45t44.5 -128v-679q0 -83 -44.5 -127.5t-126.5 -44.5h-222l-78 80l-82 -80h-272q-82 0 -126.5 44.5t-44.5 127.5v402q0 83 44 127t127 44h462v-527q0 -32 32 -32h76q32 0 32 32v588q0 32 -32 32h-849
q-31 0 -31 -32v-867q0 -32 31 -32h1104l-102 -186h-1087q-170 0 -170 169zM680 806v-309q0 -32 31 -32h123q32 0 32 32v341h-155q-31 0 -31 -32z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1098" 
d="M1005 1052v-270l-117 -117l117 -118v-296q0 -121 -64.5 -186t-186.5 -65h-633v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5zM698 847v140q0 46 -46 46h-224v-232h224q46 0 46 46zM698 317v167q0 47 -46 47h-224v-261h224q46 0 46 47z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1061" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-242h-307v177q0 47 -46 47h-177q-47 0 -47 -47v-689q0 -46 47 -46h177q46 0 46 46v203h307v-268q0 -122 -64.5 -186.5t-186.5 -64.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1098" 
d="M56 1303h698q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v1164zM698 317v670q0 46 -46 46h-224v-763h224q46 0 46 47z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="968" 
d="M707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="968" 
d="M801 1033h-373v-232h428l-149 -270h-279v-531h-307v1164l-65 139h894z" />
    <glyph glyph-name="G" unicode="G" 
d="M977 0h-633q-122 0 -186.5 65t-64.5 186v801q0 122 64.5 186.5t186.5 64.5h652l-149 -270h-400q-47 0 -47 -46v-670q0 -47 47 -47h223v531h307v-801z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1107" 
d="M698 808v495h307v-1303h-307v538h-270v-538h-307v1164l-65 139h372v-495h270z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="530" 
d="M56 1303h372v-1303h-307v1164z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="701" 
d="M233 1303h372v-1052q0 -121 -64.5 -186t-186.5 -65h-177l-149 270h223q47 0 47 47v847z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1117" 
d="M754 1303h335l-272 -631l272 -672h-335l-216 531h-110v-531h-307v1164l-65 139h372v-502h110z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="875" 
d="M410 1303v-1033h437l-149 -270h-596v1303h308z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1414" 
d="M717 786l279 517h317v-1303h-308v763l-288 -517l-289 517v-763h-307v1164l-65 139h382z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1154" 
d="M745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354z" />
    <glyph glyph-name="O" unicode="O" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="P" unicode="P" 
d="M56 1303h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -121 -64.5 -186t-186.5 -65h-326v-404h-307v1164zM698 721v266q0 46 -46 46h-224v-359h224q46 0 46 47z" />
    <glyph glyph-name="Q" unicode="Q" 
d="M344 1313h382q121 0 186 -65t65 -187v-819q0 -208 -176 -244l74 -138h-307l-71 131h-153q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1089" 
d="M877 426l147 -426h-317l-139 404h-140v-404h-307v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -177 -128 -229zM428 1033v-359h224q46 0 46 47v266q0 46 -46 46h-224z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1051" 
d="M84 242v173h307v-108q0 -46 47 -46h186q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119q0 -43 41 -52l324 -73q221 -51 221 -275v-235q0 -122 -64.5 -186.5
t-186.5 -64.5h-391q-121 0 -186 64.5t-65 186.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="958" 
d="M326 1033h-289v270h885v-270h-289v-1033h-307v1033z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1102" 
d="M693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1098" 
d="M372 0l-344 1303h326l195 -890l196 890h326l-345 -1303h-354z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="1657" 
d="M346 0l-299 1303h325l159 -813l162 813h270l163 -813l159 813h325l-301 -1303h-317l-165 741l-164 -741h-317z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1042" 
d="M689 1303h326l-285 -651l285 -652h-326l-168 432l-167 -432h-326l287 652l-287 651h326l167 -430z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1042" 
d="M369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="977" 
d="M65 1303h847v-270l-493 -763h512l-149 -270h-717v270l494 763h-494v270z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="577" 
d="M512 -93h-419v1489h419v-232h-140v-1024h140v-233z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="726" 
d="M726 -186h-270l-456 1675h270z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="577" 
d="M484 1396v-1489h-419v233h140v1024h-140v232h419z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="821" 
d="M289 1303h242l253 -558h-244l-130 326l-129 -326h-244z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="837" 
d="M47 -233v196h744v-196h-744z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="446" 
d="M195 1406l-148 270h270l83 -270h-205z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1098" 
d="M1005 1052v-270l-117 -117l117 -118v-296q0 -121 -64.5 -186t-186.5 -65h-633v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5zM698 847v140q0 46 -46 46h-224v-232h224q46 0 46 46zM698 317v167q0 47 -46 47h-224v-261h224q46 0 46 47z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="1061" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-242h-307v177q0 47 -46 47h-177q-47 0 -47 -47v-689q0 -46 47 -46h177q46 0 46 46v203h307v-268q0 -122 -64.5 -186.5t-186.5 -64.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1098" 
d="M56 1303h698q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v1164zM698 317v670q0 46 -46 46h-224v-763h224q46 0 46 47z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="968" 
d="M707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="968" 
d="M801 1033h-373v-232h428l-149 -270h-279v-531h-307v1164l-65 139h894z" />
    <glyph glyph-name="g" unicode="g" 
d="M977 0h-633q-122 0 -186.5 65t-64.5 186v801q0 122 64.5 186.5t186.5 64.5h652l-149 -270h-400q-47 0 -47 -46v-670q0 -47 47 -47h223v531h307v-801z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1107" 
d="M698 808v495h307v-1303h-307v538h-270v-538h-307v1164l-65 139h372v-495h270z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="530" 
d="M56 1303h372v-1303h-307v1164z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="701" 
d="M233 1303h372v-1052q0 -121 -64.5 -186t-186.5 -65h-177l-149 270h223q47 0 47 47v847z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1117" 
d="M754 1303h335l-272 -631l272 -672h-335l-216 531h-110v-531h-307v1164l-65 139h372v-502h110z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="875" 
d="M410 1303v-1033h437l-149 -270h-596v1303h308z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1414" 
d="M717 786l279 517h317v-1303h-308v763l-288 -517l-289 517v-763h-307v1164l-65 139h382z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1154" 
d="M745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354z" />
    <glyph glyph-name="o" unicode="o" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="p" unicode="p" 
d="M56 1303h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -121 -64.5 -186t-186.5 -65h-326v-404h-307v1164zM698 721v266q0 46 -46 46h-224v-359h224q46 0 46 47z" />
    <glyph glyph-name="q" unicode="q" 
d="M344 1313h382q121 0 186 -65t65 -187v-819q0 -208 -176 -244l74 -138h-307l-71 131h-153q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="1089" 
d="M877 426l147 -426h-317l-139 404h-140v-404h-307v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -177 -128 -229zM428 1033v-359h224q46 0 46 47v266q0 46 -46 46h-224z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="1051" 
d="M84 242v173h307v-108q0 -46 47 -46h186q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119q0 -43 41 -52l324 -73q221 -51 221 -275v-235q0 -122 -64.5 -186.5
t-186.5 -64.5h-391q-121 0 -186 64.5t-65 186.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="958" 
d="M326 1033h-289v270h885v-270h-289v-1033h-307v1033z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1102" 
d="M693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1098" 
d="M372 0l-344 1303h326l195 -890l196 890h326l-345 -1303h-354z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1657" 
d="M346 0l-299 1303h325l159 -813l162 813h270l163 -813l159 813h325l-301 -1303h-317l-165 741l-164 -741h-317z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1042" 
d="M689 1303h326l-285 -651l285 -652h-326l-168 432l-167 -432h-326l287 652l-287 651h326l167 -430z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1042" 
d="M369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="977" 
d="M65 1303h847v-270l-493 -763h512l-149 -270h-717v270l494 763h-494v270z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="688" 
d="M624 -93h-205q-102 0 -158 56t-56 158v397q0 39 -39 39h-101v232h101q39 0 39 40v353q0 102 56 158t158 56h205v-232h-101q-39 0 -39 -39v-350l-100 -101l100 -101v-394q0 -39 39 -39h101v-233z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="549" 
d="M410 -186h-270v1675h270v-1675z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="688" 
d="M270 -93h-205v233h101q39 0 39 39v394l100 101l-100 101v350q0 39 -39 39h-101v232h205q102 0 158 -56t56 -158v-353q0 -40 39 -40h101v-232h-101q-39 0 -39 -39v-397q0 -102 -56 -158t-158 -56z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="735" 
d="M521 659v149h149v-130q0 -67 -36 -103.5t-103 -36.5h-107l-204 149h-6v-149h-149v130q0 67 36.5 103.5t103.5 36.5h106l205 -149h5z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="456" 
d="M382 931v-270h-308v270h308zM382 410v-782h-308v782l10 139h288z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="949" 
d="M866 950v-166h-270v108q0 39 -39 39h-155q-39 0 -39 -39v-481q0 -39 39 -39h155q39 0 39 39v129h270v-186q0 -105 -54.5 -159.5t-159.5 -54.5h-38v-140h-270v140h-37q-103 0 -158.5 55t-55.5 159v596q0 104 55.5 159t158.5 55h37v139h270v-139h38q105 0 159.5 -54.5
t54.5 -159.5z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="1135" 
d="M484 488v-218h596l-149 -270h-745l-149 270h140v218l-119 214h119v359q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-294h458l-119 -214h-339z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="1120" 
d="M1002 512l-71 -71l99 -98l-149 -149l-99 98l-71 -70q-73 -73 -150.5 -73t-150.5 73l-71 70l-99 -98l-149 149l99 98l-71 71q-73 73 -73 151t73 151l71 70l-99 99l149 149l99 -99l71 71q73 73 150.5 73t150.5 -73l71 -71l99 99l149 -149l-99 -99l71 -70q73 -73 73 -151
t-73 -151zM588 439l196 196q28 28 0 56l-196 195q-28 28 -56 0l-195 -195q-28 -28 0 -56l195 -196q28 -28 56 0z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="1098" 
d="M745 1303h326l-319 -750h211l-120 -214h-139v-339h-307v339h-140l-119 214h210l-320 750h326l197 -525z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="549" 
d="M410 735h-270v754h270v-754zM140 596h270v-782h-270v782z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1053" 
d="M84 452v97l117 118l-117 117v277q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-91q0 -43 41 -52l326 -73q221 -51 221 -275v-97l-117 -118l117 -117v-294q0 -121 -64.5 -186t-186.5 -65h-391q-121 0 -186 65t-65 186
v164h307v-99q0 -46 46 -46h187q46 0 46 46v108q0 43 -41 52l-326 73q-221 51 -221 275zM391 586v-111q0 -42 41 -51l240 -53v111q0 41 -41 50z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="726" 
d="M47 1443v233h260v-233h-260zM419 1443v233h261v-233h-261z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1545" 
d="M93 169v963q0 82 44.5 126.5t126.5 44.5h1017q82 0 126.5 -45t44.5 -128v-959q0 -83 -44 -127t-127 -44h-1018q-170 0 -170 169zM317 1085v-867q0 -32 31 -32h849q32 0 32 32v867q0 32 -32 32h-849q-31 0 -31 -32zM866 572h223v-121q0 -83 -44.5 -127.5t-126.5 -44.5
h-291q-82 0 -126.5 44.5t-44.5 127.5v402q0 83 44 127t127 44h291q82 0 126.5 -44t44.5 -127v-106h-223v59q0 32 -32 32h-123q-31 0 -31 -32v-309q0 -32 31 -32h123q32 0 32 32v75z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="660" 
d="M151 1145l-86 158h378q143 0 143 -143v-601h-368q-144 0 -144 143v203q0 68 38 105.5t106 37.5h192v71q0 26 -28 26h-231zM251 864v-121q0 -26 26 -26h133v173h-133q-26 0 -26 -26z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="986" 
d="M317 672l242 -326h-270l-242 326l242 326h270zM698 672l242 -326h-270l-242 326l242 326h270z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="847" 
d="M549 564h-484v214h708v-456h-224v242z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="549" 
d="M65 555v233h419v-233h-419z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="809" 
d="M151 1303h508q86 0 86 -85v-481q0 -85 -86 -85h-510q-84 0 -84 85v481q0 85 86 85zM633 761v434q0 15 -17 15h-424q-15 0 -15 -15v-434q0 -16 15 -16h424q17 0 17 16zM562 1078v-95q0 -51 -31 -71l41 -121h-114l-37 106h-64v-106h-111v373h231q85 0 85 -86zM357 1071v-81
h79q15 0 15 17v49q0 15 -15 15h-79z" />
    <glyph glyph-name="overscore" unicode="&#xaf;" horiz-adv-x="605" 
d="M47 1480v196h512v-196h-512z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="487" 
d="M65 1098v84q0 121 121 121h116q121 0 121 -121v-84q0 -121 -121 -121h-116q-121 0 -121 121zM195 1179v-77q0 -18 19 -18h60q18 0 18 18v77q0 18 -18 18h-60q-19 0 -19 -18z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="875" 
d="M791 862v-214h-242v-242h242v-196h-707v196h242v242h-242v214h242v242h223v-242h242z" />
    <glyph glyph-name="uni00B2" unicode="&#xb2;" horiz-adv-x="660" 
d="M510 559h-436v234q0 128 127 157l184 42q25 6 25 30v97q0 26 -26 26h-103q-26 0 -26 -26v-60h-177v101q0 143 144 143h221q143 0 143 -143v-153q0 -127 -126 -156l-184 -43q-25 -6 -25 -30v-61h345z" />
    <glyph glyph-name="uni00B3" unicode="&#xb3;" horiz-adv-x="651" 
d="M65 702v100h177v-59q0 -26 26 -26h106q26 0 26 26v91q0 26 -26 26h-132v158h132q26 0 26 26v75q0 26 -26 26h-106q-26 0 -26 -26v-50h-177v91q0 143 144 143h225q143 0 143 -143v-155l-67 -67l67 -67v-169q0 -143 -143 -143h-225q-144 0 -144 143z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="446" 
d="M130 1676h270l-149 -270h-204z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1238" 
d="M65 655v397q0 121 65 186t187 65h409v-1303h-270v404h-139q-122 0 -187 65t-65 186zM847 1303h335l-65 -139v-1164h-270v1303z" />
    <glyph glyph-name="middot" unicode="&#xb7;" horiz-adv-x="493" 
d="M93 531v270h307v-270h-307z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="469" 
d="M115 -372l-68 121h217q21 0 21 20v28q0 21 -21 21h-208v121l35 61h157l-36 -61h97q114 0 114 -114v-84q0 -113 -114 -113h-194z" />
    <glyph glyph-name="uni00B9" unicode="&#xb9;" horiz-adv-x="372" 
d="M37 1145v158h261v-744h-177v586h-84z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="660" 
d="M443 559h-225q-144 0 -144 143v458q0 143 144 143h225q143 0 143 -143v-458q0 -143 -143 -143zM251 1119v-376q0 -26 26 -26h107q26 0 26 26v376q0 26 -26 26h-107q-26 0 -26 -26z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="986" 
d="M317 346h-270l242 326l-242 326h270l242 -326zM698 346h-270l242 326l-242 326h270l242 -326z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1381" 
d="M74 1145v158h261v-744h-177v586h-84zM199 0l624 1303h205l-624 -1303h-205zM1065 153h-317v158l267 434h227v-434h65l-65 -123v-188h-177v153zM1065 555l-149 -244h149v244z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1435" 
d="M74 1145v158h261v-744h-177v586h-84zM199 0l624 1303h205l-624 -1303h-205zM1275 0h-435v235q0 127 126 156l185 43q24 6 24 30v96q0 26 -26 26h-103q-26 0 -26 -26v-59h-177v100q0 144 144 144h221q144 0 144 -144v-152q0 -128 -127 -157l-184 -43q-24 -6 -24 -29v-62
h344z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1565" 
d="M74 702v100h177v-59q0 -26 26 -26h107q26 0 26 26v91q0 26 -26 26h-133v158h133q26 0 26 26v75q0 26 -26 26h-107q-26 0 -26 -26v-50h-177v91q0 143 144 143h225q143 0 143 -143v-155l-67 -67l67 -67v-169q0 -143 -143 -143h-225q-144 0 -144 143zM402 0l624 1303h205
l-624 -1303h-205zM1249 153h-316v158l266 434h227v-434h65l-65 -123v-188h-177v153zM1249 555l-149 -244h149v244z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="940" 
d="M689 931v-270h-307v270h307zM633 26h307v-156q0 -122 -65 -187t-186 -65h-382q-121 0 -186 65t-65 187v325q0 102 56 158.5t158 56.5h112l9 139h289l9 -139v-88q0 -88 -47.5 -135t-135.5 -47h-96q-47 0 -47 -47v-158q0 -47 47 -47h176q47 0 47 47v91z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM592 1620l84 -233h-192l-149 233h257z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM441 1387l84 233h257l-149 -233h-192z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM458 1493h-6v-87h-173v111q0 57 36.5 89t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-103.5 -32h-87z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1120" 
d="M242 1676h261v-233h-261v233zM614 1676h261v-233h-261v233zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1120" 
d="M618 1350h-115q-121 0 -121 123v80q0 123 121 123h115q121 0 121 -123v-80q0 -123 -121 -123zM512 1551v-76q0 -19 19 -19h59q19 0 19 19v76q0 19 -19 19h-59q-19 0 -19 -19zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201
l-95 493h-11z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1588" 
d="M1327 531h-279v-261h522l-149 -270h-680v270h-333l-110 -270h-298l482 1164l-65 139h1153l-149 -270h-373v-232h428zM721 1033l-202 -493h222v493h-20z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1061" 
d="M977 1061v-242h-307v177q0 47 -46 47h-177q-47 0 -47 -47v-689q0 -46 47 -46h177q46 0 46 46v203h307v-268q0 -122 -64.5 -186.5t-186.5 -64.5h-167l-30 -52h97q113 0 113 -114v-84q0 -113 -113 -113h-194l-69 121h218q20 0 20 20v28q0 21 -20 21h-209v121l30 52h-58
q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="968" 
d="M707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM559 1620l83 -233h-191l-149 233h257z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="968" 
d="M707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM384 1387l83 233h257l-149 -233h-191z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="968" 
d="M707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="968" 
d="M186 1676h261v-233h-261v233zM559 1676h260v-233h-260v233zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="530" 
d="M56 1303h372v-1303h-307v1164zM307 1620l84 -233h-192l-149 233h257z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="530" 
d="M121 1164l-65 139h372v-1303h-307v1164zM160 1387l84 233h257l-149 -233h-192z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="530" 
d="M330 1406l-75 156l-74 -156h-196l147 270h246l147 -270h-195zM121 1164l-65 139h372v-1303h-307v1164z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="530" 
d="M-61 1676h260v-233h-260v233zM311 1443v233h261v-233h-261zM121 1164l-65 139h372v-1303h-307v1164z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1126" 
d="M84 1303h698q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v560h-102v215h102v389zM726 317v670q0 46 -46 46h-224v-258h121v-215h-121v-290h224q46 0 46 47z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1154" 
d="M424 1555h-5v-149h-149v130q0 67 36.5 103.5t103.5 36.5h106l205 -149h5v149h149v-131q0 -67 -36.5 -103t-103.5 -36h-106zM745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM573 1620l84 -233h-192l-148 233h256z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM423 1387l83 233h257l-149 -233h-191z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM460 1387h-196l147 233h246l147 -233h-195
l-75 119z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM417 1493h-6v-87h-173v111q0 57 36.5 89
t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" 
d="M214 1443v233h261v-233h-261zM586 1443v233h261v-233h-261zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177
q-47 0 -47 -47z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="772" 
d="M289 367h-252l201 301l-201 304h252l98 -158l97 158h251l-201 -304l201 -301h-251l-97 158z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1163" 
d="M1017 1138l100 81v-269l-93 -75v-633q0 -122 -65 -186.5t-186 -64.5h-382q-202 0 -244 175l-100 -82v270l93 74v633q0 122 65 187t186 65h382q202 0 244 -175zM447 996v-318l270 218v100q0 47 -47 47h-177q-46 0 -46 -47zM717 307v319l-270 -218v-101q0 -46 46 -46h177
q47 0 47 46z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1102" 
d="M693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224zM579 1620l84 -233h-192l-149 233h257z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1102" 
d="M693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224zM452 1387l84 233h257l-149 -233h-192z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1102" 
d="M624 1406l-75 156l-74 -156h-196l147 270h246l147 -270h-195zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1102" 
d="M242 1676h261v-233h-261v233zM614 1676h261v-233h-261v233zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1042" 
d="M369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441zM413 1387l84 233h257l-149 -233h-192z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" 
d="M410 0h-308v1303h308v-190h325q122 0 187 -65t65 -186v-397q0 -121 -65 -186t-187 -65h-325v-214zM410 843v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1074" 
d="M512 0v270h115q47 0 47 47v167q0 47 -47 47h-115v270h115q47 0 47 46v149q0 47 -47 47h-176q-47 0 -47 -47v-996h-307v1061q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-279l-117 -117l117 -118v-296q0 -121 -64.5 -186t-186.5 -65h-218z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM592 1620l84 -233h-192l-149 233h257z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM441 1387l84 233h257l-149 -233h-192z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM458 1493h-6v-87h-173v111q0 57 36.5 89t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-103.5 -32h-87z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1120" 
d="M242 1676h261v-233h-261v233zM614 1676h261v-233h-261v233zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1120" 
d="M618 1350h-115q-121 0 -121 123v80q0 123 121 123h115q121 0 121 -123v-80q0 -123 -121 -123zM512 1551v-76q0 -19 19 -19h59q19 0 19 19v76q0 19 -19 19h-59q-19 0 -19 -19zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201
l-95 493h-11z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1588" 
d="M1327 531h-279v-261h522l-149 -270h-680v270h-333l-110 -270h-298l482 1164l-65 139h1153l-149 -270h-373v-232h428zM721 1033l-202 -493h222v493h-20z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="1061" 
d="M977 1061v-242h-307v177q0 47 -46 47h-177q-47 0 -47 -47v-689q0 -46 47 -46h177q46 0 46 46v203h307v-268q0 -122 -64.5 -186.5t-186.5 -64.5h-167l-30 -52h97q113 0 113 -114v-84q0 -113 -113 -113h-194l-69 121h218q20 0 20 20v28q0 21 -20 21h-209v121l30 52h-58
q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="968" 
d="M707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM559 1620l83 -233h-191l-149 233h257z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="968" 
d="M707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM384 1387l83 233h257l-149 -233h-191z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="968" 
d="M707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="968" 
d="M186 1676h261v-233h-261v233zM559 1676h260v-233h-260v233zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="530" 
d="M56 1303h372v-1303h-307v1164zM307 1620l84 -233h-192l-149 233h257z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="530" 
d="M121 1164l-65 139h372v-1303h-307v1164zM160 1387l84 233h257l-149 -233h-192z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="530" 
d="M330 1406l-75 156l-74 -156h-196l147 270h246l147 -270h-195zM121 1164l-65 139h372v-1303h-307v1164z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="530" 
d="M-61 1676h260v-233h-260v233zM311 1443v233h261v-233h-261zM121 1164l-65 139h372v-1303h-307v1164z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1126" 
d="M84 1303h698q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v560h-102v215h102v389zM726 317v670q0 46 -46 46h-224v-258h121v-215h-121v-290h224q46 0 46 47z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1154" 
d="M424 1555h-5v-149h-149v130q0 67 36.5 103.5t103.5 36.5h106l205 -149h5v149h149v-131q0 -67 -36.5 -103t-103.5 -36h-106zM745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM573 1620l84 -233h-192l-148 233h256z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM423 1387l83 233h257l-149 -233h-191z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M460 1387h-196l147 233h246l147 -233h-195l-75 119zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177
q-47 0 -47 -47z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM417 1493h-6v-87h-173v111q0 57 36.5 89
t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M214 1443v233h261v-233h-261zM586 1443v233h261v-233h-261zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177
q-47 0 -47 -47z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="791" 
d="M270 871v224h251v-224h-251zM56 564v214h679v-214h-679zM270 248v223h251v-223h-251z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1163" 
d="M1017 1138l100 81v-269l-93 -75v-633q0 -122 -65 -186.5t-186 -64.5h-382q-202 0 -244 175l-100 -82v270l93 74v633q0 122 65 187t186 65h382q202 0 244 -175zM447 996v-318l270 218v100q0 47 -47 47h-177q-46 0 -46 -47zM717 307v319l-270 -218v-101q0 -46 46 -46h177
q47 0 47 46z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1102" 
d="M693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224zM579 1620l84 -233h-192l-149 233h257z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1102" 
d="M693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224zM452 1387l84 233h257l-149 -233h-192z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1102" 
d="M624 1406l-75 156l-74 -156h-196l147 270h246l147 -270h-195zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1102" 
d="M242 1676h261v-233h-261v233zM614 1676h261v-233h-261v233zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1042" 
d="M369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441zM413 1387l84 233h257l-149 -233h-192z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" 
d="M410 0h-308v1303h308v-190h325q122 0 187 -65t65 -186v-397q0 -121 -65 -186t-187 -65h-325v-214zM410 843v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1042" 
d="M195 1443v233h261v-233h-261zM568 1443v233h261v-233h-261zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="Amacron" unicode="&#x100;" horiz-adv-x="1120" 
d="M307 1676h512v-196h-512v196zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="amacron" unicode="&#x101;" horiz-adv-x="1120" 
d="M307 1676h512v-196h-512v196zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Abreve" unicode="&#x102;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="abreve" unicode="&#x103;" horiz-adv-x="1120" 
d="M1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1120" 
d="M1084 0h-121l-125 -223h236l-84 -149h-195q-97 0 -133 60t12 148l93 164l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1120" 
d="M1084 0h-121l-125 -223h236l-84 -149h-195q-97 0 -133 60t12 148l93 164l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1061" 
d="M503 1676h270l-149 -270h-205zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-242h-307v177q0 47 -46 47h-177q-47 0 -47 -47v-689q0 -46 47 -46h177q46 0 46 46v203h307v-268q0 -122 -64.5 -186.5t-186.5 -64.5z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="1061" 
d="M503 1676h270l-149 -270h-205zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-242h-307v177q0 47 -46 47h-177q-47 0 -47 -47v-689q0 -46 47 -46h177q46 0 46 46v203h307v-268q0 -122 -64.5 -186.5t-186.5 -64.5z" />
    <glyph glyph-name="Ccaron" unicode="&#x10c;" horiz-adv-x="1061" 
d="M408 1406l-147 270h195l75 -157l74 157h196l-148 -270h-245zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-242h-307v177q0 47 -46 47h-177q-47 0 -47 -47v-689q0 -46 47 -46h177q46 0 46 46v203h307v-268
q0 -122 -64.5 -186.5t-186.5 -64.5z" />
    <glyph glyph-name="ccaron" unicode="&#x10d;" horiz-adv-x="1061" 
d="M408 1406l-147 270h195l75 -157l74 157h196l-148 -270h-245zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-242h-307v177q0 47 -46 47h-177q-47 0 -47 -47v-689q0 -46 47 -46h177q46 0 46 46v203h307v-268
q0 -122 -64.5 -186.5t-186.5 -64.5z" />
    <glyph glyph-name="Dcaron" unicode="&#x10e;" horiz-adv-x="1098" 
d="M810 1676l-147 -270h-246l-147 270h195l75 -157l74 157h196zM56 1303h698q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v1164zM698 317v670q0 46 -46 46h-224v-763h224q46 0 46 47z" />
    <glyph glyph-name="dcaron" unicode="&#x10f;" horiz-adv-x="1098" 
d="M810 1676l-147 -270h-246l-147 270h195l75 -157l74 157h196zM56 1303h698q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v1164zM698 317v670q0 46 -46 46h-224v-763h224q46 0 46 47z" />
    <glyph glyph-name="Dcroat" unicode="&#x110;" horiz-adv-x="1126" 
d="M84 1303h698q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v560h-102v215h102v389zM726 317v670q0 46 -46 46h-224v-258h121v-215h-121v-290h224q46 0 46 47z" />
    <glyph glyph-name="dslash" unicode="&#x111;" horiz-adv-x="1126" 
d="M84 1303h698q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v560h-102v215h102v389zM726 317v670q0 46 -46 46h-224v-258h121v-215h-121v-290h224q46 0 46 47z" />
    <glyph glyph-name="Emacron" unicode="&#x112;" horiz-adv-x="968" 
d="M242 1676h512v-196h-512v196zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="emacron" unicode="&#x113;" horiz-adv-x="968" 
d="M242 1676h512v-196h-512v196zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="Edotaccent" unicode="&#x116;" horiz-adv-x="968" 
d="M372 1676h261v-233h-261v233zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="edotaccent" unicode="&#x117;" horiz-adv-x="968" 
d="M372 1676h261v-233h-261v233zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="968" 
d="M428 270h522l-149 -270l-125 -223h236l-83 -149h-196q-97 0 -133 60t12 148l93 164h-484v1164l-65 139h894l-149 -270h-373v-232h428l-149 -270h-279v-261z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="968" 
d="M428 270h522l-149 -270l-125 -223h236l-83 -149h-196q-97 0 -133 60t12 148l93 164h-484v1164l-65 139h894l-149 -270h-373v-232h428l-149 -270h-279v-261z" />
    <glyph glyph-name="Ecaron" unicode="&#x11a;" horiz-adv-x="968" 
d="M773 1676l-147 -270h-246l-147 270h195l75 -157l74 157h196zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="ecaron" unicode="&#x11b;" horiz-adv-x="968" 
d="M773 1676l-147 -270h-246l-147 270h195l75 -157l74 157h196zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="Gbreve" unicode="&#x11e;" 
d="M279 1564v112h177v-93q0 -28 28 -28h130q28 0 28 28v93h177v-112q0 -158 -158 -158h-223q-159 0 -159 158zM977 0h-633q-122 0 -186.5 65t-64.5 186v801q0 122 64.5 186.5t186.5 64.5h652l-149 -270h-400q-47 0 -47 -46v-670q0 -47 47 -47h223v531h307v-801z" />
    <glyph glyph-name="gbreve" unicode="&#x11f;" 
d="M279 1564v112h177v-93q0 -28 28 -28h130q28 0 28 28v93h177v-112q0 -158 -158 -158h-223q-159 0 -159 158zM977 0h-633q-122 0 -186.5 65t-64.5 186v801q0 122 64.5 186.5t186.5 64.5h652l-149 -270h-400q-47 0 -47 -46v-670q0 -47 47 -47h223v531h307v-801z" />
    <glyph glyph-name="uni0122" unicode="&#x122;" 
d="M977 0h-633q-122 0 -186.5 65t-64.5 186v801q0 122 64.5 186.5t186.5 64.5h652l-149 -270h-400q-47 0 -47 -46v-670q0 -47 47 -47h223v531h307v-801zM438 -102h269l-148 -270h-205z" />
    <glyph glyph-name="uni0123" unicode="&#x123;" 
d="M977 0h-633q-122 0 -186.5 65t-64.5 186v801q0 122 64.5 186.5t186.5 64.5h652l-149 -270h-400q-47 0 -47 -46v-670q0 -47 47 -47h223v531h307v-801zM438 -102h269l-148 -270h-205z" />
    <glyph glyph-name="Itilde" unicode="&#x128;" horiz-adv-x="530" 
d="M160 1493h-5v-87h-174v111q0 57 36.5 89t103.5 32h88l162 -87h5v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88zM56 1303h372v-1303h-307v1164z" />
    <glyph glyph-name="itilde" unicode="&#x129;" horiz-adv-x="512" 
d="M160 1493h-5v-87h-174v111q0 57 36.5 89t103.5 32h88l162 -87h5v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="Imacron" unicode="&#x12a;" horiz-adv-x="530" 
d="M-9 1480v196h512v-196h-512zM121 1164l-65 139h372v-1303h-307v1164z" />
    <glyph glyph-name="imacron" unicode="&#x12b;" horiz-adv-x="530" 
d="M-9 1480v196h512v-196h-512zM121 1164l-65 139h372v-1303h-307v1164z" />
    <glyph glyph-name="Iogonek" unicode="&#x12e;" horiz-adv-x="530" 
d="M428 0l-125 -223h237l-84 -149h-195q-97 0 -133 60t12 148l93 164h-112v1164l-65 139h372v-1303z" />
    <glyph glyph-name="iogonek" unicode="&#x12f;" horiz-adv-x="530" 
d="M428 0l-125 -223h237l-84 -149h-195q-97 0 -133 60t12 148l93 164h-112v1164l-65 139h372v-1303z" />
    <glyph glyph-name="Idot" unicode="&#x130;" horiz-adv-x="530" 
d="M127 1676h260v-233h-260v233zM56 1303h372v-1303h-307v1164z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="512" 
d="M410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="uni0136" unicode="&#x136;" horiz-adv-x="1117" 
d="M754 1303h335l-272 -631l272 -672h-335l-216 531h-110v-531h-307v1164l-65 139h372v-502h110zM559 -372h-205l84 270h269z" />
    <glyph glyph-name="uni0137" unicode="&#x137;" horiz-adv-x="1117" 
d="M754 1303h335l-272 -631l272 -672h-335l-216 531h-110v-531h-307v1164l-65 139h372v-502h110zM559 -372h-205l84 270h269z" />
    <glyph glyph-name="Lacute" unicode="&#x139;" horiz-adv-x="875" 
d="M233 1676h270l-149 -270h-205zM410 1303v-1033h437l-149 -270h-596v1303h308z" />
    <glyph glyph-name="lacute" unicode="&#x13a;" horiz-adv-x="875" 
d="M233 1676h270l-149 -270h-205zM410 1303v-1033h437l-149 -270h-596v1303h308z" />
    <glyph glyph-name="uni013B" unicode="&#x13b;" horiz-adv-x="875" 
d="M410 1303v-1033h437l-149 -270h-596v1303h308zM363 -102h270l-149 -270h-205z" />
    <glyph glyph-name="uni013C" unicode="&#x13c;" horiz-adv-x="875" 
d="M410 1303v-1033h437l-149 -270h-596v1303h308zM363 -102h270l-149 -270h-205z" />
    <glyph glyph-name="Lcaron" unicode="&#x13d;" horiz-adv-x="875" 
d="M410 1303v-1033h437l-149 -270h-596v1303h308zM568 1303h270l-149 -270h-205z" />
    <glyph glyph-name="lcaron" unicode="&#x13e;" horiz-adv-x="875" 
d="M410 1303v-1033h437l-149 -270h-596v1303h308zM568 1303h270l-149 -270h-205z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="921" 
d="M456 601v-331h438l-149 -270h-596v354l-102 -84v270l102 84v679h307v-432l177 144v-270z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="921" 
d="M456 601v-331h438l-149 -270h-596v354l-102 -84v270l102 84v679h307v-432l177 144v-270z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1154" 
d="M521 1676h270l-149 -270h-204zM745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1154" 
d="M521 1676h270l-149 -270h-204zM745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354z" />
    <glyph glyph-name="uni0145" unicode="&#x145;" horiz-adv-x="1154" 
d="M745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354zM568 -372h-205l84 270h270z" />
    <glyph glyph-name="uni0146" unicode="&#x146;" horiz-adv-x="1154" 
d="M745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354zM568 -372h-205l84 270h270z" />
    <glyph glyph-name="Ncaron" unicode="&#x147;" horiz-adv-x="1154" 
d="M810 1676l-147 -270h-246l-147 270h195l75 -157l74 157h196zM745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354z" />
    <glyph glyph-name="ncaron" unicode="&#x148;" horiz-adv-x="1154" 
d="M810 1676l-147 -270h-246l-147 270h195l75 -157l74 157h196zM745 629v674h307v-1303h-289l-335 674v-674h-307v1164l-65 139h354z" />
    <glyph glyph-name="Omacron" unicode="&#x14c;" 
d="M279 1480v196h512v-196h-512zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="omacron" unicode="&#x14d;" 
d="M279 1480v196h512v-196h-512zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="Ohungarumlaut" unicode="&#x150;" 
d="M335 1676h270l-149 -270h-205zM661 1676h270l-149 -270h-205zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47
h-177q-47 0 -47 -47z" />
    <glyph glyph-name="ohungarumlaut" unicode="&#x151;" 
d="M335 1676h270l-149 -270h-205zM661 1676h270l-149 -270h-205zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47
h-177q-47 0 -47 -47z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="1517" 
d="M1257 531h-280v-261h522l-149 -270h-1006q-122 0 -186.5 65t-64.5 186v801q0 122 64.5 186.5t186.5 64.5h1155l-149 -270h-373v-232h429zM670 317v670q0 46 -46 46h-177q-47 0 -47 -46v-670q0 -47 47 -47h177q46 0 46 47z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1517" 
d="M1257 531h-280v-261h522l-149 -270h-1006q-122 0 -186.5 65t-64.5 186v801q0 122 64.5 186.5t186.5 64.5h1155l-149 -270h-373v-232h429zM670 317v670q0 46 -46 46h-177q-47 0 -47 -46v-670q0 -47 47 -47h177q46 0 46 47z" />
    <glyph glyph-name="Racute" unicode="&#x154;" horiz-adv-x="1089" 
d="M521 1676h270l-149 -270h-204zM877 426l147 -426h-317l-139 404h-140v-404h-307v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -177 -128 -229zM428 1033v-359h224q46 0 46 47v266q0 46 -46 46h-224z" />
    <glyph glyph-name="racute" unicode="&#x155;" horiz-adv-x="1089" 
d="M521 1676h270l-149 -270h-204zM877 426l147 -426h-317l-139 404h-140v-404h-307v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -177 -128 -229zM428 1033v-359h224q46 0 46 47v266q0 46 -46 46h-224z" />
    <glyph glyph-name="uni0156" unicode="&#x156;" horiz-adv-x="1089" 
d="M877 426l147 -426h-317l-139 404h-140v-404h-307v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -177 -128 -229zM428 1033v-359h224q46 0 46 47v266q0 46 -46 46h-224zM540 -372h-205l84 270h270z" />
    <glyph glyph-name="uni0157" unicode="&#x157;" horiz-adv-x="1089" 
d="M877 426l147 -426h-317l-139 404h-140v-404h-307v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -177 -128 -229zM428 1033v-359h224q46 0 46 47v266q0 46 -46 46h-224zM540 -372h-205l84 270h270z" />
    <glyph glyph-name="Rcaron" unicode="&#x158;" horiz-adv-x="1089" 
d="M801 1676l-148 -270h-245l-147 270h195l75 -157l74 157h196zM877 426l147 -426h-317l-139 404h-140v-404h-307v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -177 -128 -229zM428 1033v-359h224q46 0 46 47v266q0 46 -46 46h-224z" />
    <glyph glyph-name="rcaron" unicode="&#x159;" horiz-adv-x="1089" 
d="M801 1676l-148 -270h-245l-147 270h195l75 -157l74 157h196zM877 426l147 -426h-317l-139 404h-140v-404h-307v1164l-65 139h698q122 0 186.5 -64.5t64.5 -186.5v-397q0 -177 -128 -229zM428 1033v-359h224q46 0 46 47v266q0 46 -46 46h-224z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1051" 
d="M521 1676h270l-149 -270h-204zM84 242v173h307v-108q0 -46 47 -46h186q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119q0 -43 41 -52l324 -73
q221 -51 221 -275v-235q0 -122 -64.5 -186.5t-186.5 -64.5h-391q-121 0 -186 64.5t-65 186.5z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="1051" 
d="M521 1676h270l-149 -270h-204zM84 242v173h307v-108q0 -46 47 -46h186q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119q0 -43 41 -52l324 -73
q221 -51 221 -275v-235q0 -122 -64.5 -186.5t-186.5 -64.5h-391q-121 0 -186 64.5t-65 186.5z" />
    <glyph glyph-name="Scedilla" unicode="&#x15e;" horiz-adv-x="1051" 
d="M432 825l324 -73q221 -51 221 -275v-235q0 -122 -64.5 -186.5t-186.5 -64.5h-167l-30 -52h97q113 0 113 -114v-84q0 -113 -113 -113h-194l-69 121h218q20 0 20 20v28q0 21 -20 21h-209v121l30 52h-67q-121 0 -186 64.5t-65 186.5v173h307v-108q0 -46 47 -46h186
q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119q0 -43 41 -52z" />
    <glyph glyph-name="scedilla" unicode="&#x15f;" horiz-adv-x="1051" 
d="M432 825l324 -73q221 -51 221 -275v-235q0 -122 -64.5 -186.5t-186.5 -64.5h-167l-30 -52h97q113 0 113 -114v-84q0 -113 -113 -113h-194l-69 121h218q20 0 20 20v28q0 21 -20 21h-209v121l30 52h-67q-121 0 -186 64.5t-65 186.5v173h307v-108q0 -46 47 -46h186
q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119q0 -43 41 -52z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1051" 
d="M408 1406l-147 270h195l75 -157l74 157h196l-148 -270h-245zM84 242v173h307v-108q0 -46 47 -46h186q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119
q0 -43 41 -52l324 -73q221 -51 221 -275v-235q0 -122 -64.5 -186.5t-186.5 -64.5h-391q-121 0 -186 64.5t-65 186.5z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="1051" 
d="M408 1406l-147 270h195l75 -157l74 157h196l-148 -270h-245zM84 242v173h307v-108q0 -46 47 -46h186q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119
q0 -43 41 -52l324 -73q221 -51 221 -275v-235q0 -122 -64.5 -186.5t-186.5 -64.5h-391q-121 0 -186 64.5t-65 186.5z" />
    <glyph glyph-name="uni0162" unicode="&#x162;" horiz-adv-x="958" 
d="M633 1033v-1033h-151l-35 -61h97q113 0 113 -114v-84q0 -113 -113 -113h-194l-69 121h218q20 0 20 20v28q0 21 -20 21h-209v121l36 61v1033h-289v270h885v-270h-289z" />
    <glyph glyph-name="uni0163" unicode="&#x163;" horiz-adv-x="958" 
d="M633 1033v-1033h-151l-35 -61h97q113 0 113 -114v-84q0 -113 -113 -113h-194l-69 121h218q20 0 20 20v28q0 21 -20 21h-209v121l36 61v1033h-289v270h885v-270h-289z" />
    <glyph glyph-name="Tcaron" unicode="&#x164;" horiz-adv-x="958" 
d="M352 1406l-147 270h195l75 -157l74 157h196l-147 -270h-246zM326 1033h-289v270h885v-270h-289v-1033h-307v1033z" />
    <glyph glyph-name="tcaron" unicode="&#x165;" horiz-adv-x="958" 
d="M352 1406l-147 270h195l75 -157l74 157h196l-147 -270h-246zM326 1033h-289v270h885v-270h-289v-1033h-307v1033z" />
    <glyph glyph-name="Utilde" unicode="&#x168;" horiz-adv-x="1102" 
d="M456 1493h-5v-87h-174v111q0 57 36.5 89t103.5 32h88l162 -87h5v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="utilde" unicode="&#x169;" horiz-adv-x="1102" 
d="M456 1493h-5v-87h-174v111q0 57 36.5 89t103.5 32h88l162 -87h5v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="Umacron" unicode="&#x16a;" horiz-adv-x="1102" 
d="M298 1676h512v-196h-512v196zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="umacron" unicode="&#x16b;" horiz-adv-x="1102" 
d="M298 1676h512v-196h-512v196zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="Uring" unicode="&#x16e;" horiz-adv-x="1102" 
d="M618 1350h-115q-121 0 -121 123v80q0 123 121 123h115q121 0 121 -123v-80q0 -123 -121 -123zM512 1551v-76q0 -19 19 -19h59q19 0 19 19v76q0 19 -19 19h-59q-19 0 -19 -19zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224
z" />
    <glyph glyph-name="uring" unicode="&#x16f;" horiz-adv-x="1102" 
d="M618 1350h-115q-121 0 -121 123v80q0 123 121 123h115q121 0 121 -123v-80q0 -123 -121 -123zM512 1551v-76q0 -19 19 -19h59q19 0 19 19v76q0 19 -19 19h-59q-19 0 -19 -19zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224
z" />
    <glyph glyph-name="Uhungarumlaut" unicode="&#x170;" horiz-adv-x="1102" 
d="M354 1676h270l-149 -270h-205zM950 1676l-149 -270h-205l84 270h270zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uhungarumlaut" unicode="&#x171;" horiz-adv-x="1102" 
d="M354 1676h270l-149 -270h-205zM950 1676l-149 -270h-205l84 270h270zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="Uogonek" unicode="&#x172;" horiz-adv-x="1102" 
d="M1000 1303v-1303h-112l-125 -223h237l-84 -149h-195q-97 0 -133 60t12 148l93 164h-326q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224v1033h307z" />
    <glyph glyph-name="uogonek" unicode="&#x173;" horiz-adv-x="1102" 
d="M1000 1303v-1303h-112l-125 -223h237l-84 -149h-195q-97 0 -133 60t12 148l93 164h-326q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224v1033h307z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1042" 
d="M195 1443v233h261v-233h-261zM568 1443v233h261v-233h-261zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="986" 
d="M484 1676h270l-149 -270h-205zM65 1303h847v-270l-493 -763h512l-149 -270h-717v270l494 763h-494v270z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="986" 
d="M484 1676h270l-149 -270h-205zM65 1303h847v-270l-493 -763h512l-149 -270h-717v270l494 763h-494v270z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="986" 
d="M382 1443v233h260v-233h-260zM65 1303h847v-270l-493 -763h512l-149 -270h-717v270l494 763h-494v270z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="986" 
d="M382 1443v233h260v-233h-260zM65 1303h847v-270l-493 -763h512l-149 -270h-717v270l494 763h-494v270z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="986" 
d="M389 1406l-147 270h196l74 -157l74 157h196l-147 -270h-246zM65 1303h847v-270l-493 -763h512l-149 -270h-717v270l494 763h-494v270z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="986" 
d="M389 1406l-147 270h196l74 -157l74 157h196l-147 -270h-246zM65 1303h847v-270l-493 -763h512l-149 -270h-717v270l494 763h-494v270z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="930" 
d="M596 987v-212h221l-119 -215h-102v-309q0 -121 -65 -186t-187 -65h-139l-149 270h186q47 0 47 47v243h-103l-119 215h222v277q0 122 64.5 186.5t186.5 64.5h391l-149 -270h-140q-46 0 -46 -46z" />
    <glyph glyph-name="Ohorn" unicode="&#x1a0;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="ohorn" unicode="&#x1a1;" 
d="M726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="Uhorn" unicode="&#x1af;" horiz-adv-x="1102" 
d="M693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uhorn" unicode="&#x1b0;" horiz-adv-x="1102" 
d="M693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni0218" unicode="&#x218;" horiz-adv-x="1051" 
d="M84 242v173h307v-108q0 -46 47 -46h186q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119q0 -43 41 -52l324 -73q221 -51 221 -275v-235q0 -122 -64.5 -186.5
t-186.5 -64.5h-391q-121 0 -186 64.5t-65 186.5zM410 -102h270l-149 -270h-205z" />
    <glyph glyph-name="uni0219" unicode="&#x219;" horiz-adv-x="1051" 
d="M84 242v173h307v-108q0 -46 47 -46h186q46 0 46 46v145q0 44 -41 53l-324 72q-221 51 -221 276v208q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-156h-307v91q0 47 -47 47h-176q-47 0 -47 -47v-119q0 -43 41 -52l324 -73q221 -51 221 -275v-235q0 -122 -64.5 -186.5
t-186.5 -64.5h-391q-121 0 -186 64.5t-65 186.5zM410 -102h270l-149 -270h-205z" />
    <glyph glyph-name="uni021A" unicode="&#x21a;" horiz-adv-x="958" 
d="M326 1033h-289v270h885v-270h-289v-1033h-307v1033zM354 -102h270l-149 -270h-205z" />
    <glyph glyph-name="uni021B" unicode="&#x21b;" horiz-adv-x="958" 
d="M326 1033h-289v270h885v-270h-289v-1033h-307v1033zM354 -102h270l-149 -270h-205z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="633" 
d="M242 1406h-195l147 270h245l147 -270h-195l-74 156z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="633" 
d="M194 1406l-147 270h195l75 -157l74 157h195l-147 -270h-245z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="605" 
d="M47 1480v196h512v-196h-512z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="633" 
d="M47 1564v112h176v-93q0 -28 28 -28h131q28 0 28 28v93h176v-112q0 -158 -158 -158h-223q-158 0 -158 158z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="353" 
d="M47 1443v233h260v-233h-260z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="450" 
d="M47 1473v80q0 123 121 123h115q121 0 121 -123v-80q0 -123 -121 -123h-115q-121 0 -121 123zM177 1551v-76q0 -19 18 -19h60q19 0 19 19v76q0 19 -19 19h-60q-18 0 -18 -19z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="521" 
d="M74 -164l94 164h195l-125 -223h237l-84 -149h-196q-97 0 -133 60t12 148z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="698" 
d="M503 1527v149h149v-131q0 -67 -36.5 -103t-103.5 -36h-106l-205 149h-6v-149h-148v130q0 67 36 103.5t103 36.5h106l205 -149h6z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="772" 
d="M130 1676h270l-149 -270h-204zM456 1676h270l-149 -270h-205z" />
    <glyph glyph-name="uni0326" unicode="&#x326;" horiz-adv-x="0" 
d="M-317 -102h270l-148 -270h-205z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1051" 
d="M923 233h129l-130 -233h-103q-104 0 -159 55.5t-55 158.5v484h-233v-698h-279v810l-56 121h847v-659q0 -39 39 -39z" />
    <glyph glyph-name="uni1E9E" unicode="&#x1e9e;" horiz-adv-x="1074" 
d="M512 0v270h115q47 0 47 47v167q0 47 -47 47h-115v270h115q47 0 47 46v149q0 47 -47 47h-176q-47 0 -47 -47v-996h-307v1061q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-279l-117 -117l117 -118v-296q0 -121 -64.5 -186t-186.5 -65h-218z" />
    <glyph glyph-name="uni1EA0" unicode="&#x1ea0;" horiz-adv-x="1120" 
d="M428 -339v233h270v-233h-270zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="uni1EA1" unicode="&#x1ea1;" horiz-adv-x="1120" 
d="M428 -339v233h270v-233h-270zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="uni1EA2" unicode="&#x1ea2;" horiz-adv-x="1120" 
d="M553 1501h-123v208h237v-208q0 -114 -114 -114v114zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="uni1EA3" unicode="&#x1ea3;" horiz-adv-x="1120" 
d="M553 1501h-123v208h237v-208q0 -114 -114 -114v114zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="uni1EA4" unicode="&#x1ea4;" horiz-adv-x="1120" 
d="M795 1530l84 233h253l-149 -233h-188zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EA5" unicode="&#x1ea5;" horiz-adv-x="1120" 
d="M795 1530l84 233h253l-149 -233h-188zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EA6" unicode="&#x1ea6;" horiz-adv-x="1120" 
d="M920 1763l84 -233h-189l-148 233h253zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EA7" unicode="&#x1ea7;" horiz-adv-x="1120" 
d="M920 1763l84 -233h-189l-148 233h253zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EA8" unicode="&#x1ea8;" horiz-adv-x="1120" 
d="M869 1625h-122v209h236v-209q0 -113 -114 -113v113zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EA9" unicode="&#x1ea9;" horiz-adv-x="1120" 
d="M869 1625h-122v209h236v-209q0 -113 -114 -113v113zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EAA" unicode="&#x1eaa;" horiz-adv-x="1120" 
d="M456 1771h-5v-88h-174v112q0 57 36.5 89t103.5 32h88l162 -88h5v88h173v-112q0 -57 -36.5 -89t-102.5 -32h-88zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z
" />
    <glyph glyph-name="uni1EAB" unicode="&#x1eab;" horiz-adv-x="1120" 
d="M456 1771h-5v-88h-174v112q0 57 36.5 89t103.5 32h88l162 -88h5v88h173v-112q0 -57 -36.5 -89t-102.5 -32h-88zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z
" />
    <glyph glyph-name="uni1EAC" unicode="&#x1eac;" horiz-adv-x="1120" 
d="M428 -339v233h270v-233h-270zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EAD" unicode="&#x1ead;" horiz-adv-x="1120" 
d="M428 -339v233h270v-233h-270zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM486 1387h-196l148 233h245l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EAE" unicode="&#x1eae;" horiz-adv-x="1120" 
d="M447 1663l84 232h257l-149 -232h-192zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EAF" unicode="&#x1eaf;" horiz-adv-x="1120" 
d="M447 1663l84 232h257l-149 -232h-192zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB0" unicode="&#x1eb0;" horiz-adv-x="1120" 
d="M579 1895l84 -232h-192l-149 232h257zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB1" unicode="&#x1eb1;" horiz-adv-x="1120" 
d="M579 1895l84 -232h-192l-149 232h257zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB2" unicode="&#x1eb2;" horiz-adv-x="1120" 
d="M544 1761h-123v209h236v-209q0 -113 -113 -113v113zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB3" unicode="&#x1eb3;" horiz-adv-x="1120" 
d="M544 1761h-123v209h236v-209q0 -113 -113 -113v113zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB4" unicode="&#x1eb4;" horiz-adv-x="1120" 
d="M456 1774h-5v-87h-174v112q0 57 36.5 89t103.5 32h88l162 -88h5v88h173v-112q0 -57 -36.5 -89t-102.5 -32h-88zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65
q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB5" unicode="&#x1eb5;" horiz-adv-x="1120" 
d="M456 1774h-5v-87h-174v112q0 57 36.5 89t103.5 32h88l162 -88h5v88h173v-112q0 -57 -36.5 -89t-102.5 -32h-88zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65
q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB6" unicode="&#x1eb6;" horiz-adv-x="1120" 
d="M428 -339v233h270v-233h-270zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB7" unicode="&#x1eb7;" horiz-adv-x="1120" 
d="M428 -339v233h270v-233h-270zM1084 0h-317l-52 270h-305l-53 -270h-316l283 1164l-65 139h508zM557 1033l-95 -493h201l-95 493h-11zM670 1387h-223q-158 0 -158 136v93h193v-65q0 -24 28 -24h97q28 0 28 24v65h194v-93q0 -136 -159 -136z" />
    <glyph glyph-name="uni1EB8" unicode="&#x1eb8;" horiz-adv-x="968" 
d="M363 -339v233h270v-233h-270zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="uni1EB9" unicode="&#x1eb9;" horiz-adv-x="968" 
d="M363 -339v233h270v-233h-270zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="uni1EBA" unicode="&#x1eba;" horiz-adv-x="968" 
d="M534 1501h-123v208h237v-208q0 -114 -114 -114v114zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="uni1EBB" unicode="&#x1ebb;" horiz-adv-x="968" 
d="M534 1501h-123v208h237v-208q0 -114 -114 -114v114zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="uni1EBC" unicode="&#x1ebc;" horiz-adv-x="968" 
d="M410 1493h-6v-87h-173v111q0 57 36.5 89t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-103.5 -32h-87zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="uni1EBD" unicode="&#x1ebd;" horiz-adv-x="968" 
d="M410 1493h-6v-87h-173v111q0 57 36.5 89t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-103.5 -32h-87zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428z" />
    <glyph glyph-name="uni1EBE" unicode="&#x1ebe;" horiz-adv-x="968" 
d="M730 1530l84 233h253l-149 -233h-188zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EBF" unicode="&#x1ebf;" horiz-adv-x="968" 
d="M730 1530l84 233h253l-149 -233h-188zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC0" unicode="&#x1ec0;" horiz-adv-x="968" 
d="M855 1763l83 -233h-188l-149 233h254zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC1" unicode="&#x1ec1;" horiz-adv-x="968" 
d="M855 1763l83 -233h-188l-149 233h254zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC2" unicode="&#x1ec2;" horiz-adv-x="968" 
d="M804 1625h-123v209h237v-209q0 -113 -114 -113v113zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC3" unicode="&#x1ec3;" horiz-adv-x="968" 
d="M804 1625h-123v209h237v-209q0 -113 -114 -113v113zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC4" unicode="&#x1ec4;" horiz-adv-x="968" 
d="M391 1771h-6v-88h-173v112q0 57 36.5 89t103.5 32h87l162 -88h6v88h173v-112q0 -57 -36.5 -89t-103.5 -32h-87zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC5" unicode="&#x1ec5;" horiz-adv-x="968" 
d="M391 1771h-6v-88h-173v112q0 57 36.5 89t103.5 32h87l162 -88h6v88h173v-112q0 -57 -36.5 -89t-103.5 -32h-87zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC6" unicode="&#x1ec6;" horiz-adv-x="968" 
d="M363 -339v233h270v-233h-270zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC7" unicode="&#x1ec7;" horiz-adv-x="968" 
d="M363 -339v233h270v-233h-270zM707 531h-279v-261h522l-149 -270h-680v1164l-65 139h894l-149 -270h-373v-232h428zM421 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1EC8" unicode="&#x1ec8;" horiz-adv-x="530" 
d="M274 1501h-123v208h236v-208q0 -114 -113 -114v114zM56 1303h372v-1303h-307v1164z" />
    <glyph glyph-name="uni1EC9" unicode="&#x1ec9;" horiz-adv-x="512" 
d="M274 1501h-123v208h236v-208q0 -114 -113 -114v114zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="uni1ECA" unicode="&#x1eca;" horiz-adv-x="530" 
d="M140 -339v233h270v-233h-270zM56 1303h372v-1303h-307v1164z" />
    <glyph glyph-name="uni1ECB" unicode="&#x1ecb;" horiz-adv-x="530" 
d="M140 -339v233h270v-233h-270zM56 1303h372v-1303h-307v1164z" />
    <glyph glyph-name="uni1ECC" unicode="&#x1ecc;" 
d="M400 -339v233h270v-233h-270zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1ECD" unicode="&#x1ecd;" 
d="M400 -339v233h270v-233h-270zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1ECE" unicode="&#x1ece;" 
d="M534 1501h-123v208h237v-208q0 -114 -114 -114v114zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177
q-47 0 -47 -47z" />
    <glyph glyph-name="uni1ECF" unicode="&#x1ecf;" 
d="M534 1501h-123v208h237v-208q0 -114 -114 -114v114zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177
q-47 0 -47 -47z" />
    <glyph glyph-name="uni1ED0" unicode="&#x1ed0;" 
d="M769 1530l84 233h253l-149 -233h-188zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z
M460 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1ED1" unicode="&#x1ed1;" 
d="M769 1530l84 233h253l-149 -233h-188zM460 1387h-196l147 233h246l147 -233h-195l-75 119zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177
q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1ED2" unicode="&#x1ed2;" 
d="M894 1763l83 -233h-188l-149 233h254zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z
M460 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1ED3" unicode="&#x1ed3;" 
d="M894 1763l83 -233h-188l-149 233h254zM460 1387h-196l147 233h246l147 -233h-195l-75 119zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177
q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1ED4" unicode="&#x1ed4;" 
d="M843 1625h-122v209h236v-209q0 -113 -114 -113v113zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177
q-47 0 -47 -47zM460 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1ED5" unicode="&#x1ed5;" 
d="M843 1625h-122v209h236v-209q0 -113 -114 -113v113zM460 1387h-196l147 233h246l147 -233h-195l-75 119zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689
q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1ED6" unicode="&#x1ed6;" 
d="M430 1771h-6v-88h-173v112q0 57 36.5 89t103.5 32h87l162 -88h6v88h173v-112q0 -57 -36.5 -89t-102.5 -32h-88zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689
q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM460 1387h-196l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1ED7" unicode="&#x1ed7;" 
d="M430 1771h-6v-88h-173v112q0 57 36.5 89t103.5 32h87l162 -88h6v88h173v-112q0 -57 -36.5 -89t-102.5 -32h-88zM460 1387h-196l147 233h246l147 -233h-195l-75 119zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819
q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1ED8" unicode="&#x1ed8;" 
d="M400 -339v233h270v-233h-270zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47zM460 1387h-196
l147 233h246l147 -233h-195l-75 119z" />
    <glyph glyph-name="uni1ED9" unicode="&#x1ed9;" 
d="M400 -339v233h270v-233h-270zM460 1387h-196l147 233h246l147 -233h-195l-75 119zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q121 0 186 -65t65 -187v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46
v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EDA" unicode="&#x1eda;" 
d="M423 1387l83 233h257l-149 -233h-191zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689
q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EDB" unicode="&#x1edb;" 
d="M423 1387l83 233h257l-149 -233h-191zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689
q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EDC" unicode="&#x1edc;" 
d="M573 1620l84 -233h-192l-148 233h256zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689
q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EDD" unicode="&#x1edd;" 
d="M573 1620l84 -233h-192l-148 233h256zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689
q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EDE" unicode="&#x1ede;" 
d="M534 1501h-123v208h237v-208q0 -114 -114 -114v114zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46
v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EDF" unicode="&#x1edf;" 
d="M534 1501h-123v208h237v-208q0 -114 -114 -114v114zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46
v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EE0" unicode="&#x1ee0;" 
d="M417 1493h-6v-87h-173v111q0 57 36.5 89t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5
t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EE1" unicode="&#x1ee1;" 
d="M417 1493h-6v-87h-173v111q0 57 36.5 89t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5
t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EE2" unicode="&#x1ee2;" 
d="M400 -339v233h270v-233h-270zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177
q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EE3" unicode="&#x1ee3;" 
d="M400 -339v233h270v-233h-270zM726 -9h-382q-122 0 -186.5 64.5t-64.5 186.5v819q0 122 65 187t186 65h382q76 0 127 -25l22 127h251l-111 -270h-47q9 -39 9 -84v-819q0 -122 -64.5 -186.5t-186.5 -64.5zM400 996v-689q0 -46 47 -46h177q46 0 46 46v689q0 47 -46 47h-177
q-47 0 -47 -47z" />
    <glyph glyph-name="uni1EE4" unicode="&#x1ee4;" horiz-adv-x="1102" 
d="M428 -339v233h270v-233h-270zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EE5" unicode="&#x1ee5;" horiz-adv-x="1102" 
d="M428 -339v233h270v-233h-270zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EE6" unicode="&#x1ee6;" horiz-adv-x="1102" 
d="M560 1501h-122v208h236v-208q0 -114 -114 -114v114zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EE7" unicode="&#x1ee7;" horiz-adv-x="1102" 
d="M560 1501h-122v208h236v-208q0 -114 -114 -114v114zM693 270v1033h307v-1303h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EE8" unicode="&#x1ee8;" horiz-adv-x="1102" 
d="M452 1387l84 233h257l-149 -233h-192zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EE9" unicode="&#x1ee9;" horiz-adv-x="1102" 
d="M452 1387l84 233h257l-149 -233h-192zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EEA" unicode="&#x1eea;" horiz-adv-x="1102" 
d="M579 1620l84 -233h-192l-149 233h257zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EEB" unicode="&#x1eeb;" horiz-adv-x="1102" 
d="M579 1620l84 -233h-192l-149 233h257zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EEC" unicode="&#x1eec;" horiz-adv-x="1102" 
d="M560 1501h-122v208h236v-208q0 -114 -114 -114v114zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EED" unicode="&#x1eed;" horiz-adv-x="1102" 
d="M560 1501h-122v208h236v-208q0 -114 -114 -114v114zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EEE" unicode="&#x1eee;" horiz-adv-x="1102" 
d="M456 1493h-5v-87h-174v111q0 57 36.5 89t103.5 32h88l162 -87h5v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EEF" unicode="&#x1eef;" horiz-adv-x="1102" 
d="M456 1493h-5v-87h-174v111q0 57 36.5 89t103.5 32h88l162 -87h5v87h173v-111q0 -57 -36.5 -89t-102.5 -32h-88zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EF0" unicode="&#x1ef0;" horiz-adv-x="1102" 
d="M428 -339v233h270v-233h-270zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="uni1EF1" unicode="&#x1ef1;" horiz-adv-x="1102" 
d="M428 -339v233h270v-233h-270zM693 270v1033h203l20 112h251l-111 -270h-56v-1145h-633q-122 0 -187 65t-65 186v913l-65 139h373v-986q0 -47 46 -47h224z" />
    <glyph glyph-name="Ygrave" unicode="&#x1ef2;" horiz-adv-x="1042" 
d="M559 1620l83 -233h-191l-149 233h257zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="ygrave" unicode="&#x1ef3;" horiz-adv-x="1042" 
d="M559 1620l83 -233h-191l-149 233h257zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="uni1EF4" unicode="&#x1ef4;" horiz-adv-x="1042" 
d="M389 -339v233h270v-233h-270zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="uni1EF5" unicode="&#x1ef5;" horiz-adv-x="1042" 
d="M389 -339v233h270v-233h-270zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="uni1EF6" unicode="&#x1ef6;" horiz-adv-x="1042" 
d="M527 1501h-123v208h236v-208q0 -114 -113 -114v114zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="uni1EF7" unicode="&#x1ef7;" horiz-adv-x="1042" 
d="M527 1501h-123v208h236v-208q0 -114 -113 -114v114zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="uni1EF8" unicode="&#x1ef8;" horiz-adv-x="1042" 
d="M421 1493h-6v-87h-173v111q0 57 36.5 89t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-103.5 -32h-87zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="uni1EF9" unicode="&#x1ef9;" horiz-adv-x="1042" 
d="M421 1493h-6v-87h-173v111q0 57 36.5 89t103.5 32h87l162 -87h6v87h173v-111q0 -57 -36.5 -89t-103.5 -32h-87zM369 441l-369 862h326l197 -525l194 525h326l-367 -862v-441h-307v441z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="875" 
d="M65 555v233h745v-233h-745z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1061" 
d="M65 555v233h931v-233h-931z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="400" 
d="M195 1154h159v-270h-307v270q0 149 148 149v-149z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="400" 
d="M205 1033h-158v270h307v-270q0 -149 -149 -149v149z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="400" 
d="M205 0h-158v270h307v-270q0 -149 -149 -149v149z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="781" 
d="M195 1154h159v-270h-307v270q0 149 148 149v-149zM577 1154h158v-270h-307v270q0 149 149 149v-149z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="781" 
d="M205 1033h-158v270h307v-270q0 -149 -149 -149v149zM586 1033h-158v270h307v-270q0 -149 -149 -149v149z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="791" 
d="M205 0h-158v270h307v-270q0 -149 -149 -149v149zM596 0h-158v270h307v-270q0 -149 -149 -149v149z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="700" 
d="M462 1182h191l-107 -195h-84v-428h-224v428h-191l108 195h83v121h224v-121z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="700" 
d="M546 987h-84v-112h191l-107 -195h-84v-121h-224v121h-191l108 195h83v112h-191l108 195h83v121h224v-121h191z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="642" 
d="M93 648v35q0 88 47.5 135.5t135.5 47.5h91q88 0 135 -47.5t47 -135.5v-35q0 -88 -47 -135.5t-135 -47.5h-91q-88 0 -135.5 47.5t-47.5 135.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1182" 
d="M47 0v270h307v-270h-307zM438 0v270h307v-270h-307zM829 0v270h307v-270h-307z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2182" 
d="M443 559h-225q-144 0 -144 143v458q0 143 144 143h225q143 0 143 -143v-458q0 -143 -143 -143zM365 0l624 1303h204l-623 -1303h-205zM251 1119v-376q0 -26 26 -26h107q26 0 26 26v376q0 26 -26 26h-107q-26 0 -26 -26zM1341 0h-226q-143 0 -143 143v458q0 144 143 144
h226q143 0 143 -144v-458q0 -143 -143 -143zM1964 0h-225q-143 0 -143 143v458q0 144 143 144h225q144 0 144 -144v-458q0 -143 -144 -143zM1149 560v-376q0 -26 26 -26h106q26 0 26 26v376q0 26 -26 26h-106q-26 0 -26 -26zM1772 560v-376q0 -26 27 -26h106q26 0 26 26v376
q0 26 -26 26h-106q-27 0 -27 -26z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="605" 
d="M317 672l242 -326h-270l-242 326l242 326h270z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="605" 
d="M317 346h-270l242 326l-242 326h270l242 -326z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="232" 
d="M-298 0l624 1303h205l-624 -1303h-205z" />
    <glyph glyph-name="uni2070" unicode="&#x2070;" horiz-adv-x="660" 
d="M443 559h-225q-144 0 -144 143v458q0 143 144 143h225q143 0 143 -143v-458q0 -143 -143 -143zM251 1119v-376q0 -26 26 -26h107q26 0 26 26v376q0 26 -26 26h-107q-26 0 -26 -26z" />
    <glyph glyph-name="uni2074" unicode="&#x2074;" horiz-adv-x="614" 
d="M354 711h-317v158l266 434h228v-434h65l-65 -122v-188h-177v152zM354 1113l-149 -244h149v244z" />
    <glyph glyph-name="uni2075" unicode="&#x2075;" horiz-adv-x="651" 
d="M74 702v100h177v-57q0 -28 28 -28h105q26 0 26 28v119q0 26 -26 26h-310v413h522l-86 -158h-259v-97h192q68 0 105.5 -37.5t37.5 -105.5v-203q0 -143 -143 -143h-225q-144 0 -144 143z" />
    <glyph glyph-name="uni2076" unicode="&#x2076;" horiz-adv-x="651" 
d="M74 702v458q0 143 144 143h378l-86 -158h-231q-28 0 -28 -26v-71h192q68 0 105.5 -37.5t37.5 -105.5v-203q0 -143 -143 -143h-225q-144 0 -144 143zM251 890v-147q0 -26 28 -26h105q26 0 26 26v121q0 26 -26 26h-133z" />
    <glyph glyph-name="uni2077" unicode="&#x2077;" horiz-adv-x="558" 
d="M324 1145h-287v158h484v-158l-160 -328v-258h-177v279q0 31 17 65z" />
    <glyph glyph-name="uni2078" unicode="&#x2078;" horiz-adv-x="660" 
d="M74 702v169l67 67l-67 67v155q0 143 144 143h225q143 0 143 -143v-155l-67 -67l67 -67v-169q0 -143 -143 -143h-225q-144 0 -144 143zM251 1119v-75q0 -26 26 -26h105q28 0 28 26v75q0 26 -28 26h-105q-26 0 -26 -26zM251 834v-91q0 -26 26 -26h105q28 0 28 26v91
q0 26 -28 26h-105q-26 0 -26 -26z" />
    <glyph glyph-name="uni2079" unicode="&#x2079;" horiz-adv-x="651" 
d="M577 1160v-458q0 -143 -143 -143h-285l-84 158h307q28 0 28 26v71h-191q-68 0 -106 37.5t-38 105.5v203q0 143 144 143h225q143 0 143 -143zM400 972v147q0 26 -28 26h-104q-26 0 -26 -26v-121q0 -26 26 -26h132z" />
    <glyph glyph-name="uni2080" unicode="&#x2080;" horiz-adv-x="660" 
d="M443 0h-225q-144 0 -144 143v458q0 144 144 144h225q143 0 143 -144v-458q0 -143 -143 -143zM251 560v-376q0 -26 26 -26h107q26 0 26 26v376q0 26 -26 26h-107q-26 0 -26 -26z" />
    <glyph glyph-name="uni2081" unicode="&#x2081;" horiz-adv-x="372" 
d="M37 586v159h261v-745h-177v586h-84z" />
    <glyph glyph-name="uni2082" unicode="&#x2082;" horiz-adv-x="660" 
d="M510 0h-436v235q0 127 127 156l184 43q25 6 25 30v96q0 26 -26 26h-103q-26 0 -26 -26v-59h-177v100q0 144 144 144h221q143 0 143 -144v-152q0 -129 -126 -157l-184 -43q-25 -6 -25 -29v-62h345z" />
    <glyph glyph-name="uni2083" unicode="&#x2083;" horiz-adv-x="651" 
d="M65 143v101h177v-60q0 -26 26 -26h106q26 0 26 26v92q0 26 -26 26h-132v158h132q26 0 26 26v74q0 26 -26 26h-106q-26 0 -26 -26v-50h-177v91q0 144 144 144h225q143 0 143 -144v-154l-67 -67l67 -67v-170q0 -143 -143 -143h-225q-144 0 -144 143z" />
    <glyph glyph-name="uni2084" unicode="&#x2084;" horiz-adv-x="614" 
d="M354 153h-317v158l266 434h228v-434h65l-65 -123v-188h-177v153zM354 555l-149 -244h149v244z" />
    <glyph glyph-name="uni2085" unicode="&#x2085;" horiz-adv-x="651" 
d="M74 143v101h177v-58q0 -28 28 -28h105q26 0 26 28v119q0 26 -26 26h-310v414h522l-86 -159h-259v-96h192q68 0 105.5 -38t37.5 -106v-203q0 -143 -143 -143h-225q-144 0 -144 143z" />
    <glyph glyph-name="uni2086" unicode="&#x2086;" horiz-adv-x="651" 
d="M74 143v458q0 144 144 144h378l-86 -159h-231q-28 0 -28 -26v-70h192q68 0 105.5 -38t37.5 -106v-203q0 -143 -143 -143h-225q-144 0 -144 143zM251 331v-147q0 -26 28 -26h105q26 0 26 26v121q0 26 -26 26h-133z" />
    <glyph glyph-name="uni2087" unicode="&#x2087;" horiz-adv-x="558" 
d="M324 586h-287v159h484v-159l-160 -327v-259h-177v279q0 31 17 65z" />
    <glyph glyph-name="uni2088" unicode="&#x2088;" horiz-adv-x="660" 
d="M74 143v170l67 67l-67 67v154q0 144 144 144h225q143 0 143 -144v-154l-67 -67l67 -67v-170q0 -143 -143 -143h-225q-144 0 -144 143zM251 560v-74q0 -26 26 -26h105q28 0 28 26v74q0 26 -28 26h-105q-26 0 -26 -26zM251 276v-92q0 -26 26 -26h105q28 0 28 26v92
q0 26 -28 26h-105q-26 0 -26 -26z" />
    <glyph glyph-name="uni2089" unicode="&#x2089;" horiz-adv-x="651" 
d="M577 601v-458q0 -143 -143 -143h-285l-84 158h307q28 0 28 26v71h-191q-68 0 -106 37.5t-38 105.5v203q0 144 144 144h225q143 0 143 -144zM400 413v147q0 26 -28 26h-104q-26 0 -26 -26v-121q0 -26 26 -26h132z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="1096" 
d="M1013 1061v-156h-307v91q0 47 -47 47h-177q-46 0 -46 -47v-221h389l-119 -215h-270v-253q0 -46 46 -46h177q47 0 47 46v108h307v-173q0 -121 -65 -186t-187 -65h-381q-122 0 -187 65t-65 186v318l-119 215h119v286q0 121 65 186.5t187 65.5h381q121 0 186.5 -65.5
t65.5 -186.5z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1210" 
d="M182 1169h-145v134h443v-134h-145v-517h-153v517zM704 652h-155v651h158l140 -259l140 259h158v-651h-153v381l-145 -258l-143 258v-381z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="642" 
d="M84 555v233h512v-233h-512z" />
    <glyph glyph-name="uni2215" unicode="&#x2215;" horiz-adv-x="828" 
d="M0 0l624 1303h205l-624 -1303h-205z" />
    <glyph glyph-name="uni2219" unicode="&#x2219;" horiz-adv-x="642" 
d="M93 648v35q0 88 47.5 135.5t135.5 47.5h91q88 0 135 -47.5t47 -135.5v-35q0 -88 -47 -135.5t-135 -47.5h-91q-88 0 -135.5 47.5t-47.5 135.5z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1470" 
d="M84 411v503q0 103 55 158.5t159 55.5h337l100 -100l101 100h337q104 0 159 -55.5t55 -158.5v-503q0 -103 -55 -158.5t-159 -55.5h-337l-101 101l-100 -101h-337q-104 0 -159 55.5t-55 158.5zM363 856v-387q0 -39 39 -39h255l-97 432q-9 34 -42 34h-116q-39 0 -39 -40z
M1069 896h-255l96 -432q9 -34 43 -34h116q39 0 39 39v387q0 40 -39 40z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="735" 
d="M521 853v149h149v-131q0 -67 -36 -103t-103 -36h-107l-204 149h-6v-149h-149v130q0 67 36.5 103.5t103.5 36.5h106l205 -149h5zM521 462v149h149v-131q0 -67 -36 -103t-103 -36h-107l-204 149h-6v-149h-149v130q0 67 36.5 103.5t103.5 36.5h106l205 -149h5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="642" 
d="M352 601h225v-217l-309 1l-35 -91h-121l35 93h-82v214h166l52 140h-218v214h302l35 93h121l-35 -93h82v-214h-166z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="744" 
d="M93 866l559 251v-242l-326 -130l326 -131v-419h-559v196h518l-518 233v242z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="744" 
d="M93 1117l559 -251v-242l-518 -233h518v-196h-559v419l326 131l-326 130v242z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="CR" horiz-adv-x="681" 
 />
    <glyph glyph-name="A.ss01" horiz-adv-x="1117" 
d="M1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Aacute.ss01" horiz-adv-x="1117" 
d="M540 1676h270l-149 -270h-205zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Abreve.ss01" horiz-adv-x="1117" 
d="M670 1406h-223q-158 0 -158 158v112h176v-93q0 -28 28 -28h131q28 0 28 28v93h177v-112q0 -158 -159 -158zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Acircumflex.ss01" horiz-adv-x="1117" 
d="M633 1406l-74 156l-75 -156h-195l147 270h245l148 -270h-196zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Adieresis.ss01" horiz-adv-x="1117" 
d="M242 1676h261v-233h-261v233zM614 1676h261v-233h-261v233zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Agrave.ss01" horiz-adv-x="1117" 
d="M586 1676l84 -270h-205l-148 270h269zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Amacron.ss01" horiz-adv-x="1117" 
d="M307 1676h512v-196h-512v196zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Aogonek.ss01" horiz-adv-x="1117" 
d="M1080 0h-121l-125 -223h237l-84 -149h-196q-97 0 -133 60t12 148l93 164l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="Aring.ss01" horiz-adv-x="1117" 
d="M618 1350h-115q-121 0 -121 123v80q0 123 121 123h115q121 0 121 -123v-80q0 -123 -121 -123zM512 1551v-76q0 -19 19 -19h59q19 0 19 19v76q0 19 -19 19h-59q-19 0 -19 -19zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z
" />
    <glyph glyph-name="Atilde.ss01" horiz-adv-x="1117" 
d="M415 1555h-5v-149h-149v130q0 67 36 103.5t103 36.5h106l205 -149h6v149h149v-131q0 -67 -36.5 -103t-103.5 -36h-106zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="AE.ss01" horiz-adv-x="1588" 
d="M1327 531h-279v-261h522l-149 -270h-680v270h-333l-110 -270h-298l540 1303h1030l-149 -270h-373v-232h428zM721 1033l-202 -493h222v493h-20z" />
    <glyph glyph-name="B.ss01" horiz-adv-x="1079" 
d="M735 0h-633v1303h633q122 0 187 -65t65 -186v-270l-118 -117l118 -118v-296q0 -121 -65 -186t-187 -65zM410 1033v-232h223q47 0 47 46v140q0 46 -47 46h-223zM410 531v-261h223q47 0 47 47v167q0 47 -47 47h-223z" />
    <glyph glyph-name="D.ss01" horiz-adv-x="1079" 
d="M735 0h-633v1303h633q122 0 187 -65t65 -186v-801q0 -121 -65 -186t-187 -65zM410 1033v-763h223q47 0 47 47v670q0 46 -47 46h-223z" />
    <glyph glyph-name="Eth.ss01" horiz-adv-x="1126" 
d="M149 1303h633q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v560h-102v215h102v528zM726 317v670q0 46 -46 46h-224v-258h121v-215h-121v-290h224q46 0 46 47z" />
    <glyph glyph-name="Dcaron.ss01" horiz-adv-x="1079" 
d="M417 1406l-147 270h195l75 -157l74 157h196l-147 -270h-246zM735 0h-633v1303h633q122 0 187 -65t65 -186v-801q0 -121 -65 -186t-187 -65zM410 1033v-763h223q47 0 47 47v670q0 46 -47 46h-223z" />
    <glyph glyph-name="Dcroat.ss01" horiz-adv-x="1126" 
d="M149 1303h633q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v560h-102v215h102v528zM726 317v670q0 46 -46 46h-224v-258h121v-215h-121v-290h224q46 0 46 47z" />
    <glyph glyph-name="E.ss01" horiz-adv-x="949" 
d="M689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="Eacute.ss01" horiz-adv-x="949" 
d="M478 1676h270l-148 -270h-205zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="Ecaron.ss01" horiz-adv-x="949" 
d="M380 1406l-147 270h195l75 -157l74 157h196l-147 -270h-246zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="Ecircumflex.ss01" horiz-adv-x="949" 
d="M419 1406h-196l148 270h245l147 -270h-195l-75 156zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="Edieresis.ss01" horiz-adv-x="949" 
d="M186 1443v233h261v-233h-261zM559 1443v233h260v-233h-260zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="Edotaccent.ss01" horiz-adv-x="949" 
d="M372 1443v233h261v-233h-261zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="Egrave.ss01" horiz-adv-x="949" 
d="M419 1406l-149 270h270l84 -270h-205zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="Emacron.ss01" horiz-adv-x="949" 
d="M242 1480v196h512v-196h-512zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="Eogonek.ss01" horiz-adv-x="949" 
d="M410 270h521l-149 -270l-125 -223h237l-84 -149h-196q-97 0 -133 60t12 148l93 164h-484v1303h829l-149 -270h-372v-232h428l-149 -270h-279v-261z" />
    <glyph glyph-name="F.ss01" horiz-adv-x="949" 
d="M689 531h-279v-531h-308v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="H.ss01" horiz-adv-x="1089" 
d="M680 808v495h307v-1303h-307v538h-270v-538h-308v1303h308v-495h270z" />
    <glyph glyph-name="I.ss01" horiz-adv-x="512" 
d="M410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="Iacute.ss01" horiz-adv-x="512" 
d="M242 1676h270l-149 -270h-205zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="Icircumflex.ss01" horiz-adv-x="512" 
d="M181 1406h-196l147 270h246l147 -270h-195l-75 156zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="Idieresis.ss01" horiz-adv-x="512" 
d="M-61 1443v233h260v-233h-260zM311 1443v233h261v-233h-261zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="Idotaccent.ss01" horiz-adv-x="512" 
d="M123 1443v233h261v-233h-261zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="Igrave.ss01" horiz-adv-x="512" 
d="M149 1406l-149 270h270l84 -270h-205zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="Imacron.ss01" horiz-adv-x="512" 
d="M0 1480v196h512v-196h-512zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="Iogonek.ss01" horiz-adv-x="512" 
d="M410 0l-125 -223h236l-83 -149h-196q-97 0 -133 60t12 148l93 164h-112v1303h308v-1303z" />
    <glyph glyph-name="J.ss01" horiz-adv-x="701" 
d="M298 317v986h307v-1052q0 -121 -64.5 -186t-186.5 -65h-177l-149 270h223q47 0 47 47z" />
    <glyph glyph-name="K.ss01" horiz-adv-x="1098" 
d="M410 0h-308v1303h308v-502h109l216 502h336l-272 -631l272 -672h-336l-216 531h-109v-531z" />
    <glyph glyph-name="uni0136.ss01" horiz-adv-x="1098" 
d="M410 0h-308v1303h308v-502h109l216 502h336l-272 -631l272 -672h-336l-216 531h-109v-531zM438 -102h269l-148 -270h-205z" />
    <glyph glyph-name="M.ss01" horiz-adv-x="1396" 
d="M410 0h-308v1303h317l279 -517l279 517h317v-1303h-307v763l-289 -517l-288 517v-763z" />
    <glyph glyph-name="N.ss01" horiz-adv-x="1135" 
d="M726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289z" />
    <glyph glyph-name="Nacute.ss01" horiz-adv-x="1135" 
d="M521 1676h270l-149 -270h-204zM726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289z" />
    <glyph glyph-name="Ncaron.ss01" horiz-adv-x="1135" 
d="M417 1406l-147 270h195l75 -157l74 157h196l-147 -270h-246zM726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289z" />
    <glyph glyph-name="uni0145.ss01" horiz-adv-x="1135" 
d="M726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289zM447 -102h270l-149 -270h-205z" />
    <glyph glyph-name="Ntilde.ss01" horiz-adv-x="1135" 
d="M726 1527v149h149v-131q0 -67 -36.5 -103t-103.5 -36h-106l-205 149h-5v-149h-149v130q0 67 36.5 103.5t103.5 36.5h106l205 -149h5zM726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289z" />
    <glyph glyph-name="P.ss01" horiz-adv-x="1051" 
d="M410 0h-308v1303h633q122 0 187 -65t65 -186v-397q0 -121 -65 -186t-187 -65h-325v-404zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="R.ss01" 
d="M858 426l147 -426h-316l-140 404h-139v-404h-308v1303h633q122 0 187 -65t65 -186v-397q0 -177 -129 -229zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="Racute.ss01" 
d="M521 1676h270l-149 -270h-204zM858 426l147 -426h-316l-140 404h-139v-404h-308v1303h633q122 0 187 -65t65 -186v-397q0 -177 -129 -229zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="Rcaron.ss01" 
d="M801 1676l-148 -270h-245l-147 270h195l75 -157l74 157h196zM858 426l147 -426h-316l-140 404h-139v-404h-308v1303h633q122 0 187 -65t65 -186v-397q0 -177 -129 -229zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="uni0156.ss01" 
d="M858 426l147 -426h-316l-140 404h-139v-404h-308v1303h633q122 0 187 -65t65 -186v-397q0 -177 -129 -229zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223zM540 -372h-205l84 270h270z" />
    <glyph glyph-name="U.ss01" horiz-adv-x="1083" 
d="M981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="Uacute.ss01" horiz-adv-x="1083" 
d="M512 1676h270l-149 -270h-205zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="Ucircumflex.ss01" horiz-adv-x="1083" 
d="M456 1406h-195l147 270h245l148 -270h-196l-74 156zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="Udieresis.ss01" horiz-adv-x="1083" 
d="M223 1443v233h261v-233h-261zM596 1443v233h260v-233h-260zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="Ugrave.ss01" horiz-adv-x="1083" 
d="M438 1406l-149 270h270l83 -270h-204zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="Uhungarumlaut.ss01" horiz-adv-x="1083" 
d="M335 1676h270l-149 -270h-205zM661 1676h270l-149 -270h-205zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="Umacron.ss01" horiz-adv-x="1083" 
d="M279 1480v196h512v-196h-512zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="Uogonek.ss01" horiz-adv-x="1083" 
d="M981 1303v-1303h-112l-124 -223h236l-84 -149h-195q-97 0 -133 60t12 148l93 164h-326q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307z" />
    <glyph glyph-name="Uring.ss01" horiz-adv-x="1083" 
d="M363 1473v80q0 123 121 123h116q121 0 121 -123v-80q0 -123 -121 -123h-116q-121 0 -121 123zM493 1551v-76q0 -19 19 -19h60q18 0 18 19v76q0 19 -18 19h-60q-19 0 -19 -19zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="i.loclTRK" horiz-adv-x="530" 
d="M127 1676h260v-233h-260v233zM56 1303h372v-1303h-307v1164z" />
    <glyph glyph-name="a.ss01" horiz-adv-x="1117" 
d="M1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="aacute.ss01" horiz-adv-x="1117" 
d="M540 1676h270l-149 -270h-205zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="abreve.ss01" horiz-adv-x="1117" 
d="M670 1406h-223q-158 0 -158 158v112h176v-93q0 -28 28 -28h131q28 0 28 28v93h177v-112q0 -158 -159 -158zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="acircumflex.ss01" horiz-adv-x="1117" 
d="M633 1406l-74 156l-75 -156h-195l147 270h245l148 -270h-196zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="adieresis.ss01" horiz-adv-x="1117" 
d="M242 1676h261v-233h-261v233zM614 1676h261v-233h-261v233zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="agrave.ss01" horiz-adv-x="1117" 
d="M586 1676l84 -270h-205l-148 270h269zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="amacron.ss01" horiz-adv-x="1117" 
d="M307 1676h512v-196h-512v196zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="aogonek.ss01" horiz-adv-x="1117" 
d="M1080 0h-121l-125 -223h237l-84 -149h-196q-97 0 -133 60t12 148l93 164l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="aring.ss01" horiz-adv-x="1117" 
d="M618 1350h-115q-121 0 -121 123v80q0 123 121 123h115q121 0 121 -123v-80q0 -123 -121 -123zM512 1551v-76q0 -19 19 -19h59q19 0 19 19v76q0 19 -19 19h-59q-19 0 -19 -19zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z
" />
    <glyph glyph-name="atilde.ss01" horiz-adv-x="1117" 
d="M415 1555h-5v-149h-149v130q0 67 36 103.5t103 36.5h106l205 -149h6v149h149v-131q0 -67 -36.5 -103t-103.5 -36h-106zM1080 0h-317l-52 270h-305l-52 -270h-317l317 1303h409zM553 1033l-95 -493h201l-95 493h-11z" />
    <glyph glyph-name="ae.ss01" horiz-adv-x="1588" 
d="M1327 531h-279v-261h522l-149 -270h-680v270h-333l-110 -270h-298l540 1303h1030l-149 -270h-373v-232h428zM721 1033l-202 -493h222v493h-20z" />
    <glyph glyph-name="b.ss01" horiz-adv-x="1079" 
d="M735 0h-633v1303h633q122 0 187 -65t65 -186v-270l-118 -117l118 -118v-296q0 -121 -65 -186t-187 -65zM410 1033v-232h223q47 0 47 46v140q0 46 -47 46h-223zM410 531v-261h223q47 0 47 47v167q0 47 -47 47h-223z" />
    <glyph glyph-name="d.ss01" horiz-adv-x="1079" 
d="M735 0h-633v1303h633q122 0 187 -65t65 -186v-801q0 -121 -65 -186t-187 -65zM410 1033v-763h223q47 0 47 47v670q0 46 -47 46h-223z" />
    <glyph glyph-name="eth.ss01" horiz-adv-x="1126" 
d="M149 1303h633q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v560h-102v215h102v528zM726 317v670q0 46 -46 46h-224v-258h121v-215h-121v-290h224q46 0 46 47z" />
    <glyph glyph-name="dcaron.ss01" horiz-adv-x="1079" 
d="M417 1406l-147 270h195l75 -157l74 157h196l-147 -270h-246zM735 0h-633v1303h633q122 0 187 -65t65 -186v-801q0 -121 -65 -186t-187 -65zM410 1033v-763h223q47 0 47 47v670q0 46 -47 46h-223z" />
    <glyph glyph-name="dcroat.ss01" horiz-adv-x="1126" 
d="M149 1303h633q122 0 186.5 -64.5t64.5 -186.5v-801q0 -121 -64.5 -186t-186.5 -65h-633v560h-102v215h102v528zM726 317v670q0 46 -46 46h-224v-258h121v-215h-121v-290h224q46 0 46 47z" />
    <glyph glyph-name="e.ss01" horiz-adv-x="949" 
d="M689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="eacute.ss01" horiz-adv-x="949" 
d="M478 1676h270l-148 -270h-205zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="ecaron.ss01" horiz-adv-x="949" 
d="M380 1406l-147 270h195l75 -157l74 157h196l-147 -270h-246zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="ecircumflex.ss01" horiz-adv-x="949" 
d="M419 1406h-196l148 270h245l147 -270h-195l-75 156zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="edieresis.ss01" horiz-adv-x="949" 
d="M186 1443v233h261v-233h-261zM559 1443v233h260v-233h-260zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="edotaccent.ss01" horiz-adv-x="949" 
d="M372 1443v233h261v-233h-261zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="egrave.ss01" horiz-adv-x="949" 
d="M419 1406l-149 270h270l84 -270h-205zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="emacron.ss01" horiz-adv-x="949" 
d="M242 1480v196h512v-196h-512zM689 531h-279v-261h521l-149 -270h-680v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="eogonek.ss01" horiz-adv-x="949" 
d="M410 270h521l-149 -270l-125 -223h237l-84 -149h-196q-97 0 -133 60t12 148l93 164h-484v1303h829l-149 -270h-372v-232h428l-149 -270h-279v-261z" />
    <glyph glyph-name="f.ss01" horiz-adv-x="949" 
d="M689 531h-279v-531h-308v1303h829l-149 -270h-372v-232h428z" />
    <glyph glyph-name="h.ss01" horiz-adv-x="1089" 
d="M680 808v495h307v-1303h-307v538h-270v-538h-308v1303h308v-495h270z" />
    <glyph glyph-name="i.ss01" horiz-adv-x="512" 
d="M410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="iacute.ss01" horiz-adv-x="512" 
d="M242 1676h270l-149 -270h-205zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="icircumflex.ss01" horiz-adv-x="512" 
d="M181 1406h-196l147 270h246l147 -270h-195l-75 156zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="idieresis.ss01" horiz-adv-x="512" 
d="M-61 1443v233h260v-233h-260zM311 1443v233h261v-233h-261zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="i.loclTRK.ss01" horiz-adv-x="512" 
d="M123 1443v233h261v-233h-261zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="igrave.ss01" horiz-adv-x="512" 
d="M149 1406l-149 270h270l84 -270h-205zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="imacron.ss01" horiz-adv-x="512" 
d="M0 1480v196h512v-196h-512zM410 1303v-1303h-308v1303h308z" />
    <glyph glyph-name="iogonek.ss01" horiz-adv-x="512" 
d="M410 0l-125 -223h236l-83 -149h-196q-97 0 -133 60t12 148l93 164h-112v1303h308v-1303z" />
    <glyph glyph-name="j.ss01" horiz-adv-x="701" 
d="M298 317v986h307v-1052q0 -121 -64.5 -186t-186.5 -65h-177l-149 270h223q47 0 47 47z" />
    <glyph glyph-name="k.ss01" horiz-adv-x="1098" 
d="M410 0h-308v1303h308v-502h109l216 502h336l-272 -631l272 -672h-336l-216 531h-109v-531z" />
    <glyph glyph-name="uni0137.ss01" horiz-adv-x="1098" 
d="M410 0h-308v1303h308v-502h109l216 502h336l-272 -631l272 -672h-336l-216 531h-109v-531zM438 -102h269l-148 -270h-205z" />
    <glyph glyph-name="m.ss01" horiz-adv-x="1396" 
d="M410 0h-308v1303h317l279 -517l279 517h317v-1303h-307v763l-289 -517l-288 517v-763z" />
    <glyph glyph-name="n.ss01" horiz-adv-x="1135" 
d="M726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289z" />
    <glyph glyph-name="nacute.ss01" horiz-adv-x="1135" 
d="M521 1676h270l-149 -270h-204zM726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289z" />
    <glyph glyph-name="ncaron.ss01" horiz-adv-x="1135" 
d="M417 1406l-147 270h195l75 -157l74 157h196l-147 -270h-246zM726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289z" />
    <glyph glyph-name="uni0146.ss01" horiz-adv-x="1135" 
d="M726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289zM447 -102h270l-149 -270h-205z" />
    <glyph glyph-name="ntilde.ss01" horiz-adv-x="1135" 
d="M726 1527v149h149v-131q0 -67 -36.5 -103t-103.5 -36h-106l-205 149h-5v-149h-149v130q0 67 36.5 103.5t103.5 36.5h106l205 -149h5zM726 629v674h307v-1303h-288l-335 674v-674h-308v1303h289z" />
    <glyph glyph-name="p.ss01" horiz-adv-x="1051" 
d="M410 0h-308v1303h633q122 0 187 -65t65 -186v-397q0 -121 -65 -186t-187 -65h-325v-404zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="r.ss01" 
d="M858 426l147 -426h-316l-140 404h-139v-404h-308v1303h633q122 0 187 -65t65 -186v-397q0 -177 -129 -229zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="racute.ss01" 
d="M521 1676h270l-149 -270h-204zM858 426l147 -426h-316l-140 404h-139v-404h-308v1303h633q122 0 187 -65t65 -186v-397q0 -177 -129 -229zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="rcaron.ss01" 
d="M801 1676l-148 -270h-245l-147 270h195l75 -157l74 157h196zM858 426l147 -426h-316l-140 404h-139v-404h-308v1303h633q122 0 187 -65t65 -186v-397q0 -177 -129 -229zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223z" />
    <glyph glyph-name="uni0157.ss01" 
d="M858 426l147 -426h-316l-140 404h-139v-404h-308v1303h633q122 0 187 -65t65 -186v-397q0 -177 -129 -229zM410 1033v-359h223q47 0 47 47v266q0 46 -47 46h-223zM540 -372h-205l84 270h270z" />
    <glyph glyph-name="u.ss01" horiz-adv-x="1083" 
d="M981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="uacute.ss01" horiz-adv-x="1083" 
d="M512 1676h270l-149 -270h-205zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="ucircumflex.ss01" horiz-adv-x="1083" 
d="M456 1406h-195l147 270h245l148 -270h-196l-74 156zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="udieresis.ss01" horiz-adv-x="1083" 
d="M223 1443v233h261v-233h-261zM596 1443v233h260v-233h-260zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="ugrave.ss01" horiz-adv-x="1083" 
d="M438 1406l-149 270h270l83 -270h-204zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="uhungarumlaut.ss01" horiz-adv-x="1083" 
d="M335 1676h270l-149 -270h-205zM661 1676h270l-149 -270h-205zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="umacron.ss01" horiz-adv-x="1083" 
d="M279 1480v196h512v-196h-512zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="uogonek.ss01" horiz-adv-x="1083" 
d="M981 1303v-1303h-112l-124 -223h236l-84 -149h-195q-97 0 -133 60t12 148l93 164h-326q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307z" />
    <glyph glyph-name="uring.ss01" horiz-adv-x="1083" 
d="M363 1473v80q0 123 121 123h116q121 0 121 -123v-80q0 -123 -121 -123h-116q-121 0 -121 123zM493 1551v-76q0 -19 19 -19h60q18 0 18 19v76q0 19 -18 19h-60q-19 0 -19 -19zM981 0h-633q-122 0 -186.5 65t-64.5 186v1052h307v-986q0 -47 47 -47h223v1033h307v-1303z" />
    <glyph glyph-name="fi.ss01" horiz-adv-x="1461" 
d="M689 531h-279v-531h-308v1303h829l-149 -270h-372v-232h428zM1359 1303v-1303h-307v1303h307z" />
    <glyph glyph-name="pi.ss01" horiz-adv-x="1033" 
d="M354 0h-280v931h792v-659q0 -39 39 -39h128l-130 -233h-102q-104 0 -159.5 55.5t-55.5 158.5v484h-232v-698z" />
    <glyph glyph-name="zero.dnom" horiz-adv-x="660" 
d="M443 0h-225q-144 0 -144 143v458q0 144 144 144h225q143 0 143 -144v-458q0 -143 -143 -143zM251 560v-376q0 -26 26 -26h107q26 0 26 26v376q0 26 -26 26h-107q-26 0 -26 -26z" />
    <glyph glyph-name="one.dnom" horiz-adv-x="372" 
d="M37 586v159h261v-745h-177v586h-84z" />
    <glyph glyph-name="two.dnom" horiz-adv-x="660" 
d="M510 0h-436v235q0 127 127 156l184 43q25 6 25 30v96q0 26 -26 26h-103q-26 0 -26 -26v-59h-177v100q0 144 144 144h221q143 0 143 -144v-152q0 -129 -126 -157l-184 -43q-25 -6 -25 -29v-62h345z" />
    <glyph glyph-name="three.dnom" horiz-adv-x="651" 
d="M65 143v101h177v-60q0 -26 26 -26h106q26 0 26 26v92q0 26 -26 26h-132v158h132q26 0 26 26v74q0 26 -26 26h-106q-26 0 -26 -26v-50h-177v91q0 144 144 144h225q143 0 143 -144v-154l-67 -67l67 -67v-170q0 -143 -143 -143h-225q-144 0 -144 143z" />
    <glyph glyph-name="four.dnom" horiz-adv-x="614" 
d="M354 153h-317v158l266 434h228v-434h65l-65 -123v-188h-177v153zM354 555l-149 -244h149v244z" />
    <glyph glyph-name="five.dnom" horiz-adv-x="651" 
d="M74 143v101h177v-58q0 -28 28 -28h105q26 0 26 28v119q0 26 -26 26h-310v414h522l-86 -159h-259v-96h192q68 0 105.5 -38t37.5 -106v-203q0 -143 -143 -143h-225q-144 0 -144 143z" />
    <glyph glyph-name="six.dnom" horiz-adv-x="651" 
d="M74 143v458q0 144 144 144h378l-86 -159h-231q-28 0 -28 -26v-70h192q68 0 105.5 -38t37.5 -106v-203q0 -143 -143 -143h-225q-144 0 -144 143zM251 331v-147q0 -26 28 -26h105q26 0 26 26v121q0 26 -26 26h-133z" />
    <glyph glyph-name="seven.dnom" horiz-adv-x="558" 
d="M324 586h-287v159h484v-159l-160 -327v-259h-177v279q0 31 17 65z" />
    <glyph glyph-name="eight.dnom" horiz-adv-x="660" 
d="M74 143v170l67 67l-67 67v154q0 144 144 144h225q143 0 143 -144v-154l-67 -67l67 -67v-170q0 -143 -143 -143h-225q-144 0 -144 143zM251 560v-74q0 -26 26 -26h105q28 0 28 26v74q0 26 -28 26h-105q-26 0 -26 -26zM251 276v-92q0 -26 26 -26h105q28 0 28 26v92
q0 26 -28 26h-105q-26 0 -26 -26z" />
    <glyph glyph-name="nine.dnom" horiz-adv-x="651" 
d="M577 601v-458q0 -143 -143 -143h-285l-84 158h307q28 0 28 26v71h-191q-68 0 -106 37.5t-38 105.5v203q0 144 144 144h225q143 0 143 -144zM400 413v147q0 26 -28 26h-104q-26 0 -26 -26v-121q0 -26 26 -26h132z" />
    <glyph glyph-name="zero.numr" horiz-adv-x="660" 
d="M443 559h-225q-144 0 -144 143v458q0 143 144 143h225q143 0 143 -143v-458q0 -143 -143 -143zM251 1119v-376q0 -26 26 -26h107q26 0 26 26v376q0 26 -26 26h-107q-26 0 -26 -26z" />
    <glyph glyph-name="one.numr" horiz-adv-x="372" 
d="M37 1145v158h261v-744h-177v586h-84z" />
    <glyph glyph-name="two.numr" horiz-adv-x="660" 
d="M510 559h-436v234q0 128 127 157l184 42q25 6 25 30v97q0 26 -26 26h-103q-26 0 -26 -26v-60h-177v101q0 143 144 143h221q143 0 143 -143v-153q0 -127 -126 -156l-184 -43q-25 -6 -25 -30v-61h345z" />
    <glyph glyph-name="three.numr" horiz-adv-x="651" 
d="M65 702v100h177v-59q0 -26 26 -26h106q26 0 26 26v91q0 26 -26 26h-132v158h132q26 0 26 26v75q0 26 -26 26h-106q-26 0 -26 -26v-50h-177v91q0 143 144 143h225q143 0 143 -143v-155l-67 -67l67 -67v-169q0 -143 -143 -143h-225q-144 0 -144 143z" />
    <glyph glyph-name="four.numr" horiz-adv-x="614" 
d="M354 711h-317v158l266 434h228v-434h65l-65 -122v-188h-177v152zM354 1113l-149 -244h149v244z" />
    <glyph glyph-name="five.numr" horiz-adv-x="651" 
d="M74 702v100h177v-57q0 -28 28 -28h105q26 0 26 28v119q0 26 -26 26h-310v413h522l-86 -158h-259v-97h192q68 0 105.5 -37.5t37.5 -105.5v-203q0 -143 -143 -143h-225q-144 0 -144 143z" />
    <glyph glyph-name="six.numr" horiz-adv-x="651" 
d="M74 702v458q0 143 144 143h378l-86 -158h-231q-28 0 -28 -26v-71h192q68 0 105.5 -37.5t37.5 -105.5v-203q0 -143 -143 -143h-225q-144 0 -144 143zM251 890v-147q0 -26 28 -26h105q26 0 26 26v121q0 26 -26 26h-133z" />
    <glyph glyph-name="seven.numr" horiz-adv-x="558" 
d="M324 1145h-287v158h484v-158l-160 -328v-258h-177v279q0 31 17 65z" />
    <glyph glyph-name="eight.numr" horiz-adv-x="660" 
d="M74 702v169l67 67l-67 67v155q0 143 144 143h225q143 0 143 -143v-155l-67 -67l67 -67v-169q0 -143 -143 -143h-225q-144 0 -144 143zM251 1119v-75q0 -26 26 -26h105q28 0 28 26v75q0 26 -28 26h-105q-26 0 -26 -26zM251 834v-91q0 -26 26 -26h105q28 0 28 26v91
q0 26 -28 26h-105q-26 0 -26 -26z" />
    <glyph glyph-name="nine.numr" horiz-adv-x="651" 
d="M577 1160v-458q0 -143 -143 -143h-285l-84 158h307q28 0 28 26v71h-191q-68 0 -106 37.5t-38 105.5v203q0 143 144 143h225q143 0 143 -143zM400 972v147q0 26 -28 26h-104q-26 0 -26 -26v-121q0 -26 26 -26h132z" />
    <glyph glyph-name="caron.alt" horiz-adv-x="446" 
d="M130 1303h270l-149 -270h-204z" />
    <hkern u1="&#x22;" u2="&#x192;" k="56" />
    <hkern u1="&#x22;" u2="&#xbf;" k="112" />
    <hkern u1="&#x22;" u2="&#xa3;" k="37" />
    <hkern u1="&#x22;" u2="&#x34;" k="74" />
    <hkern u1="&#x22;" u2="&#x2f;" k="74" />
    <hkern u1="&#x23;" g2="ae.ss01" k="37" />
    <hkern u1="&#x23;" g2="atilde.ss01" k="37" />
    <hkern u1="&#x23;" g2="aring.ss01" k="37" />
    <hkern u1="&#x23;" g2="aogonek.ss01" k="37" />
    <hkern u1="&#x23;" g2="amacron.ss01" k="37" />
    <hkern u1="&#x23;" g2="agrave.ss01" k="37" />
    <hkern u1="&#x23;" g2="adieresis.ss01" k="37" />
    <hkern u1="&#x23;" g2="acircumflex.ss01" k="37" />
    <hkern u1="&#x23;" g2="abreve.ss01" k="37" />
    <hkern u1="&#x23;" g2="aacute.ss01" k="37" />
    <hkern u1="&#x23;" g2="a.ss01" k="37" />
    <hkern u1="&#x23;" g2="AE.ss01" k="37" />
    <hkern u1="&#x23;" g2="Atilde.ss01" k="37" />
    <hkern u1="&#x23;" g2="Aring.ss01" k="37" />
    <hkern u1="&#x23;" g2="Aogonek.ss01" k="37" />
    <hkern u1="&#x23;" g2="Amacron.ss01" k="37" />
    <hkern u1="&#x23;" g2="Agrave.ss01" k="37" />
    <hkern u1="&#x23;" g2="Adieresis.ss01" k="37" />
    <hkern u1="&#x23;" g2="Acircumflex.ss01" k="37" />
    <hkern u1="&#x23;" g2="Abreve.ss01" k="37" />
    <hkern u1="&#x23;" g2="Aacute.ss01" k="37" />
    <hkern u1="&#x23;" g2="A.ss01" k="37" />
    <hkern u1="&#x23;" u2="&#x1ef9;" k="74" />
    <hkern u1="&#x23;" u2="&#x1ef8;" k="74" />
    <hkern u1="&#x23;" u2="&#x1ef7;" k="74" />
    <hkern u1="&#x23;" u2="&#x1ef6;" k="74" />
    <hkern u1="&#x23;" u2="&#x1ef5;" k="74" />
    <hkern u1="&#x23;" u2="&#x1ef4;" k="74" />
    <hkern u1="&#x23;" u2="&#x1ef3;" k="74" />
    <hkern u1="&#x23;" u2="&#x1ef2;" k="74" />
    <hkern u1="&#x23;" u2="&#x1eb7;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eb6;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eb5;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eb4;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eb3;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eb2;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eb1;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eb0;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eaf;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eae;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ead;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eac;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eab;" k="37" />
    <hkern u1="&#x23;" u2="&#x1eaa;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea9;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea8;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea7;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea6;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea5;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea4;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea3;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea2;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea1;" k="37" />
    <hkern u1="&#x23;" u2="&#x1ea0;" k="37" />
    <hkern u1="&#x23;" u2="&#x21b;" k="19" />
    <hkern u1="&#x23;" u2="&#x21a;" k="19" />
    <hkern u1="&#x23;" u2="&#x17e;" k="19" />
    <hkern u1="&#x23;" u2="&#x17d;" k="19" />
    <hkern u1="&#x23;" u2="&#x17c;" k="19" />
    <hkern u1="&#x23;" u2="&#x17b;" k="19" />
    <hkern u1="&#x23;" u2="&#x17a;" k="19" />
    <hkern u1="&#x23;" u2="&#x179;" k="19" />
    <hkern u1="&#x23;" u2="&#x178;" k="74" />
    <hkern u1="&#x23;" u2="&#x165;" k="19" />
    <hkern u1="&#x23;" u2="&#x164;" k="19" />
    <hkern u1="&#x23;" u2="&#x163;" k="19" />
    <hkern u1="&#x23;" u2="&#x162;" k="19" />
    <hkern u1="&#x23;" u2="&#x105;" k="37" />
    <hkern u1="&#x23;" u2="&#x104;" k="37" />
    <hkern u1="&#x23;" u2="&#x103;" k="37" />
    <hkern u1="&#x23;" u2="&#x102;" k="37" />
    <hkern u1="&#x23;" u2="&#x101;" k="37" />
    <hkern u1="&#x23;" u2="&#x100;" k="37" />
    <hkern u1="&#x23;" u2="&#xff;" k="74" />
    <hkern u1="&#x23;" u2="&#xfd;" k="74" />
    <hkern u1="&#x23;" u2="&#xe6;" k="37" />
    <hkern u1="&#x23;" u2="&#xe5;" k="37" />
    <hkern u1="&#x23;" u2="&#xe4;" k="37" />
    <hkern u1="&#x23;" u2="&#xe3;" k="37" />
    <hkern u1="&#x23;" u2="&#xe2;" k="37" />
    <hkern u1="&#x23;" u2="&#xe1;" k="37" />
    <hkern u1="&#x23;" u2="&#xe0;" k="37" />
    <hkern u1="&#x23;" u2="&#xdd;" k="74" />
    <hkern u1="&#x23;" u2="&#xc6;" k="37" />
    <hkern u1="&#x23;" u2="&#xc5;" k="37" />
    <hkern u1="&#x23;" u2="&#xc4;" k="37" />
    <hkern u1="&#x23;" u2="&#xc3;" k="37" />
    <hkern u1="&#x23;" u2="&#xc2;" k="37" />
    <hkern u1="&#x23;" u2="&#xc1;" k="37" />
    <hkern u1="&#x23;" u2="&#xc0;" k="37" />
    <hkern u1="&#x23;" u2="&#xa5;" k="74" />
    <hkern u1="&#x23;" u2="&#x7d;" k="19" />
    <hkern u1="&#x23;" u2="z" k="19" />
    <hkern u1="&#x23;" u2="y" k="74" />
    <hkern u1="&#x23;" u2="x" k="56" />
    <hkern u1="&#x23;" u2="w" k="37" />
    <hkern u1="&#x23;" u2="v" k="37" />
    <hkern u1="&#x23;" u2="t" k="19" />
    <hkern u1="&#x23;" u2="a" k="37" />
    <hkern u1="&#x23;" u2="]" k="19" />
    <hkern u1="&#x23;" u2="Z" k="19" />
    <hkern u1="&#x23;" u2="Y" k="74" />
    <hkern u1="&#x23;" u2="X" k="56" />
    <hkern u1="&#x23;" u2="W" k="37" />
    <hkern u1="&#x23;" u2="V" k="37" />
    <hkern u1="&#x23;" u2="T" k="19" />
    <hkern u1="&#x23;" u2="A" k="37" />
    <hkern u1="&#x23;" u2="&#x29;" k="19" />
    <hkern u1="&#x23;" u2="&#xbf;" k="19" />
    <hkern u1="&#x23;" u2="\" k="37" />
    <hkern u1="&#x23;" u2="&#x37;" k="37" />
    <hkern u1="&#x23;" u2="&#x2f;" k="37" />
    <hkern u1="&#x24;" g2="ae.ss01" k="37" />
    <hkern u1="&#x24;" g2="atilde.ss01" k="37" />
    <hkern u1="&#x24;" g2="aring.ss01" k="37" />
    <hkern u1="&#x24;" g2="aogonek.ss01" k="37" />
    <hkern u1="&#x24;" g2="amacron.ss01" k="37" />
    <hkern u1="&#x24;" g2="agrave.ss01" k="37" />
    <hkern u1="&#x24;" g2="adieresis.ss01" k="37" />
    <hkern u1="&#x24;" g2="acircumflex.ss01" k="37" />
    <hkern u1="&#x24;" g2="abreve.ss01" k="37" />
    <hkern u1="&#x24;" g2="aacute.ss01" k="37" />
    <hkern u1="&#x24;" g2="a.ss01" k="37" />
    <hkern u1="&#x24;" g2="AE.ss01" k="37" />
    <hkern u1="&#x24;" g2="Atilde.ss01" k="37" />
    <hkern u1="&#x24;" g2="Aring.ss01" k="37" />
    <hkern u1="&#x24;" g2="Aogonek.ss01" k="37" />
    <hkern u1="&#x24;" g2="Amacron.ss01" k="37" />
    <hkern u1="&#x24;" g2="Agrave.ss01" k="37" />
    <hkern u1="&#x24;" g2="Adieresis.ss01" k="37" />
    <hkern u1="&#x24;" g2="Acircumflex.ss01" k="37" />
    <hkern u1="&#x24;" g2="Abreve.ss01" k="37" />
    <hkern u1="&#x24;" g2="Aacute.ss01" k="37" />
    <hkern u1="&#x24;" g2="A.ss01" k="37" />
    <hkern u1="&#x24;" u2="&#x20ac;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ef9;" k="19" />
    <hkern u1="&#x24;" u2="&#x1ef8;" k="19" />
    <hkern u1="&#x24;" u2="&#x1ef7;" k="19" />
    <hkern u1="&#x24;" u2="&#x1ef6;" k="19" />
    <hkern u1="&#x24;" u2="&#x1ef5;" k="19" />
    <hkern u1="&#x24;" u2="&#x1ef4;" k="19" />
    <hkern u1="&#x24;" u2="&#x1ef3;" k="19" />
    <hkern u1="&#x24;" u2="&#x1ef2;" k="19" />
    <hkern u1="&#x24;" u2="&#x1ee3;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ee2;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ee1;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ee0;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1edf;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ede;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1edd;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1edc;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1edb;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1eda;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed9;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed8;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed7;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed6;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed5;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed4;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed3;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed2;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed1;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ed0;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ecf;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ece;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ecd;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1ecc;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1eb7;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eb6;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eb5;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eb4;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eb3;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eb2;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eb1;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eb0;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eaf;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eae;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ead;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eac;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eab;" k="37" />
    <hkern u1="&#x24;" u2="&#x1eaa;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea9;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea8;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea7;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea6;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea5;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea4;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea3;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea2;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea1;" k="37" />
    <hkern u1="&#x24;" u2="&#x1ea0;" k="37" />
    <hkern u1="&#x24;" u2="&#x1a1;" k="-19" />
    <hkern u1="&#x24;" u2="&#x1a0;" k="-19" />
    <hkern u1="&#x24;" u2="&#x178;" k="19" />
    <hkern u1="&#x24;" u2="&#x153;" k="-19" />
    <hkern u1="&#x24;" u2="&#x152;" k="-19" />
    <hkern u1="&#x24;" u2="&#x151;" k="-19" />
    <hkern u1="&#x24;" u2="&#x150;" k="-19" />
    <hkern u1="&#x24;" u2="&#x14d;" k="-19" />
    <hkern u1="&#x24;" u2="&#x14c;" k="-19" />
    <hkern u1="&#x24;" u2="&#x123;" k="-19" />
    <hkern u1="&#x24;" u2="&#x122;" k="-19" />
    <hkern u1="&#x24;" u2="&#x11f;" k="-19" />
    <hkern u1="&#x24;" u2="&#x11e;" k="-19" />
    <hkern u1="&#x24;" u2="&#x10d;" k="-19" />
    <hkern u1="&#x24;" u2="&#x10c;" k="-19" />
    <hkern u1="&#x24;" u2="&#x107;" k="-19" />
    <hkern u1="&#x24;" u2="&#x106;" k="-19" />
    <hkern u1="&#x24;" u2="&#x105;" k="37" />
    <hkern u1="&#x24;" u2="&#x104;" k="37" />
    <hkern u1="&#x24;" u2="&#x103;" k="37" />
    <hkern u1="&#x24;" u2="&#x102;" k="37" />
    <hkern u1="&#x24;" u2="&#x101;" k="37" />
    <hkern u1="&#x24;" u2="&#x100;" k="37" />
    <hkern u1="&#x24;" u2="&#xff;" k="19" />
    <hkern u1="&#x24;" u2="&#xfd;" k="19" />
    <hkern u1="&#x24;" u2="&#xf8;" k="-19" />
    <hkern u1="&#x24;" u2="&#xf6;" k="-19" />
    <hkern u1="&#x24;" u2="&#xf5;" k="-19" />
    <hkern u1="&#x24;" u2="&#xf4;" k="-19" />
    <hkern u1="&#x24;" u2="&#xf3;" k="-19" />
    <hkern u1="&#x24;" u2="&#xf2;" k="-19" />
    <hkern u1="&#x24;" u2="&#xe7;" k="-19" />
    <hkern u1="&#x24;" u2="&#xe6;" k="37" />
    <hkern u1="&#x24;" u2="&#xe5;" k="37" />
    <hkern u1="&#x24;" u2="&#xe4;" k="37" />
    <hkern u1="&#x24;" u2="&#xe3;" k="37" />
    <hkern u1="&#x24;" u2="&#xe2;" k="37" />
    <hkern u1="&#x24;" u2="&#xe1;" k="37" />
    <hkern u1="&#x24;" u2="&#xe0;" k="37" />
    <hkern u1="&#x24;" u2="&#xdd;" k="19" />
    <hkern u1="&#x24;" u2="&#xd8;" k="-19" />
    <hkern u1="&#x24;" u2="&#xd6;" k="-19" />
    <hkern u1="&#x24;" u2="&#xd5;" k="-19" />
    <hkern u1="&#x24;" u2="&#xd4;" k="-19" />
    <hkern u1="&#x24;" u2="&#xd3;" k="-19" />
    <hkern u1="&#x24;" u2="&#xd2;" k="-19" />
    <hkern u1="&#x24;" u2="&#xc7;" k="-19" />
    <hkern u1="&#x24;" u2="&#xc6;" k="37" />
    <hkern u1="&#x24;" u2="&#xc5;" k="37" />
    <hkern u1="&#x24;" u2="&#xc4;" k="37" />
    <hkern u1="&#x24;" u2="&#xc3;" k="37" />
    <hkern u1="&#x24;" u2="&#xc2;" k="37" />
    <hkern u1="&#x24;" u2="&#xc1;" k="37" />
    <hkern u1="&#x24;" u2="&#xc0;" k="37" />
    <hkern u1="&#x24;" u2="&#xa9;" k="-19" />
    <hkern u1="&#x24;" u2="&#xa5;" k="19" />
    <hkern u1="&#x24;" u2="y" k="19" />
    <hkern u1="&#x24;" u2="x" k="19" />
    <hkern u1="&#x24;" u2="q" k="-19" />
    <hkern u1="&#x24;" u2="o" k="-19" />
    <hkern u1="&#x24;" u2="g" k="-19" />
    <hkern u1="&#x24;" u2="c" k="-19" />
    <hkern u1="&#x24;" u2="a" k="37" />
    <hkern u1="&#x24;" u2="Y" k="19" />
    <hkern u1="&#x24;" u2="X" k="19" />
    <hkern u1="&#x24;" u2="Q" k="-19" />
    <hkern u1="&#x24;" u2="O" k="-19" />
    <hkern u1="&#x24;" u2="G" k="-19" />
    <hkern u1="&#x24;" u2="C" k="-19" />
    <hkern u1="&#x24;" u2="A" k="37" />
    <hkern u1="&#x24;" u2="&#x40;" k="-19" />
    <hkern u1="&#x24;" u2="&#x38;" k="-19" />
    <hkern u1="&#x24;" u2="&#x36;" k="-19" />
    <hkern u1="&#x24;" u2="&#x30;" k="-19" />
    <hkern u1="&#x24;" u2="&#x26;" k="-19" />
    <hkern u1="&#x24;" u2="\" k="19" />
    <hkern u1="&#x24;" u2="&#x3f;" k="-9" />
    <hkern u1="&#x24;" u2="&#x34;" k="-19" />
    <hkern u1="&#x24;" u2="&#x2f;" k="19" />
    <hkern u1="&#x25;" u2="\" k="74" />
    <hkern u1="&#x25;" u2="&#x3f;" k="37" />
    <hkern u1="&#x25;" u2="&#x37;" k="93" />
    <hkern u1="&#x25;" u2="&#x31;" k="37" />
    <hkern u1="&#x26;" g2="caron.alt" k="37" />
    <hkern u1="&#x26;" g2="pi.ss01" k="-9" />
    <hkern u1="&#x26;" g2="j.ss01" k="37" />
    <hkern u1="&#x26;" g2="ae.ss01" k="37" />
    <hkern u1="&#x26;" g2="atilde.ss01" k="47" />
    <hkern u1="&#x26;" g2="aring.ss01" k="47" />
    <hkern u1="&#x26;" g2="aogonek.ss01" k="47" />
    <hkern u1="&#x26;" g2="amacron.ss01" k="47" />
    <hkern u1="&#x26;" g2="agrave.ss01" k="47" />
    <hkern u1="&#x26;" g2="adieresis.ss01" k="47" />
    <hkern u1="&#x26;" g2="acircumflex.ss01" k="47" />
    <hkern u1="&#x26;" g2="abreve.ss01" k="47" />
    <hkern u1="&#x26;" g2="aacute.ss01" k="47" />
    <hkern u1="&#x26;" g2="a.ss01" k="47" />
    <hkern u1="&#x26;" g2="i.loclTRK" k="9" />
    <hkern u1="&#x26;" g2="J.ss01" k="37" />
    <hkern u1="&#x26;" g2="AE.ss01" k="37" />
    <hkern u1="&#x26;" g2="Atilde.ss01" k="47" />
    <hkern u1="&#x26;" g2="Aring.ss01" k="47" />
    <hkern u1="&#x26;" g2="Aogonek.ss01" k="47" />
    <hkern u1="&#x26;" g2="Amacron.ss01" k="47" />
    <hkern u1="&#x26;" g2="Agrave.ss01" k="47" />
    <hkern u1="&#x26;" g2="Adieresis.ss01" k="47" />
    <hkern u1="&#x26;" g2="Acircumflex.ss01" k="47" />
    <hkern u1="&#x26;" g2="Abreve.ss01" k="47" />
    <hkern u1="&#x26;" g2="Aacute.ss01" k="47" />
    <hkern u1="&#x26;" g2="A.ss01" k="47" />
    <hkern u1="&#x26;" g2="fi" k="9" />
    <hkern u1="&#x26;" u2="&#x2122;" k="37" />
    <hkern u1="&#x26;" u2="&#x2021;" k="37" />
    <hkern u1="&#x26;" u2="&#x2020;" k="37" />
    <hkern u1="&#x26;" u2="&#x201d;" k="37" />
    <hkern u1="&#x26;" u2="&#x201c;" k="37" />
    <hkern u1="&#x26;" u2="&#x2019;" k="37" />
    <hkern u1="&#x26;" u2="&#x2018;" k="37" />
    <hkern u1="&#x26;" u2="&#x1ef9;" k="56" />
    <hkern u1="&#x26;" u2="&#x1ef8;" k="56" />
    <hkern u1="&#x26;" u2="&#x1ef7;" k="56" />
    <hkern u1="&#x26;" u2="&#x1ef6;" k="56" />
    <hkern u1="&#x26;" u2="&#x1ef5;" k="56" />
    <hkern u1="&#x26;" u2="&#x1ef4;" k="56" />
    <hkern u1="&#x26;" u2="&#x1ef3;" k="56" />
    <hkern u1="&#x26;" u2="&#x1ef2;" k="56" />
    <hkern u1="&#x26;" u2="&#x1ef1;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ef0;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eef;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eee;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eed;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eec;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eeb;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eea;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ee9;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ee8;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ee7;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ee6;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ee5;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ee4;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ecb;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eca;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec9;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec8;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec7;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec6;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec5;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec4;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec3;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec2;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec1;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ec0;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ebf;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ebe;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ebd;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ebc;" k="9" />
    <hkern u1="&#x26;" u2="&#x1ebb;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eba;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eb9;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eb8;" k="9" />
    <hkern u1="&#x26;" u2="&#x1eb7;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eb6;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eb5;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eb4;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eb3;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eb2;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eb1;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eb0;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eaf;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eae;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ead;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eac;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eab;" k="47" />
    <hkern u1="&#x26;" u2="&#x1eaa;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea9;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea8;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea7;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea6;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea5;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea4;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea3;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea2;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea1;" k="47" />
    <hkern u1="&#x26;" u2="&#x1ea0;" k="47" />
    <hkern u1="&#x26;" u2="&#x2dd;" k="37" />
    <hkern u1="&#x26;" u2="&#x2dc;" k="37" />
    <hkern u1="&#x26;" u2="&#x2da;" k="37" />
    <hkern u1="&#x26;" u2="&#x2d9;" k="37" />
    <hkern u1="&#x26;" u2="&#x2d8;" k="37" />
    <hkern u1="&#x26;" u2="&#x2c9;" k="37" />
    <hkern u1="&#x26;" u2="&#x2c7;" k="37" />
    <hkern u1="&#x26;" u2="&#x2c6;" k="37" />
    <hkern u1="&#x26;" u2="&#x21b;" k="37" />
    <hkern u1="&#x26;" u2="&#x21a;" k="37" />
    <hkern u1="&#x26;" u2="&#x1b0;" k="9" />
    <hkern u1="&#x26;" u2="&#x1af;" k="9" />
    <hkern u1="&#x26;" u2="&#x17e;" k="19" />
    <hkern u1="&#x26;" u2="&#x17d;" k="19" />
    <hkern u1="&#x26;" u2="&#x17c;" k="19" />
    <hkern u1="&#x26;" u2="&#x17b;" k="19" />
    <hkern u1="&#x26;" u2="&#x17a;" k="19" />
    <hkern u1="&#x26;" u2="&#x179;" k="19" />
    <hkern u1="&#x26;" u2="&#x178;" k="56" />
    <hkern u1="&#x26;" u2="&#x173;" k="9" />
    <hkern u1="&#x26;" u2="&#x172;" k="9" />
    <hkern u1="&#x26;" u2="&#x171;" k="9" />
    <hkern u1="&#x26;" u2="&#x170;" k="9" />
    <hkern u1="&#x26;" u2="&#x16f;" k="9" />
    <hkern u1="&#x26;" u2="&#x16e;" k="9" />
    <hkern u1="&#x26;" u2="&#x16b;" k="9" />
    <hkern u1="&#x26;" u2="&#x16a;" k="9" />
    <hkern u1="&#x26;" u2="&#x169;" k="9" />
    <hkern u1="&#x26;" u2="&#x168;" k="9" />
    <hkern u1="&#x26;" u2="&#x165;" k="37" />
    <hkern u1="&#x26;" u2="&#x164;" k="37" />
    <hkern u1="&#x26;" u2="&#x163;" k="37" />
    <hkern u1="&#x26;" u2="&#x162;" k="37" />
    <hkern u1="&#x26;" u2="&#x159;" k="9" />
    <hkern u1="&#x26;" u2="&#x158;" k="9" />
    <hkern u1="&#x26;" u2="&#x157;" k="9" />
    <hkern u1="&#x26;" u2="&#x156;" k="9" />
    <hkern u1="&#x26;" u2="&#x155;" k="9" />
    <hkern u1="&#x26;" u2="&#x154;" k="9" />
    <hkern u1="&#x26;" u2="&#x148;" k="9" />
    <hkern u1="&#x26;" u2="&#x147;" k="9" />
    <hkern u1="&#x26;" u2="&#x146;" k="9" />
    <hkern u1="&#x26;" u2="&#x145;" k="9" />
    <hkern u1="&#x26;" u2="&#x144;" k="9" />
    <hkern u1="&#x26;" u2="&#x143;" k="9" />
    <hkern u1="&#x26;" u2="&#x137;" k="9" />
    <hkern u1="&#x26;" u2="&#x136;" k="9" />
    <hkern u1="&#x26;" u2="&#x130;" k="9" />
    <hkern u1="&#x26;" u2="&#x12f;" k="9" />
    <hkern u1="&#x26;" u2="&#x12e;" k="9" />
    <hkern u1="&#x26;" u2="&#x12b;" k="9" />
    <hkern u1="&#x26;" u2="&#x12a;" k="9" />
    <hkern u1="&#x26;" u2="&#x129;" k="9" />
    <hkern u1="&#x26;" u2="&#x128;" k="9" />
    <hkern u1="&#x26;" u2="&#x11b;" k="9" />
    <hkern u1="&#x26;" u2="&#x11a;" k="9" />
    <hkern u1="&#x26;" u2="&#x119;" k="9" />
    <hkern u1="&#x26;" u2="&#x118;" k="9" />
    <hkern u1="&#x26;" u2="&#x117;" k="9" />
    <hkern u1="&#x26;" u2="&#x116;" k="9" />
    <hkern u1="&#x26;" u2="&#x113;" k="9" />
    <hkern u1="&#x26;" u2="&#x112;" k="9" />
    <hkern u1="&#x26;" u2="&#x111;" k="9" />
    <hkern u1="&#x26;" u2="&#x110;" k="9" />
    <hkern u1="&#x26;" u2="&#x10f;" k="9" />
    <hkern u1="&#x26;" u2="&#x10e;" k="9" />
    <hkern u1="&#x26;" u2="&#x105;" k="47" />
    <hkern u1="&#x26;" u2="&#x104;" k="47" />
    <hkern u1="&#x26;" u2="&#x103;" k="47" />
    <hkern u1="&#x26;" u2="&#x102;" k="47" />
    <hkern u1="&#x26;" u2="&#x101;" k="47" />
    <hkern u1="&#x26;" u2="&#x100;" k="47" />
    <hkern u1="&#x26;" u2="&#xff;" k="56" />
    <hkern u1="&#x26;" u2="&#xfd;" k="56" />
    <hkern u1="&#x26;" u2="&#xfc;" k="9" />
    <hkern u1="&#x26;" u2="&#xfb;" k="9" />
    <hkern u1="&#x26;" u2="&#xfa;" k="9" />
    <hkern u1="&#x26;" u2="&#xf9;" k="9" />
    <hkern u1="&#x26;" u2="&#xf1;" k="9" />
    <hkern u1="&#x26;" u2="&#xf0;" k="9" />
    <hkern u1="&#x26;" u2="&#xef;" k="9" />
    <hkern u1="&#x26;" u2="&#xee;" k="9" />
    <hkern u1="&#x26;" u2="&#xed;" k="9" />
    <hkern u1="&#x26;" u2="&#xec;" k="9" />
    <hkern u1="&#x26;" u2="&#xeb;" k="9" />
    <hkern u1="&#x26;" u2="&#xea;" k="9" />
    <hkern u1="&#x26;" u2="&#xe9;" k="9" />
    <hkern u1="&#x26;" u2="&#xe8;" k="9" />
    <hkern u1="&#x26;" u2="&#xe6;" k="37" />
    <hkern u1="&#x26;" u2="&#xe5;" k="47" />
    <hkern u1="&#x26;" u2="&#xe4;" k="47" />
    <hkern u1="&#x26;" u2="&#xe3;" k="47" />
    <hkern u1="&#x26;" u2="&#xe2;" k="47" />
    <hkern u1="&#x26;" u2="&#xe1;" k="47" />
    <hkern u1="&#x26;" u2="&#xe0;" k="47" />
    <hkern u1="&#x26;" u2="&#xdd;" k="56" />
    <hkern u1="&#x26;" u2="&#xdc;" k="9" />
    <hkern u1="&#x26;" u2="&#xdb;" k="9" />
    <hkern u1="&#x26;" u2="&#xda;" k="9" />
    <hkern u1="&#x26;" u2="&#xd9;" k="9" />
    <hkern u1="&#x26;" u2="&#xd1;" k="9" />
    <hkern u1="&#x26;" u2="&#xd0;" k="9" />
    <hkern u1="&#x26;" u2="&#xcf;" k="9" />
    <hkern u1="&#x26;" u2="&#xce;" k="9" />
    <hkern u1="&#x26;" u2="&#xcd;" k="9" />
    <hkern u1="&#x26;" u2="&#xcc;" k="9" />
    <hkern u1="&#x26;" u2="&#xcb;" k="9" />
    <hkern u1="&#x26;" u2="&#xca;" k="9" />
    <hkern u1="&#x26;" u2="&#xc9;" k="9" />
    <hkern u1="&#x26;" u2="&#xc8;" k="9" />
    <hkern u1="&#x26;" u2="&#xc6;" k="37" />
    <hkern u1="&#x26;" u2="&#xc5;" k="47" />
    <hkern u1="&#x26;" u2="&#xc4;" k="47" />
    <hkern u1="&#x26;" u2="&#xc3;" k="47" />
    <hkern u1="&#x26;" u2="&#xc2;" k="47" />
    <hkern u1="&#x26;" u2="&#xc1;" k="47" />
    <hkern u1="&#x26;" u2="&#xc0;" k="47" />
    <hkern u1="&#x26;" u2="&#xb4;" k="37" />
    <hkern u1="&#x26;" u2="&#xb0;" k="37" />
    <hkern u1="&#x26;" u2="&#xaf;" k="37" />
    <hkern u1="&#x26;" u2="&#xae;" k="37" />
    <hkern u1="&#x26;" u2="&#xa8;" k="37" />
    <hkern u1="&#x26;" u2="&#xa5;" k="56" />
    <hkern u1="&#x26;" u2="&#xa1;" k="-9" />
    <hkern u1="&#x26;" u2="&#x7d;" k="19" />
    <hkern u1="&#x26;" u2="z" k="19" />
    <hkern u1="&#x26;" u2="y" k="56" />
    <hkern u1="&#x26;" u2="x" k="28" />
    <hkern u1="&#x26;" u2="w" k="37" />
    <hkern u1="&#x26;" u2="v" k="37" />
    <hkern u1="&#x26;" u2="u" k="9" />
    <hkern u1="&#x26;" u2="t" k="37" />
    <hkern u1="&#x26;" u2="r" k="9" />
    <hkern u1="&#x26;" u2="p" k="9" />
    <hkern u1="&#x26;" u2="n" k="9" />
    <hkern u1="&#x26;" u2="m" k="9" />
    <hkern u1="&#x26;" u2="k" k="9" />
    <hkern u1="&#x26;" u2="j" k="37" />
    <hkern u1="&#x26;" u2="i" k="9" />
    <hkern u1="&#x26;" u2="h" k="9" />
    <hkern u1="&#x26;" u2="f" k="9" />
    <hkern u1="&#x26;" u2="e" k="9" />
    <hkern u1="&#x26;" u2="d" k="9" />
    <hkern u1="&#x26;" u2="b" k="9" />
    <hkern u1="&#x26;" u2="a" k="47" />
    <hkern u1="&#x26;" u2="`" k="37" />
    <hkern u1="&#x26;" u2="^" k="37" />
    <hkern u1="&#x26;" u2="]" k="19" />
    <hkern u1="&#x26;" u2="Z" k="19" />
    <hkern u1="&#x26;" u2="Y" k="56" />
    <hkern u1="&#x26;" u2="X" k="28" />
    <hkern u1="&#x26;" u2="W" k="37" />
    <hkern u1="&#x26;" u2="V" k="37" />
    <hkern u1="&#x26;" u2="U" k="9" />
    <hkern u1="&#x26;" u2="T" k="37" />
    <hkern u1="&#x26;" u2="R" k="9" />
    <hkern u1="&#x26;" u2="P" k="9" />
    <hkern u1="&#x26;" u2="N" k="9" />
    <hkern u1="&#x26;" u2="M" k="9" />
    <hkern u1="&#x26;" u2="K" k="9" />
    <hkern u1="&#x26;" u2="J" k="37" />
    <hkern u1="&#x26;" u2="I" k="9" />
    <hkern u1="&#x26;" u2="H" k="9" />
    <hkern u1="&#x26;" u2="F" k="9" />
    <hkern u1="&#x26;" u2="E" k="9" />
    <hkern u1="&#x26;" u2="D" k="9" />
    <hkern u1="&#x26;" u2="B" k="9" />
    <hkern u1="&#x26;" u2="A" k="47" />
    <hkern u1="&#x26;" u2="&#x3b;" k="-9" />
    <hkern u1="&#x26;" u2="&#x2a;" k="37" />
    <hkern u1="&#x26;" u2="&#x29;" k="19" />
    <hkern u1="&#x26;" u2="&#x27;" k="37" />
    <hkern u1="&#x26;" u2="&#x22;" k="37" />
    <hkern u1="&#x26;" u2="&#x3c0;" k="-19" />
    <hkern u1="&#x26;" u2="&#xbf;" k="19" />
    <hkern u1="&#x26;" u2="\" k="37" />
    <hkern u1="&#x26;" u2="&#x37;" k="37" />
    <hkern u1="&#x26;" u2="&#x34;" k="9" />
    <hkern u1="&#x26;" u2="&#x33;" k="28" />
    <hkern u1="&#x26;" u2="&#x31;" k="19" />
    <hkern u1="&#x26;" u2="&#x2f;" k="19" />
    <hkern u1="&#x27;" u2="&#x192;" k="56" />
    <hkern u1="&#x27;" u2="&#xbf;" k="112" />
    <hkern u1="&#x27;" u2="&#xa3;" k="37" />
    <hkern u1="&#x27;" u2="&#x34;" k="74" />
    <hkern u1="&#x27;" u2="&#x2f;" k="74" />
    <hkern u1="&#x28;" u2="&#x221e;" k="19" />
    <hkern u1="&#x28;" u2="&#x192;" k="19" />
    <hkern u1="&#x28;" u2="&#xa4;" k="19" />
    <hkern u1="&#x28;" u2="&#xa3;" k="19" />
    <hkern u1="&#x28;" u2="&#xa2;" k="19" />
    <hkern u1="&#x28;" u2="&#x3a;" k="19" />
    <hkern u1="&#x28;" u2="&#x34;" k="9" />
    <hkern u1="&#x28;" u2="&#x24;" k="19" />
    <hkern u1="&#x28;" u2="&#x23;" k="19" />
    <hkern u1="&#x2a;" u2="&#x192;" k="56" />
    <hkern u1="&#x2a;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2a;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2a;" u2="&#x34;" k="74" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2b;" u2="&#x192;" k="9" />
    <hkern u1="&#x2b;" u2="&#xbf;" k="19" />
    <hkern u1="&#x2b;" u2="\" k="28" />
    <hkern u1="&#x2b;" u2="&#x3f;" k="9" />
    <hkern u1="&#x2b;" u2="&#x37;" k="37" />
    <hkern u1="&#x2b;" u2="&#x33;" k="9" />
    <hkern u1="&#x2b;" u2="&#x2f;" k="28" />
    <hkern u1="&#x2c;" u2="&#xb6;" k="74" />
    <hkern u1="&#x2c;" u2="\" k="74" />
    <hkern u1="&#x2c;" u2="&#x3f;" k="74" />
    <hkern u1="&#x2c;" u2="&#x37;" k="93" />
    <hkern u1="&#x2c;" u2="&#x31;" k="37" />
    <hkern u1="&#x2c;" u2="&#x24;" k="9" />
    <hkern u1="&#x2d;" u2="&#x192;" k="19" />
    <hkern u1="&#x2d;" u2="&#xbf;" k="37" />
    <hkern u1="&#x2d;" u2="\" k="56" />
    <hkern u1="&#x2d;" u2="&#x3f;" k="19" />
    <hkern u1="&#x2d;" u2="&#x37;" k="74" />
    <hkern u1="&#x2d;" u2="&#x33;" k="19" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="56" />
    <hkern u1="&#x2e;" u2="&#xb6;" k="74" />
    <hkern u1="&#x2e;" u2="\" k="74" />
    <hkern u1="&#x2e;" u2="&#x3f;" k="74" />
    <hkern u1="&#x2e;" u2="&#x37;" k="93" />
    <hkern u1="&#x2e;" u2="&#x31;" k="37" />
    <hkern u1="&#x2e;" u2="&#x24;" k="9" />
    <hkern u1="&#x2f;" g2="nine.dnom" k="74" />
    <hkern u1="&#x2f;" g2="eight.dnom" k="74" />
    <hkern u1="&#x2f;" g2="seven.dnom" k="74" />
    <hkern u1="&#x2f;" g2="six.dnom" k="74" />
    <hkern u1="&#x2f;" g2="five.dnom" k="74" />
    <hkern u1="&#x2f;" g2="four.dnom" k="74" />
    <hkern u1="&#x2f;" g2="three.dnom" k="74" />
    <hkern u1="&#x2f;" g2="two.dnom" k="74" />
    <hkern u1="&#x2f;" g2="one.dnom" k="74" />
    <hkern u1="&#x2f;" g2="zero.dnom" k="74" />
    <hkern u1="&#x2f;" g2="pi.ss01" k="37" />
    <hkern u1="&#x2f;" g2="j.ss01" k="74" />
    <hkern u1="&#x2f;" g2="ae.ss01" k="149" />
    <hkern u1="&#x2f;" g2="atilde.ss01" k="112" />
    <hkern u1="&#x2f;" g2="aring.ss01" k="112" />
    <hkern u1="&#x2f;" g2="aogonek.ss01" k="112" />
    <hkern u1="&#x2f;" g2="amacron.ss01" k="112" />
    <hkern u1="&#x2f;" g2="agrave.ss01" k="112" />
    <hkern u1="&#x2f;" g2="adieresis.ss01" k="112" />
    <hkern u1="&#x2f;" g2="acircumflex.ss01" k="112" />
    <hkern u1="&#x2f;" g2="abreve.ss01" k="112" />
    <hkern u1="&#x2f;" g2="aacute.ss01" k="112" />
    <hkern u1="&#x2f;" g2="a.ss01" k="112" />
    <hkern u1="&#x2f;" g2="J.ss01" k="74" />
    <hkern u1="&#x2f;" g2="AE.ss01" k="149" />
    <hkern u1="&#x2f;" g2="Atilde.ss01" k="112" />
    <hkern u1="&#x2f;" g2="Aring.ss01" k="112" />
    <hkern u1="&#x2f;" g2="Aogonek.ss01" k="112" />
    <hkern u1="&#x2f;" g2="Amacron.ss01" k="112" />
    <hkern u1="&#x2f;" g2="Agrave.ss01" k="112" />
    <hkern u1="&#x2f;" g2="Adieresis.ss01" k="112" />
    <hkern u1="&#x2f;" g2="Acircumflex.ss01" k="112" />
    <hkern u1="&#x2f;" g2="Abreve.ss01" k="112" />
    <hkern u1="&#x2f;" g2="Aacute.ss01" k="112" />
    <hkern u1="&#x2f;" g2="A.ss01" k="112" />
    <hkern u1="&#x2f;" u2="&#x2265;" k="28" />
    <hkern u1="&#x2f;" u2="&#x2264;" k="28" />
    <hkern u1="&#x2f;" u2="&#x2260;" k="28" />
    <hkern u1="&#x2f;" u2="&#x2248;" k="28" />
    <hkern u1="&#x2f;" u2="&#x2219;" k="56" />
    <hkern u1="&#x2f;" u2="&#x2212;" k="56" />
    <hkern u1="&#x2f;" u2="&#x20ac;" k="19" />
    <hkern u1="&#x2f;" u2="&#x2089;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2088;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2087;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2086;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2085;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2084;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2083;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2082;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2081;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2080;" k="74" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="28" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="28" />
    <hkern u1="&#x2f;" u2="&#x2026;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2022;" k="56" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="74" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="56" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="56" />
    <hkern u1="&#x2f;" u2="&#x1ee3;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ee2;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ee1;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ee0;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1edf;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ede;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1edd;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1edc;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1edb;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1eda;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed9;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed8;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed7;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed6;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed5;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed4;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed3;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed2;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed1;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ed0;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ecf;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ece;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ecd;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1ecc;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1eb7;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eb6;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eb5;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eb4;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eb3;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eb2;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eb1;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eb0;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eaf;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eae;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ead;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eac;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eab;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1eaa;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea9;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea8;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea7;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea6;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea5;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea4;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea3;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea2;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea1;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1ea0;" k="112" />
    <hkern u1="&#x2f;" u2="&#x1e9e;" k="19" />
    <hkern u1="&#x2f;" u2="&#x326;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2db;" k="74" />
    <hkern u1="&#x2f;" u2="&#x219;" k="19" />
    <hkern u1="&#x2f;" u2="&#x218;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1a1;" k="19" />
    <hkern u1="&#x2f;" u2="&#x1a0;" k="19" />
    <hkern u1="&#x2f;" u2="&#x161;" k="19" />
    <hkern u1="&#x2f;" u2="&#x160;" k="19" />
    <hkern u1="&#x2f;" u2="&#x15f;" k="19" />
    <hkern u1="&#x2f;" u2="&#x15e;" k="19" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="19" />
    <hkern u1="&#x2f;" u2="&#x15a;" k="19" />
    <hkern u1="&#x2f;" u2="&#x153;" k="19" />
    <hkern u1="&#x2f;" u2="&#x152;" k="19" />
    <hkern u1="&#x2f;" u2="&#x151;" k="19" />
    <hkern u1="&#x2f;" u2="&#x150;" k="19" />
    <hkern u1="&#x2f;" u2="&#x14d;" k="19" />
    <hkern u1="&#x2f;" u2="&#x14c;" k="19" />
    <hkern u1="&#x2f;" u2="&#x123;" k="19" />
    <hkern u1="&#x2f;" u2="&#x122;" k="19" />
    <hkern u1="&#x2f;" u2="&#x11f;" k="19" />
    <hkern u1="&#x2f;" u2="&#x11e;" k="19" />
    <hkern u1="&#x2f;" u2="&#x10d;" k="19" />
    <hkern u1="&#x2f;" u2="&#x10c;" k="19" />
    <hkern u1="&#x2f;" u2="&#x107;" k="19" />
    <hkern u1="&#x2f;" u2="&#x106;" k="19" />
    <hkern u1="&#x2f;" u2="&#x105;" k="112" />
    <hkern u1="&#x2f;" u2="&#x104;" k="112" />
    <hkern u1="&#x2f;" u2="&#x103;" k="112" />
    <hkern u1="&#x2f;" u2="&#x102;" k="112" />
    <hkern u1="&#x2f;" u2="&#x101;" k="112" />
    <hkern u1="&#x2f;" u2="&#x100;" k="112" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="19" />
    <hkern u1="&#x2f;" u2="&#xf7;" k="28" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="19" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="19" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="19" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="19" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="19" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="19" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="149" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="112" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="112" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="112" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="112" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="112" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="112" />
    <hkern u1="&#x2f;" u2="&#xdf;" k="19" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="19" />
    <hkern u1="&#x2f;" u2="&#xd7;" k="28" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="19" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="19" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="19" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="19" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="19" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="19" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="149" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="112" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="112" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="112" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="112" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="112" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="112" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="28" />
    <hkern u1="&#x2f;" u2="&#xb8;" k="74" />
    <hkern u1="&#x2f;" u2="&#xb7;" k="56" />
    <hkern u1="&#x2f;" u2="&#xb1;" k="28" />
    <hkern u1="&#x2f;" u2="&#xad;" k="56" />
    <hkern u1="&#x2f;" u2="&#xac;" k="56" />
    <hkern u1="&#x2f;" u2="&#xab;" k="28" />
    <hkern u1="&#x2f;" u2="&#xa9;" k="19" />
    <hkern u1="&#x2f;" u2="&#xa7;" k="19" />
    <hkern u1="&#x2f;" u2="&#xa1;" k="37" />
    <hkern u1="&#x2f;" u2="&#x7e;" k="56" />
    <hkern u1="&#x2f;" u2="&#x7b;" k="56" />
    <hkern u1="&#x2f;" u2="s" k="19" />
    <hkern u1="&#x2f;" u2="q" k="19" />
    <hkern u1="&#x2f;" u2="o" k="19" />
    <hkern u1="&#x2f;" u2="j" k="74" />
    <hkern u1="&#x2f;" u2="g" k="19" />
    <hkern u1="&#x2f;" u2="c" k="19" />
    <hkern u1="&#x2f;" u2="a" k="112" />
    <hkern u1="&#x2f;" u2="_" k="74" />
    <hkern u1="&#x2f;" u2="S" k="19" />
    <hkern u1="&#x2f;" u2="Q" k="19" />
    <hkern u1="&#x2f;" u2="O" k="19" />
    <hkern u1="&#x2f;" u2="J" k="74" />
    <hkern u1="&#x2f;" u2="G" k="19" />
    <hkern u1="&#x2f;" u2="C" k="19" />
    <hkern u1="&#x2f;" u2="A" k="112" />
    <hkern u1="&#x2f;" u2="&#x40;" k="19" />
    <hkern u1="&#x2f;" u2="&#x3e;" k="28" />
    <hkern u1="&#x2f;" u2="&#x3d;" k="28" />
    <hkern u1="&#x2f;" u2="&#x3c;" k="56" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="37" />
    <hkern u1="&#x2f;" u2="&#x38;" k="19" />
    <hkern u1="&#x2f;" u2="&#x36;" k="19" />
    <hkern u1="&#x2f;" u2="&#x30;" k="19" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="56" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="74" />
    <hkern u1="&#x2f;" u2="&#x2b;" k="28" />
    <hkern u1="&#x2f;" u2="&#x26;" k="19" />
    <hkern u1="&#x2f;" u2="&#x221e;" k="37" />
    <hkern u1="&#x2f;" u2="&#x3c0;" k="37" />
    <hkern u1="&#x2f;" u2="&#x192;" k="74" />
    <hkern u1="&#x2f;" u2="&#xbf;" k="130" />
    <hkern u1="&#x2f;" u2="&#xa4;" k="47" />
    <hkern u1="&#x2f;" u2="&#xa3;" k="74" />
    <hkern u1="&#x2f;" u2="&#xa2;" k="37" />
    <hkern u1="&#x2f;" u2="\" k="-37" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="28" />
    <hkern u1="&#x2f;" u2="&#x39;" k="19" />
    <hkern u1="&#x2f;" u2="&#x34;" k="93" />
    <hkern u1="&#x2f;" u2="&#x33;" k="19" />
    <hkern u1="&#x2f;" u2="&#x32;" k="19" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2f;" u2="&#x24;" k="56" />
    <hkern u1="&#x2f;" u2="&#x23;" k="37" />
    <hkern u1="&#x30;" u2="\" k="19" />
    <hkern u1="&#x30;" u2="&#x2f;" k="19" />
    <hkern u1="&#x32;" g2="ae.ss01" k="37" />
    <hkern u1="&#x32;" g2="atilde.ss01" k="56" />
    <hkern u1="&#x32;" g2="aring.ss01" k="56" />
    <hkern u1="&#x32;" g2="aogonek.ss01" k="56" />
    <hkern u1="&#x32;" g2="amacron.ss01" k="56" />
    <hkern u1="&#x32;" g2="agrave.ss01" k="56" />
    <hkern u1="&#x32;" g2="adieresis.ss01" k="56" />
    <hkern u1="&#x32;" g2="acircumflex.ss01" k="56" />
    <hkern u1="&#x32;" g2="abreve.ss01" k="56" />
    <hkern u1="&#x32;" g2="aacute.ss01" k="56" />
    <hkern u1="&#x32;" g2="a.ss01" k="56" />
    <hkern u1="&#x32;" g2="AE.ss01" k="37" />
    <hkern u1="&#x32;" g2="Atilde.ss01" k="56" />
    <hkern u1="&#x32;" g2="Aring.ss01" k="56" />
    <hkern u1="&#x32;" g2="Aogonek.ss01" k="56" />
    <hkern u1="&#x32;" g2="Amacron.ss01" k="56" />
    <hkern u1="&#x32;" g2="Agrave.ss01" k="56" />
    <hkern u1="&#x32;" g2="Adieresis.ss01" k="56" />
    <hkern u1="&#x32;" g2="Acircumflex.ss01" k="56" />
    <hkern u1="&#x32;" g2="Abreve.ss01" k="56" />
    <hkern u1="&#x32;" g2="Aacute.ss01" k="56" />
    <hkern u1="&#x32;" g2="A.ss01" k="56" />
    <hkern u1="&#x32;" u2="&#x1ef9;" k="19" />
    <hkern u1="&#x32;" u2="&#x1ef8;" k="19" />
    <hkern u1="&#x32;" u2="&#x1ef7;" k="19" />
    <hkern u1="&#x32;" u2="&#x1ef6;" k="19" />
    <hkern u1="&#x32;" u2="&#x1ef5;" k="19" />
    <hkern u1="&#x32;" u2="&#x1ef4;" k="19" />
    <hkern u1="&#x32;" u2="&#x1ef3;" k="19" />
    <hkern u1="&#x32;" u2="&#x1ef2;" k="19" />
    <hkern u1="&#x32;" u2="&#x1eb7;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eb6;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eb5;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eb4;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eb3;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eb2;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eb1;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eb0;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eaf;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eae;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ead;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eac;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eab;" k="56" />
    <hkern u1="&#x32;" u2="&#x1eaa;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea9;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea8;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea7;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea6;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea5;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea4;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea3;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea2;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea1;" k="56" />
    <hkern u1="&#x32;" u2="&#x1ea0;" k="56" />
    <hkern u1="&#x32;" u2="&#x178;" k="19" />
    <hkern u1="&#x32;" u2="&#x105;" k="56" />
    <hkern u1="&#x32;" u2="&#x104;" k="56" />
    <hkern u1="&#x32;" u2="&#x103;" k="56" />
    <hkern u1="&#x32;" u2="&#x102;" k="56" />
    <hkern u1="&#x32;" u2="&#x101;" k="56" />
    <hkern u1="&#x32;" u2="&#x100;" k="56" />
    <hkern u1="&#x32;" u2="&#xff;" k="19" />
    <hkern u1="&#x32;" u2="&#xfd;" k="19" />
    <hkern u1="&#x32;" u2="&#xe6;" k="37" />
    <hkern u1="&#x32;" u2="&#xe5;" k="56" />
    <hkern u1="&#x32;" u2="&#xe4;" k="56" />
    <hkern u1="&#x32;" u2="&#xe3;" k="56" />
    <hkern u1="&#x32;" u2="&#xe2;" k="56" />
    <hkern u1="&#x32;" u2="&#xe1;" k="56" />
    <hkern u1="&#x32;" u2="&#xe0;" k="56" />
    <hkern u1="&#x32;" u2="&#xdd;" k="19" />
    <hkern u1="&#x32;" u2="&#xc6;" k="37" />
    <hkern u1="&#x32;" u2="&#xc5;" k="56" />
    <hkern u1="&#x32;" u2="&#xc4;" k="56" />
    <hkern u1="&#x32;" u2="&#xc3;" k="56" />
    <hkern u1="&#x32;" u2="&#xc2;" k="56" />
    <hkern u1="&#x32;" u2="&#xc1;" k="56" />
    <hkern u1="&#x32;" u2="&#xc0;" k="56" />
    <hkern u1="&#x32;" u2="&#xa5;" k="19" />
    <hkern u1="&#x32;" u2="y" k="19" />
    <hkern u1="&#x32;" u2="x" k="19" />
    <hkern u1="&#x32;" u2="w" k="9" />
    <hkern u1="&#x32;" u2="v" k="9" />
    <hkern u1="&#x32;" u2="a" k="56" />
    <hkern u1="&#x32;" u2="Y" k="19" />
    <hkern u1="&#x32;" u2="X" k="19" />
    <hkern u1="&#x32;" u2="W" k="9" />
    <hkern u1="&#x32;" u2="V" k="9" />
    <hkern u1="&#x32;" u2="A" k="56" />
    <hkern u1="&#x32;" u2="&#xa3;" k="9" />
    <hkern u1="&#x32;" u2="\" k="19" />
    <hkern u1="&#x32;" u2="&#x34;" k="19" />
    <hkern u1="&#x32;" u2="&#x2f;" k="37" />
    <hkern u1="&#x33;" u2="\" k="19" />
    <hkern u1="&#x33;" u2="&#x2f;" k="19" />
    <hkern u1="&#x34;" g2="j.ss01" k="19" />
    <hkern u1="&#x34;" g2="ae.ss01" k="19" />
    <hkern u1="&#x34;" g2="atilde.ss01" k="19" />
    <hkern u1="&#x34;" g2="aring.ss01" k="19" />
    <hkern u1="&#x34;" g2="aogonek.ss01" k="19" />
    <hkern u1="&#x34;" g2="amacron.ss01" k="19" />
    <hkern u1="&#x34;" g2="agrave.ss01" k="19" />
    <hkern u1="&#x34;" g2="adieresis.ss01" k="19" />
    <hkern u1="&#x34;" g2="acircumflex.ss01" k="19" />
    <hkern u1="&#x34;" g2="abreve.ss01" k="19" />
    <hkern u1="&#x34;" g2="aacute.ss01" k="19" />
    <hkern u1="&#x34;" g2="a.ss01" k="19" />
    <hkern u1="&#x34;" g2="J.ss01" k="19" />
    <hkern u1="&#x34;" g2="AE.ss01" k="19" />
    <hkern u1="&#x34;" g2="Atilde.ss01" k="19" />
    <hkern u1="&#x34;" g2="Aring.ss01" k="19" />
    <hkern u1="&#x34;" g2="Aogonek.ss01" k="19" />
    <hkern u1="&#x34;" g2="Amacron.ss01" k="19" />
    <hkern u1="&#x34;" g2="Agrave.ss01" k="19" />
    <hkern u1="&#x34;" g2="Adieresis.ss01" k="19" />
    <hkern u1="&#x34;" g2="Acircumflex.ss01" k="19" />
    <hkern u1="&#x34;" g2="Abreve.ss01" k="19" />
    <hkern u1="&#x34;" g2="Aacute.ss01" k="19" />
    <hkern u1="&#x34;" g2="A.ss01" k="19" />
    <hkern u1="&#x34;" u2="&#x1ef9;" k="28" />
    <hkern u1="&#x34;" u2="&#x1ef8;" k="28" />
    <hkern u1="&#x34;" u2="&#x1ef7;" k="28" />
    <hkern u1="&#x34;" u2="&#x1ef6;" k="28" />
    <hkern u1="&#x34;" u2="&#x1ef5;" k="28" />
    <hkern u1="&#x34;" u2="&#x1ef4;" k="28" />
    <hkern u1="&#x34;" u2="&#x1ef3;" k="28" />
    <hkern u1="&#x34;" u2="&#x1ef2;" k="28" />
    <hkern u1="&#x34;" u2="&#x1eb7;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eb6;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eb5;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eb4;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eb3;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eb2;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eb1;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eb0;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eaf;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eae;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ead;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eac;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eab;" k="19" />
    <hkern u1="&#x34;" u2="&#x1eaa;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea9;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea8;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea7;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea6;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea5;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea4;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea3;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea2;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea1;" k="19" />
    <hkern u1="&#x34;" u2="&#x1ea0;" k="19" />
    <hkern u1="&#x34;" u2="&#x21b;" k="9" />
    <hkern u1="&#x34;" u2="&#x21a;" k="9" />
    <hkern u1="&#x34;" u2="&#x219;" k="19" />
    <hkern u1="&#x34;" u2="&#x218;" k="19" />
    <hkern u1="&#x34;" u2="&#x178;" k="28" />
    <hkern u1="&#x34;" u2="&#x165;" k="9" />
    <hkern u1="&#x34;" u2="&#x164;" k="9" />
    <hkern u1="&#x34;" u2="&#x163;" k="9" />
    <hkern u1="&#x34;" u2="&#x162;" k="9" />
    <hkern u1="&#x34;" u2="&#x161;" k="19" />
    <hkern u1="&#x34;" u2="&#x160;" k="19" />
    <hkern u1="&#x34;" u2="&#x15f;" k="19" />
    <hkern u1="&#x34;" u2="&#x15e;" k="19" />
    <hkern u1="&#x34;" u2="&#x15b;" k="19" />
    <hkern u1="&#x34;" u2="&#x15a;" k="19" />
    <hkern u1="&#x34;" u2="&#x105;" k="19" />
    <hkern u1="&#x34;" u2="&#x104;" k="19" />
    <hkern u1="&#x34;" u2="&#x103;" k="19" />
    <hkern u1="&#x34;" u2="&#x102;" k="19" />
    <hkern u1="&#x34;" u2="&#x101;" k="19" />
    <hkern u1="&#x34;" u2="&#x100;" k="19" />
    <hkern u1="&#x34;" u2="&#xff;" k="28" />
    <hkern u1="&#x34;" u2="&#xfd;" k="28" />
    <hkern u1="&#x34;" u2="&#xe6;" k="19" />
    <hkern u1="&#x34;" u2="&#xe5;" k="19" />
    <hkern u1="&#x34;" u2="&#xe4;" k="19" />
    <hkern u1="&#x34;" u2="&#xe3;" k="19" />
    <hkern u1="&#x34;" u2="&#xe2;" k="19" />
    <hkern u1="&#x34;" u2="&#xe1;" k="19" />
    <hkern u1="&#x34;" u2="&#xe0;" k="19" />
    <hkern u1="&#x34;" u2="&#xdd;" k="28" />
    <hkern u1="&#x34;" u2="&#xc6;" k="19" />
    <hkern u1="&#x34;" u2="&#xc5;" k="19" />
    <hkern u1="&#x34;" u2="&#xc4;" k="19" />
    <hkern u1="&#x34;" u2="&#xc3;" k="19" />
    <hkern u1="&#x34;" u2="&#xc2;" k="19" />
    <hkern u1="&#x34;" u2="&#xc1;" k="19" />
    <hkern u1="&#x34;" u2="&#xc0;" k="19" />
    <hkern u1="&#x34;" u2="&#xa7;" k="19" />
    <hkern u1="&#x34;" u2="&#xa5;" k="28" />
    <hkern u1="&#x34;" u2="&#x7d;" k="19" />
    <hkern u1="&#x34;" u2="y" k="28" />
    <hkern u1="&#x34;" u2="x" k="9" />
    <hkern u1="&#x34;" u2="w" k="19" />
    <hkern u1="&#x34;" u2="v" k="19" />
    <hkern u1="&#x34;" u2="t" k="9" />
    <hkern u1="&#x34;" u2="s" k="19" />
    <hkern u1="&#x34;" u2="j" k="19" />
    <hkern u1="&#x34;" u2="a" k="19" />
    <hkern u1="&#x34;" u2="]" k="19" />
    <hkern u1="&#x34;" u2="Y" k="28" />
    <hkern u1="&#x34;" u2="X" k="9" />
    <hkern u1="&#x34;" u2="W" k="19" />
    <hkern u1="&#x34;" u2="V" k="19" />
    <hkern u1="&#x34;" u2="T" k="9" />
    <hkern u1="&#x34;" u2="S" k="19" />
    <hkern u1="&#x34;" u2="J" k="19" />
    <hkern u1="&#x34;" u2="A" k="19" />
    <hkern u1="&#x34;" u2="&#x29;" k="19" />
    <hkern u1="&#x34;" u2="\" k="19" />
    <hkern u1="&#x34;" u2="&#x2f;" k="19" />
    <hkern u1="&#x34;" u2="&#x24;" k="19" />
    <hkern u1="&#x35;" u2="&#x3f;" k="19" />
    <hkern u1="&#x35;" u2="&#x37;" k="19" />
    <hkern u1="&#x35;" u2="&#x31;" k="9" />
    <hkern u1="&#x35;" u2="&#x2f;" k="19" />
    <hkern u1="&#x35;" u2="&#x24;" k="9" />
    <hkern u1="&#x36;" u2="&#x3f;" k="19" />
    <hkern u1="&#x36;" u2="&#x37;" k="19" />
    <hkern u1="&#x36;" u2="&#x31;" k="9" />
    <hkern u1="&#x36;" u2="&#x2f;" k="19" />
    <hkern u1="&#x36;" u2="&#x24;" k="9" />
    <hkern u1="&#x37;" g2="nine.dnom" k="37" />
    <hkern u1="&#x37;" g2="eight.dnom" k="37" />
    <hkern u1="&#x37;" g2="seven.dnom" k="37" />
    <hkern u1="&#x37;" g2="six.dnom" k="37" />
    <hkern u1="&#x37;" g2="five.dnom" k="37" />
    <hkern u1="&#x37;" g2="four.dnom" k="37" />
    <hkern u1="&#x37;" g2="three.dnom" k="37" />
    <hkern u1="&#x37;" g2="two.dnom" k="37" />
    <hkern u1="&#x37;" g2="one.dnom" k="37" />
    <hkern u1="&#x37;" g2="zero.dnom" k="37" />
    <hkern u1="&#x37;" g2="j.ss01" k="65" />
    <hkern u1="&#x37;" g2="ae.ss01" k="112" />
    <hkern u1="&#x37;" g2="atilde.ss01" k="93" />
    <hkern u1="&#x37;" g2="aring.ss01" k="93" />
    <hkern u1="&#x37;" g2="aogonek.ss01" k="93" />
    <hkern u1="&#x37;" g2="amacron.ss01" k="93" />
    <hkern u1="&#x37;" g2="agrave.ss01" k="93" />
    <hkern u1="&#x37;" g2="adieresis.ss01" k="93" />
    <hkern u1="&#x37;" g2="acircumflex.ss01" k="93" />
    <hkern u1="&#x37;" g2="abreve.ss01" k="93" />
    <hkern u1="&#x37;" g2="aacute.ss01" k="93" />
    <hkern u1="&#x37;" g2="a.ss01" k="93" />
    <hkern u1="&#x37;" g2="J.ss01" k="65" />
    <hkern u1="&#x37;" g2="AE.ss01" k="112" />
    <hkern u1="&#x37;" g2="Atilde.ss01" k="93" />
    <hkern u1="&#x37;" g2="Aring.ss01" k="93" />
    <hkern u1="&#x37;" g2="Aogonek.ss01" k="93" />
    <hkern u1="&#x37;" g2="Amacron.ss01" k="93" />
    <hkern u1="&#x37;" g2="Agrave.ss01" k="93" />
    <hkern u1="&#x37;" g2="Adieresis.ss01" k="93" />
    <hkern u1="&#x37;" g2="Acircumflex.ss01" k="93" />
    <hkern u1="&#x37;" g2="Abreve.ss01" k="93" />
    <hkern u1="&#x37;" g2="Aacute.ss01" k="93" />
    <hkern u1="&#x37;" g2="A.ss01" k="93" />
    <hkern u1="&#x37;" u2="&#x2089;" k="37" />
    <hkern u1="&#x37;" u2="&#x2088;" k="37" />
    <hkern u1="&#x37;" u2="&#x2087;" k="37" />
    <hkern u1="&#x37;" u2="&#x2086;" k="37" />
    <hkern u1="&#x37;" u2="&#x2085;" k="37" />
    <hkern u1="&#x37;" u2="&#x2084;" k="37" />
    <hkern u1="&#x37;" u2="&#x2083;" k="37" />
    <hkern u1="&#x37;" u2="&#x2082;" k="37" />
    <hkern u1="&#x37;" u2="&#x2081;" k="37" />
    <hkern u1="&#x37;" u2="&#x2080;" k="37" />
    <hkern u1="&#x37;" u2="&#x2026;" k="93" />
    <hkern u1="&#x37;" u2="&#x201e;" k="93" />
    <hkern u1="&#x37;" u2="&#x201a;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eb7;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eb6;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eb5;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eb4;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eb3;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eb2;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eb1;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eb0;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eaf;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eae;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ead;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eac;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eab;" k="93" />
    <hkern u1="&#x37;" u2="&#x1eaa;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea9;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea8;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea7;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea6;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea5;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea4;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea3;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea2;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea1;" k="93" />
    <hkern u1="&#x37;" u2="&#x1ea0;" k="93" />
    <hkern u1="&#x37;" u2="&#x326;" k="93" />
    <hkern u1="&#x37;" u2="&#x2db;" k="93" />
    <hkern u1="&#x37;" u2="&#x105;" k="93" />
    <hkern u1="&#x37;" u2="&#x104;" k="93" />
    <hkern u1="&#x37;" u2="&#x103;" k="93" />
    <hkern u1="&#x37;" u2="&#x102;" k="93" />
    <hkern u1="&#x37;" u2="&#x101;" k="93" />
    <hkern u1="&#x37;" u2="&#x100;" k="93" />
    <hkern u1="&#x37;" u2="&#xe6;" k="112" />
    <hkern u1="&#x37;" u2="&#xe5;" k="93" />
    <hkern u1="&#x37;" u2="&#xe4;" k="93" />
    <hkern u1="&#x37;" u2="&#xe3;" k="93" />
    <hkern u1="&#x37;" u2="&#xe2;" k="93" />
    <hkern u1="&#x37;" u2="&#xe1;" k="93" />
    <hkern u1="&#x37;" u2="&#xe0;" k="93" />
    <hkern u1="&#x37;" u2="&#xc6;" k="112" />
    <hkern u1="&#x37;" u2="&#xc5;" k="93" />
    <hkern u1="&#x37;" u2="&#xc4;" k="93" />
    <hkern u1="&#x37;" u2="&#xc3;" k="93" />
    <hkern u1="&#x37;" u2="&#xc2;" k="93" />
    <hkern u1="&#x37;" u2="&#xc1;" k="93" />
    <hkern u1="&#x37;" u2="&#xc0;" k="93" />
    <hkern u1="&#x37;" u2="&#xb8;" k="93" />
    <hkern u1="&#x37;" u2="j" k="65" />
    <hkern u1="&#x37;" u2="a" k="93" />
    <hkern u1="&#x37;" u2="_" k="93" />
    <hkern u1="&#x37;" u2="J" k="65" />
    <hkern u1="&#x37;" u2="A" k="93" />
    <hkern u1="&#x37;" u2="&#x2e;" k="93" />
    <hkern u1="&#x37;" u2="&#x2c;" k="93" />
    <hkern u1="&#x37;" u2="&#x221e;" k="9" />
    <hkern u1="&#x37;" u2="&#x192;" k="37" />
    <hkern u1="&#x37;" u2="&#xbf;" k="112" />
    <hkern u1="&#x37;" u2="&#xa3;" k="47" />
    <hkern u1="&#x37;" u2="&#x34;" k="84" />
    <hkern u1="&#x37;" u2="&#x2f;" k="112" />
    <hkern u1="&#x37;" u2="&#x23;" k="19" />
    <hkern u1="&#x38;" u2="\" k="19" />
    <hkern u1="&#x38;" u2="&#x2f;" k="19" />
    <hkern u1="&#x39;" u2="\" k="19" />
    <hkern u1="&#x39;" u2="&#x2f;" k="19" />
    <hkern u1="&#x3a;" g2="ae.ss01" k="19" />
    <hkern u1="&#x3a;" g2="atilde.ss01" k="28" />
    <hkern u1="&#x3a;" g2="aring.ss01" k="28" />
    <hkern u1="&#x3a;" g2="aogonek.ss01" k="28" />
    <hkern u1="&#x3a;" g2="amacron.ss01" k="28" />
    <hkern u1="&#x3a;" g2="agrave.ss01" k="28" />
    <hkern u1="&#x3a;" g2="adieresis.ss01" k="28" />
    <hkern u1="&#x3a;" g2="acircumflex.ss01" k="28" />
    <hkern u1="&#x3a;" g2="abreve.ss01" k="28" />
    <hkern u1="&#x3a;" g2="aacute.ss01" k="28" />
    <hkern u1="&#x3a;" g2="a.ss01" k="28" />
    <hkern u1="&#x3a;" g2="AE.ss01" k="19" />
    <hkern u1="&#x3a;" g2="Atilde.ss01" k="28" />
    <hkern u1="&#x3a;" g2="Aring.ss01" k="28" />
    <hkern u1="&#x3a;" g2="Aogonek.ss01" k="28" />
    <hkern u1="&#x3a;" g2="Amacron.ss01" k="28" />
    <hkern u1="&#x3a;" g2="Agrave.ss01" k="28" />
    <hkern u1="&#x3a;" g2="Adieresis.ss01" k="28" />
    <hkern u1="&#x3a;" g2="Acircumflex.ss01" k="28" />
    <hkern u1="&#x3a;" g2="Abreve.ss01" k="28" />
    <hkern u1="&#x3a;" g2="Aacute.ss01" k="28" />
    <hkern u1="&#x3a;" g2="A.ss01" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ef9;" k="47" />
    <hkern u1="&#x3a;" u2="&#x1ef8;" k="47" />
    <hkern u1="&#x3a;" u2="&#x1ef7;" k="47" />
    <hkern u1="&#x3a;" u2="&#x1ef6;" k="47" />
    <hkern u1="&#x3a;" u2="&#x1ef5;" k="47" />
    <hkern u1="&#x3a;" u2="&#x1ef4;" k="47" />
    <hkern u1="&#x3a;" u2="&#x1ef3;" k="47" />
    <hkern u1="&#x3a;" u2="&#x1ef2;" k="47" />
    <hkern u1="&#x3a;" u2="&#x1eb7;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eb6;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eb5;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eb4;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eb3;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eb2;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eb1;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eb0;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eaf;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eae;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ead;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eac;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eab;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1eaa;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea9;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea8;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea7;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea6;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea5;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea4;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea3;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea2;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea1;" k="28" />
    <hkern u1="&#x3a;" u2="&#x1ea0;" k="28" />
    <hkern u1="&#x3a;" u2="&#x21b;" k="37" />
    <hkern u1="&#x3a;" u2="&#x21a;" k="37" />
    <hkern u1="&#x3a;" u2="&#x17e;" k="19" />
    <hkern u1="&#x3a;" u2="&#x17d;" k="19" />
    <hkern u1="&#x3a;" u2="&#x17c;" k="19" />
    <hkern u1="&#x3a;" u2="&#x17b;" k="19" />
    <hkern u1="&#x3a;" u2="&#x17a;" k="19" />
    <hkern u1="&#x3a;" u2="&#x179;" k="19" />
    <hkern u1="&#x3a;" u2="&#x178;" k="47" />
    <hkern u1="&#x3a;" u2="&#x165;" k="37" />
    <hkern u1="&#x3a;" u2="&#x164;" k="37" />
    <hkern u1="&#x3a;" u2="&#x163;" k="37" />
    <hkern u1="&#x3a;" u2="&#x162;" k="37" />
    <hkern u1="&#x3a;" u2="&#x105;" k="28" />
    <hkern u1="&#x3a;" u2="&#x104;" k="28" />
    <hkern u1="&#x3a;" u2="&#x103;" k="28" />
    <hkern u1="&#x3a;" u2="&#x102;" k="28" />
    <hkern u1="&#x3a;" u2="&#x101;" k="28" />
    <hkern u1="&#x3a;" u2="&#x100;" k="28" />
    <hkern u1="&#x3a;" u2="&#xff;" k="47" />
    <hkern u1="&#x3a;" u2="&#xfd;" k="47" />
    <hkern u1="&#x3a;" u2="&#xe6;" k="19" />
    <hkern u1="&#x3a;" u2="&#xe5;" k="28" />
    <hkern u1="&#x3a;" u2="&#xe4;" k="28" />
    <hkern u1="&#x3a;" u2="&#xe3;" k="28" />
    <hkern u1="&#x3a;" u2="&#xe2;" k="28" />
    <hkern u1="&#x3a;" u2="&#xe1;" k="28" />
    <hkern u1="&#x3a;" u2="&#xe0;" k="28" />
    <hkern u1="&#x3a;" u2="&#xdd;" k="47" />
    <hkern u1="&#x3a;" u2="&#xc6;" k="19" />
    <hkern u1="&#x3a;" u2="&#xc5;" k="28" />
    <hkern u1="&#x3a;" u2="&#xc4;" k="28" />
    <hkern u1="&#x3a;" u2="&#xc3;" k="28" />
    <hkern u1="&#x3a;" u2="&#xc2;" k="28" />
    <hkern u1="&#x3a;" u2="&#xc1;" k="28" />
    <hkern u1="&#x3a;" u2="&#xc0;" k="28" />
    <hkern u1="&#x3a;" u2="&#xa5;" k="47" />
    <hkern u1="&#x3a;" u2="&#x7d;" k="19" />
    <hkern u1="&#x3a;" u2="z" k="19" />
    <hkern u1="&#x3a;" u2="y" k="47" />
    <hkern u1="&#x3a;" u2="x" k="28" />
    <hkern u1="&#x3a;" u2="w" k="28" />
    <hkern u1="&#x3a;" u2="v" k="28" />
    <hkern u1="&#x3a;" u2="t" k="37" />
    <hkern u1="&#x3a;" u2="a" k="28" />
    <hkern u1="&#x3a;" u2="]" k="19" />
    <hkern u1="&#x3a;" u2="Z" k="19" />
    <hkern u1="&#x3a;" u2="Y" k="47" />
    <hkern u1="&#x3a;" u2="X" k="28" />
    <hkern u1="&#x3a;" u2="W" k="28" />
    <hkern u1="&#x3a;" u2="V" k="28" />
    <hkern u1="&#x3a;" u2="T" k="37" />
    <hkern u1="&#x3a;" u2="A" k="28" />
    <hkern u1="&#x3a;" u2="&#x29;" k="19" />
    <hkern u1="&#x3a;" u2="\" k="28" />
    <hkern u1="&#x3a;" u2="&#x37;" k="37" />
    <hkern u1="&#x3a;" u2="&#x2f;" k="28" />
    <hkern u1="&#x3b;" u2="\" k="37" />
    <hkern u1="&#x3b;" u2="&#x37;" k="56" />
    <hkern u1="&#x3b;" u2="&#x2f;" k="-37" />
    <hkern u1="&#x3c;" u2="&#x192;" k="9" />
    <hkern u1="&#x3c;" u2="&#xbf;" k="19" />
    <hkern u1="&#x3c;" u2="\" k="28" />
    <hkern u1="&#x3c;" u2="&#x3f;" k="9" />
    <hkern u1="&#x3c;" u2="&#x37;" k="37" />
    <hkern u1="&#x3c;" u2="&#x33;" k="9" />
    <hkern u1="&#x3c;" u2="&#x2f;" k="28" />
    <hkern u1="&#x3d;" u2="&#x192;" k="9" />
    <hkern u1="&#x3d;" u2="&#xbf;" k="19" />
    <hkern u1="&#x3d;" u2="\" k="28" />
    <hkern u1="&#x3d;" u2="&#x3f;" k="9" />
    <hkern u1="&#x3d;" u2="&#x37;" k="37" />
    <hkern u1="&#x3d;" u2="&#x33;" k="9" />
    <hkern u1="&#x3d;" u2="&#x2f;" k="28" />
    <hkern u1="&#x3e;" u2="&#x192;" k="19" />
    <hkern u1="&#x3e;" u2="&#xbf;" k="37" />
    <hkern u1="&#x3e;" u2="\" k="56" />
    <hkern u1="&#x3e;" u2="&#x3f;" k="19" />
    <hkern u1="&#x3e;" u2="&#x37;" k="74" />
    <hkern u1="&#x3e;" u2="&#x33;" k="19" />
    <hkern u1="&#x3e;" u2="&#x2f;" k="56" />
    <hkern u1="&#x3f;" g2="j.ss01" k="56" />
    <hkern u1="&#x3f;" g2="ae.ss01" k="112" />
    <hkern u1="&#x3f;" g2="atilde.ss01" k="65" />
    <hkern u1="&#x3f;" g2="aring.ss01" k="65" />
    <hkern u1="&#x3f;" g2="aogonek.ss01" k="65" />
    <hkern u1="&#x3f;" g2="amacron.ss01" k="65" />
    <hkern u1="&#x3f;" g2="agrave.ss01" k="65" />
    <hkern u1="&#x3f;" g2="adieresis.ss01" k="65" />
    <hkern u1="&#x3f;" g2="acircumflex.ss01" k="65" />
    <hkern u1="&#x3f;" g2="abreve.ss01" k="65" />
    <hkern u1="&#x3f;" g2="aacute.ss01" k="65" />
    <hkern u1="&#x3f;" g2="a.ss01" k="65" />
    <hkern u1="&#x3f;" g2="J.ss01" k="56" />
    <hkern u1="&#x3f;" g2="AE.ss01" k="112" />
    <hkern u1="&#x3f;" g2="Atilde.ss01" k="65" />
    <hkern u1="&#x3f;" g2="Aring.ss01" k="65" />
    <hkern u1="&#x3f;" g2="Aogonek.ss01" k="65" />
    <hkern u1="&#x3f;" g2="Amacron.ss01" k="65" />
    <hkern u1="&#x3f;" g2="Agrave.ss01" k="65" />
    <hkern u1="&#x3f;" g2="Adieresis.ss01" k="65" />
    <hkern u1="&#x3f;" g2="Acircumflex.ss01" k="65" />
    <hkern u1="&#x3f;" g2="Abreve.ss01" k="65" />
    <hkern u1="&#x3f;" g2="Aacute.ss01" k="65" />
    <hkern u1="&#x3f;" g2="A.ss01" k="65" />
    <hkern u1="&#x3f;" u2="&#x2026;" k="74" />
    <hkern u1="&#x3f;" u2="&#x201e;" k="74" />
    <hkern u1="&#x3f;" u2="&#x201a;" k="74" />
    <hkern u1="&#x3f;" u2="&#x1eb7;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eb6;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eb5;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eb4;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eb3;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eb2;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eb1;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eb0;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eaf;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eae;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ead;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eac;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eab;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1eaa;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea9;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea8;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea7;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea6;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea5;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea4;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea3;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea2;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea1;" k="65" />
    <hkern u1="&#x3f;" u2="&#x1ea0;" k="65" />
    <hkern u1="&#x3f;" u2="&#x326;" k="74" />
    <hkern u1="&#x3f;" u2="&#x2db;" k="74" />
    <hkern u1="&#x3f;" u2="&#x105;" k="65" />
    <hkern u1="&#x3f;" u2="&#x104;" k="65" />
    <hkern u1="&#x3f;" u2="&#x103;" k="65" />
    <hkern u1="&#x3f;" u2="&#x102;" k="65" />
    <hkern u1="&#x3f;" u2="&#x101;" k="65" />
    <hkern u1="&#x3f;" u2="&#x100;" k="65" />
    <hkern u1="&#x3f;" u2="&#xe6;" k="112" />
    <hkern u1="&#x3f;" u2="&#xe5;" k="65" />
    <hkern u1="&#x3f;" u2="&#xe4;" k="65" />
    <hkern u1="&#x3f;" u2="&#xe3;" k="65" />
    <hkern u1="&#x3f;" u2="&#xe2;" k="65" />
    <hkern u1="&#x3f;" u2="&#xe1;" k="65" />
    <hkern u1="&#x3f;" u2="&#xe0;" k="65" />
    <hkern u1="&#x3f;" u2="&#xc6;" k="112" />
    <hkern u1="&#x3f;" u2="&#xc5;" k="65" />
    <hkern u1="&#x3f;" u2="&#xc4;" k="65" />
    <hkern u1="&#x3f;" u2="&#xc3;" k="65" />
    <hkern u1="&#x3f;" u2="&#xc2;" k="65" />
    <hkern u1="&#x3f;" u2="&#xc1;" k="65" />
    <hkern u1="&#x3f;" u2="&#xc0;" k="65" />
    <hkern u1="&#x3f;" u2="&#xb8;" k="74" />
    <hkern u1="&#x3f;" u2="x" k="9" />
    <hkern u1="&#x3f;" u2="j" k="56" />
    <hkern u1="&#x3f;" u2="a" k="65" />
    <hkern u1="&#x3f;" u2="_" k="74" />
    <hkern u1="&#x3f;" u2="X" k="9" />
    <hkern u1="&#x3f;" u2="J" k="56" />
    <hkern u1="&#x3f;" u2="A" k="65" />
    <hkern u1="&#x3f;" u2="&#x2e;" k="74" />
    <hkern u1="&#x3f;" u2="&#x2c;" k="74" />
    <hkern u1="&#x3f;" u2="&#xbf;" k="112" />
    <hkern u1="&#x3f;" u2="&#xa4;" k="-19" />
    <hkern u1="&#x3f;" u2="&#x2f;" k="93" />
    <hkern u1="&#x40;" u2="\" k="19" />
    <hkern u1="&#x40;" u2="&#x2f;" k="19" />
    <hkern u1="A" u2="&#x221e;" k="37" />
    <hkern u1="A" u2="&#x192;" k="37" />
    <hkern u1="A" u2="&#xb6;" k="65" />
    <hkern u1="A" u2="&#xa4;" k="56" />
    <hkern u1="A" u2="&#xa3;" k="19" />
    <hkern u1="A" u2="&#xa2;" k="47" />
    <hkern u1="A" u2="\" k="112" />
    <hkern u1="A" u2="&#x3f;" k="84" />
    <hkern u1="A" u2="&#x3a;" k="28" />
    <hkern u1="A" u2="&#x37;" k="112" />
    <hkern u1="A" u2="&#x34;" k="19" />
    <hkern u1="A" u2="&#x24;" k="74" />
    <hkern u1="A" u2="&#x23;" k="37" />
    <hkern u1="B" u2="\" k="19" />
    <hkern u1="B" u2="&#x2f;" k="19" />
    <hkern u1="C" u2="\" k="19" />
    <hkern u1="C" u2="&#x2f;" k="19" />
    <hkern u1="D" u2="\" k="19" />
    <hkern u1="D" u2="&#x2f;" k="19" />
    <hkern u1="E" u2="&#x221e;" k="19" />
    <hkern u1="E" u2="&#x192;" k="19" />
    <hkern u1="E" u2="&#xb6;" k="19" />
    <hkern u1="E" u2="&#xa4;" k="28" />
    <hkern u1="E" u2="&#xa2;" k="19" />
    <hkern u1="E" u2="&#x3a;" k="19" />
    <hkern u1="E" u2="&#x24;" k="19" />
    <hkern u1="E" u2="&#x23;" k="19" />
    <hkern u1="F" u2="&#x221e;" k="47" />
    <hkern u1="F" u2="&#x3c0;" k="56" />
    <hkern u1="F" u2="&#x192;" k="74" />
    <hkern u1="F" u2="&#xbf;" k="186" />
    <hkern u1="F" u2="&#xb6;" k="28" />
    <hkern u1="F" u2="&#xa4;" k="56" />
    <hkern u1="F" u2="&#xa3;" k="74" />
    <hkern u1="F" u2="&#xa2;" k="47" />
    <hkern u1="F" u2="&#x39;" k="28" />
    <hkern u1="F" u2="&#x37;" k="19" />
    <hkern u1="F" u2="&#x34;" k="93" />
    <hkern u1="F" u2="&#x33;" k="28" />
    <hkern u1="F" u2="&#x32;" k="28" />
    <hkern u1="F" u2="&#x2f;" k="112" />
    <hkern u1="F" u2="&#x24;" k="47" />
    <hkern u1="F" u2="&#x23;" k="47" />
    <hkern u1="G" u2="&#x3f;" k="19" />
    <hkern u1="G" u2="&#x32;" k="-9" />
    <hkern u1="G" u2="&#x31;" k="19" />
    <hkern u1="J" u2="&#x2f;" k="19" />
    <hkern u1="K" u2="&#x221e;" k="47" />
    <hkern u1="K" u2="&#x192;" k="37" />
    <hkern u1="K" u2="&#xb6;" k="9" />
    <hkern u1="K" u2="&#xa4;" k="65" />
    <hkern u1="K" u2="&#xa3;" k="19" />
    <hkern u1="K" u2="&#xa2;" k="47" />
    <hkern u1="K" u2="&#x3a;" k="28" />
    <hkern u1="K" u2="&#x39;" k="19" />
    <hkern u1="K" u2="&#x34;" k="37" />
    <hkern u1="K" u2="&#x33;" k="19" />
    <hkern u1="K" u2="&#x32;" k="9" />
    <hkern u1="K" u2="&#x24;" k="56" />
    <hkern u1="K" u2="&#x23;" k="56" />
    <hkern u1="L" u2="&#x221e;" k="19" />
    <hkern u1="L" u2="&#xb6;" k="102" />
    <hkern u1="L" u2="&#xa4;" k="47" />
    <hkern u1="L" u2="&#xa2;" k="19" />
    <hkern u1="L" u2="\" k="140" />
    <hkern u1="L" u2="&#x3f;" k="140" />
    <hkern u1="L" u2="&#x37;" k="140" />
    <hkern u1="L" u2="&#x31;" k="74" />
    <hkern u1="L" u2="&#x24;" k="19" />
    <hkern u1="O" u2="\" k="19" />
    <hkern u1="O" u2="&#x2f;" k="19" />
    <hkern u1="P" u2="&#xbf;" k="74" />
    <hkern u1="P" u2="&#x2f;" k="74" />
    <hkern u1="Q" u2="\" k="19" />
    <hkern u1="Q" u2="&#x2f;" k="19" />
    <hkern u1="R" u2="&#x3c0;" k="-19" />
    <hkern u1="R" u2="\" k="19" />
    <hkern u1="S" u2="\" k="19" />
    <hkern u1="S" u2="&#x2f;" k="19" />
    <hkern u1="T" u2="&#x221e;" k="19" />
    <hkern u1="T" u2="&#x3c0;" k="47" />
    <hkern u1="T" u2="&#x192;" k="74" />
    <hkern u1="T" u2="&#xbf;" k="112" />
    <hkern u1="T" u2="&#xa4;" k="28" />
    <hkern u1="T" u2="&#xa3;" k="56" />
    <hkern u1="T" u2="&#xa2;" k="19" />
    <hkern u1="T" u2="&#x3a;" k="37" />
    <hkern u1="T" u2="&#x34;" k="65" />
    <hkern u1="T" u2="&#x2f;" k="93" />
    <hkern u1="T" u2="&#x24;" k="9" />
    <hkern u1="T" u2="&#x23;" k="19" />
    <hkern u1="V" u2="&#x221e;" k="37" />
    <hkern u1="V" u2="&#x3c0;" k="19" />
    <hkern u1="V" u2="&#x192;" k="84" />
    <hkern u1="V" u2="&#xbf;" k="112" />
    <hkern u1="V" u2="&#xa4;" k="47" />
    <hkern u1="V" u2="&#xa3;" k="65" />
    <hkern u1="V" u2="&#xa2;" k="28" />
    <hkern u1="V" u2="&#x3a;" k="28" />
    <hkern u1="V" u2="&#x39;" k="9" />
    <hkern u1="V" u2="&#x34;" k="84" />
    <hkern u1="V" u2="&#x33;" k="9" />
    <hkern u1="V" u2="&#x32;" k="9" />
    <hkern u1="V" u2="&#x2f;" k="112" />
    <hkern u1="V" u2="&#x24;" k="37" />
    <hkern u1="V" u2="&#x23;" k="37" />
    <hkern u1="W" u2="&#x221e;" k="37" />
    <hkern u1="W" u2="&#x3c0;" k="19" />
    <hkern u1="W" u2="&#x192;" k="84" />
    <hkern u1="W" u2="&#xbf;" k="112" />
    <hkern u1="W" u2="&#xa4;" k="47" />
    <hkern u1="W" u2="&#xa3;" k="65" />
    <hkern u1="W" u2="&#xa2;" k="28" />
    <hkern u1="W" u2="&#x3a;" k="28" />
    <hkern u1="W" u2="&#x39;" k="9" />
    <hkern u1="W" u2="&#x34;" k="84" />
    <hkern u1="W" u2="&#x33;" k="9" />
    <hkern u1="W" u2="&#x32;" k="9" />
    <hkern u1="W" u2="&#x2f;" k="112" />
    <hkern u1="W" u2="&#x24;" k="37" />
    <hkern u1="W" u2="&#x23;" k="37" />
    <hkern u1="X" u2="&#x221e;" k="47" />
    <hkern u1="X" u2="&#x192;" k="37" />
    <hkern u1="X" u2="&#xb6;" k="9" />
    <hkern u1="X" u2="&#xa4;" k="65" />
    <hkern u1="X" u2="&#xa3;" k="19" />
    <hkern u1="X" u2="&#xa2;" k="47" />
    <hkern u1="X" u2="&#x3a;" k="28" />
    <hkern u1="X" u2="&#x39;" k="19" />
    <hkern u1="X" u2="&#x34;" k="37" />
    <hkern u1="X" u2="&#x33;" k="19" />
    <hkern u1="X" u2="&#x32;" k="9" />
    <hkern u1="X" u2="&#x24;" k="56" />
    <hkern u1="X" u2="&#x23;" k="56" />
    <hkern u1="Y" u2="&#x221e;" k="74" />
    <hkern u1="Y" u2="&#x3c0;" k="47" />
    <hkern u1="Y" u2="&#x192;" k="121" />
    <hkern u1="Y" u2="&#xbf;" k="168" />
    <hkern u1="Y" u2="&#xb6;" k="19" />
    <hkern u1="Y" u2="&#xa4;" k="84" />
    <hkern u1="Y" u2="&#xa3;" k="93" />
    <hkern u1="Y" u2="&#xa2;" k="65" />
    <hkern u1="Y" u2="&#x3a;" k="47" />
    <hkern u1="Y" u2="&#x39;" k="19" />
    <hkern u1="Y" u2="&#x34;" k="130" />
    <hkern u1="Y" u2="&#x33;" k="19" />
    <hkern u1="Y" u2="&#x32;" k="19" />
    <hkern u1="Y" u2="&#x2f;" k="130" />
    <hkern u1="Y" u2="&#x24;" k="65" />
    <hkern u1="Y" u2="&#x23;" k="74" />
    <hkern u1="Z" u2="&#x221e;" k="19" />
    <hkern u1="Z" u2="&#x192;" k="19" />
    <hkern u1="Z" u2="&#xa4;" k="37" />
    <hkern u1="Z" u2="&#xa3;" k="37" />
    <hkern u1="Z" u2="&#xa2;" k="19" />
    <hkern u1="Z" u2="&#x3a;" k="19" />
    <hkern u1="Z" u2="&#x34;" k="37" />
    <hkern u1="Z" u2="&#x2f;" k="37" />
    <hkern u1="Z" u2="&#x24;" k="19" />
    <hkern u1="Z" u2="&#x23;" k="19" />
    <hkern u1="[" u2="&#x221e;" k="19" />
    <hkern u1="[" u2="&#x192;" k="19" />
    <hkern u1="[" u2="&#xa4;" k="19" />
    <hkern u1="[" u2="&#xa3;" k="19" />
    <hkern u1="[" u2="&#xa2;" k="19" />
    <hkern u1="[" u2="&#x3a;" k="19" />
    <hkern u1="[" u2="&#x34;" k="9" />
    <hkern u1="[" u2="&#x24;" k="19" />
    <hkern u1="[" u2="&#x23;" k="19" />
    <hkern u1="\" g2="caron.alt" k="74" />
    <hkern u1="\" g2="nine.numr" k="74" />
    <hkern u1="\" g2="eight.numr" k="74" />
    <hkern u1="\" g2="seven.numr" k="74" />
    <hkern u1="\" g2="six.numr" k="74" />
    <hkern u1="\" g2="five.numr" k="74" />
    <hkern u1="\" g2="four.numr" k="74" />
    <hkern u1="\" g2="three.numr" k="74" />
    <hkern u1="\" g2="two.numr" k="74" />
    <hkern u1="\" g2="one.numr" k="74" />
    <hkern u1="\" g2="zero.numr" k="74" />
    <hkern u1="\" g2="pi.ss01" k="-37" />
    <hkern u1="\" g2="uring.ss01" k="19" />
    <hkern u1="\" g2="uogonek.ss01" k="19" />
    <hkern u1="\" g2="umacron.ss01" k="19" />
    <hkern u1="\" g2="uhungarumlaut.ss01" k="19" />
    <hkern u1="\" g2="ugrave.ss01" k="19" />
    <hkern u1="\" g2="udieresis.ss01" k="19" />
    <hkern u1="\" g2="ucircumflex.ss01" k="19" />
    <hkern u1="\" g2="uacute.ss01" k="19" />
    <hkern u1="\" g2="u.ss01" k="19" />
    <hkern u1="\" g2="j.ss01" k="19" />
    <hkern u1="\" g2="i.loclTRK" k="9" />
    <hkern u1="\" g2="Uring.ss01" k="19" />
    <hkern u1="\" g2="Uogonek.ss01" k="19" />
    <hkern u1="\" g2="Umacron.ss01" k="19" />
    <hkern u1="\" g2="Uhungarumlaut.ss01" k="19" />
    <hkern u1="\" g2="Ugrave.ss01" k="19" />
    <hkern u1="\" g2="Udieresis.ss01" k="19" />
    <hkern u1="\" g2="Ucircumflex.ss01" k="19" />
    <hkern u1="\" g2="Uacute.ss01" k="19" />
    <hkern u1="\" g2="U.ss01" k="19" />
    <hkern u1="\" g2="J.ss01" k="19" />
    <hkern u1="\" g2="fi" k="9" />
    <hkern u1="\" u2="&#x2265;" k="28" />
    <hkern u1="\" u2="&#x2264;" k="28" />
    <hkern u1="\" u2="&#x2260;" k="28" />
    <hkern u1="\" u2="&#x2248;" k="28" />
    <hkern u1="\" u2="&#x2219;" k="56" />
    <hkern u1="\" u2="&#x2212;" k="56" />
    <hkern u1="\" u2="&#x2122;" k="74" />
    <hkern u1="\" u2="&#x20ac;" k="19" />
    <hkern u1="\" u2="&#x2079;" k="74" />
    <hkern u1="\" u2="&#x2078;" k="74" />
    <hkern u1="\" u2="&#x2077;" k="74" />
    <hkern u1="\" u2="&#x2076;" k="74" />
    <hkern u1="\" u2="&#x2075;" k="74" />
    <hkern u1="\" u2="&#x2074;" k="74" />
    <hkern u1="\" u2="&#x2070;" k="74" />
    <hkern u1="\" u2="&#x203a;" k="28" />
    <hkern u1="\" u2="&#x2039;" k="28" />
    <hkern u1="\" u2="&#x2030;" k="74" />
    <hkern u1="\" u2="&#x2022;" k="56" />
    <hkern u1="\" u2="&#x2021;" k="74" />
    <hkern u1="\" u2="&#x2020;" k="74" />
    <hkern u1="\" u2="&#x201d;" k="74" />
    <hkern u1="\" u2="&#x201c;" k="74" />
    <hkern u1="\" u2="&#x2019;" k="74" />
    <hkern u1="\" u2="&#x2018;" k="74" />
    <hkern u1="\" u2="&#x2014;" k="56" />
    <hkern u1="\" u2="&#x2013;" k="56" />
    <hkern u1="\" u2="&#x1ef9;" k="130" />
    <hkern u1="\" u2="&#x1ef8;" k="130" />
    <hkern u1="\" u2="&#x1ef7;" k="130" />
    <hkern u1="\" u2="&#x1ef6;" k="130" />
    <hkern u1="\" u2="&#x1ef5;" k="130" />
    <hkern u1="\" u2="&#x1ef4;" k="130" />
    <hkern u1="\" u2="&#x1ef3;" k="130" />
    <hkern u1="\" u2="&#x1ef2;" k="130" />
    <hkern u1="\" u2="&#x1ef1;" k="19" />
    <hkern u1="\" u2="&#x1ef0;" k="19" />
    <hkern u1="\" u2="&#x1eef;" k="19" />
    <hkern u1="\" u2="&#x1eee;" k="19" />
    <hkern u1="\" u2="&#x1eed;" k="19" />
    <hkern u1="\" u2="&#x1eec;" k="19" />
    <hkern u1="\" u2="&#x1eeb;" k="19" />
    <hkern u1="\" u2="&#x1eea;" k="19" />
    <hkern u1="\" u2="&#x1ee9;" k="19" />
    <hkern u1="\" u2="&#x1ee8;" k="19" />
    <hkern u1="\" u2="&#x1ee7;" k="19" />
    <hkern u1="\" u2="&#x1ee6;" k="19" />
    <hkern u1="\" u2="&#x1ee5;" k="19" />
    <hkern u1="\" u2="&#x1ee4;" k="19" />
    <hkern u1="\" u2="&#x1ee3;" k="19" />
    <hkern u1="\" u2="&#x1ee2;" k="19" />
    <hkern u1="\" u2="&#x1ee1;" k="19" />
    <hkern u1="\" u2="&#x1ee0;" k="19" />
    <hkern u1="\" u2="&#x1edf;" k="19" />
    <hkern u1="\" u2="&#x1ede;" k="19" />
    <hkern u1="\" u2="&#x1edd;" k="19" />
    <hkern u1="\" u2="&#x1edc;" k="19" />
    <hkern u1="\" u2="&#x1edb;" k="19" />
    <hkern u1="\" u2="&#x1eda;" k="19" />
    <hkern u1="\" u2="&#x1ed9;" k="19" />
    <hkern u1="\" u2="&#x1ed8;" k="19" />
    <hkern u1="\" u2="&#x1ed7;" k="19" />
    <hkern u1="\" u2="&#x1ed6;" k="19" />
    <hkern u1="\" u2="&#x1ed5;" k="19" />
    <hkern u1="\" u2="&#x1ed4;" k="19" />
    <hkern u1="\" u2="&#x1ed3;" k="19" />
    <hkern u1="\" u2="&#x1ed2;" k="19" />
    <hkern u1="\" u2="&#x1ed1;" k="19" />
    <hkern u1="\" u2="&#x1ed0;" k="19" />
    <hkern u1="\" u2="&#x1ecf;" k="19" />
    <hkern u1="\" u2="&#x1ece;" k="19" />
    <hkern u1="\" u2="&#x1ecd;" k="19" />
    <hkern u1="\" u2="&#x1ecc;" k="19" />
    <hkern u1="\" u2="&#x1ecb;" k="9" />
    <hkern u1="\" u2="&#x1eca;" k="9" />
    <hkern u1="\" u2="&#x1ec9;" k="9" />
    <hkern u1="\" u2="&#x1ec8;" k="9" />
    <hkern u1="\" u2="&#x1ec7;" k="9" />
    <hkern u1="\" u2="&#x1ec6;" k="9" />
    <hkern u1="\" u2="&#x1ec5;" k="9" />
    <hkern u1="\" u2="&#x1ec4;" k="9" />
    <hkern u1="\" u2="&#x1ec3;" k="9" />
    <hkern u1="\" u2="&#x1ec2;" k="9" />
    <hkern u1="\" u2="&#x1ec1;" k="9" />
    <hkern u1="\" u2="&#x1ec0;" k="9" />
    <hkern u1="\" u2="&#x1ebf;" k="9" />
    <hkern u1="\" u2="&#x1ebe;" k="9" />
    <hkern u1="\" u2="&#x1ebd;" k="9" />
    <hkern u1="\" u2="&#x1ebc;" k="9" />
    <hkern u1="\" u2="&#x1ebb;" k="9" />
    <hkern u1="\" u2="&#x1eba;" k="9" />
    <hkern u1="\" u2="&#x1eb9;" k="9" />
    <hkern u1="\" u2="&#x1eb8;" k="9" />
    <hkern u1="\" u2="&#x2dd;" k="74" />
    <hkern u1="\" u2="&#x2dc;" k="74" />
    <hkern u1="\" u2="&#x2da;" k="74" />
    <hkern u1="\" u2="&#x2d9;" k="74" />
    <hkern u1="\" u2="&#x2d8;" k="74" />
    <hkern u1="\" u2="&#x2c9;" k="74" />
    <hkern u1="\" u2="&#x2c7;" k="74" />
    <hkern u1="\" u2="&#x2c6;" k="74" />
    <hkern u1="\" u2="&#x21b;" k="93" />
    <hkern u1="\" u2="&#x21a;" k="93" />
    <hkern u1="\" u2="&#x219;" k="19" />
    <hkern u1="\" u2="&#x218;" k="19" />
    <hkern u1="\" u2="&#x1b0;" k="19" />
    <hkern u1="\" u2="&#x1af;" k="19" />
    <hkern u1="\" u2="&#x1a1;" k="19" />
    <hkern u1="\" u2="&#x1a0;" k="19" />
    <hkern u1="\" u2="&#x178;" k="130" />
    <hkern u1="\" u2="&#x173;" k="19" />
    <hkern u1="\" u2="&#x172;" k="19" />
    <hkern u1="\" u2="&#x171;" k="19" />
    <hkern u1="\" u2="&#x170;" k="19" />
    <hkern u1="\" u2="&#x16f;" k="19" />
    <hkern u1="\" u2="&#x16e;" k="19" />
    <hkern u1="\" u2="&#x16b;" k="19" />
    <hkern u1="\" u2="&#x16a;" k="19" />
    <hkern u1="\" u2="&#x169;" k="19" />
    <hkern u1="\" u2="&#x168;" k="19" />
    <hkern u1="\" u2="&#x165;" k="93" />
    <hkern u1="\" u2="&#x164;" k="93" />
    <hkern u1="\" u2="&#x163;" k="93" />
    <hkern u1="\" u2="&#x162;" k="93" />
    <hkern u1="\" u2="&#x161;" k="19" />
    <hkern u1="\" u2="&#x160;" k="19" />
    <hkern u1="\" u2="&#x15f;" k="19" />
    <hkern u1="\" u2="&#x15e;" k="19" />
    <hkern u1="\" u2="&#x15b;" k="19" />
    <hkern u1="\" u2="&#x15a;" k="19" />
    <hkern u1="\" u2="&#x159;" k="9" />
    <hkern u1="\" u2="&#x158;" k="9" />
    <hkern u1="\" u2="&#x157;" k="9" />
    <hkern u1="\" u2="&#x156;" k="9" />
    <hkern u1="\" u2="&#x155;" k="9" />
    <hkern u1="\" u2="&#x154;" k="9" />
    <hkern u1="\" u2="&#x153;" k="19" />
    <hkern u1="\" u2="&#x152;" k="19" />
    <hkern u1="\" u2="&#x151;" k="19" />
    <hkern u1="\" u2="&#x150;" k="19" />
    <hkern u1="\" u2="&#x14d;" k="19" />
    <hkern u1="\" u2="&#x14c;" k="19" />
    <hkern u1="\" u2="&#x148;" k="9" />
    <hkern u1="\" u2="&#x147;" k="9" />
    <hkern u1="\" u2="&#x146;" k="9" />
    <hkern u1="\" u2="&#x145;" k="9" />
    <hkern u1="\" u2="&#x144;" k="9" />
    <hkern u1="\" u2="&#x143;" k="9" />
    <hkern u1="\" u2="&#x137;" k="9" />
    <hkern u1="\" u2="&#x136;" k="9" />
    <hkern u1="\" u2="&#x130;" k="9" />
    <hkern u1="\" u2="&#x12f;" k="9" />
    <hkern u1="\" u2="&#x12e;" k="9" />
    <hkern u1="\" u2="&#x12b;" k="9" />
    <hkern u1="\" u2="&#x12a;" k="9" />
    <hkern u1="\" u2="&#x129;" k="9" />
    <hkern u1="\" u2="&#x128;" k="9" />
    <hkern u1="\" u2="&#x123;" k="19" />
    <hkern u1="\" u2="&#x122;" k="19" />
    <hkern u1="\" u2="&#x11f;" k="19" />
    <hkern u1="\" u2="&#x11e;" k="19" />
    <hkern u1="\" u2="&#x11b;" k="9" />
    <hkern u1="\" u2="&#x11a;" k="9" />
    <hkern u1="\" u2="&#x119;" k="9" />
    <hkern u1="\" u2="&#x118;" k="9" />
    <hkern u1="\" u2="&#x117;" k="9" />
    <hkern u1="\" u2="&#x116;" k="9" />
    <hkern u1="\" u2="&#x113;" k="9" />
    <hkern u1="\" u2="&#x112;" k="9" />
    <hkern u1="\" u2="&#x111;" k="9" />
    <hkern u1="\" u2="&#x110;" k="9" />
    <hkern u1="\" u2="&#x10f;" k="9" />
    <hkern u1="\" u2="&#x10e;" k="9" />
    <hkern u1="\" u2="&#x10d;" k="19" />
    <hkern u1="\" u2="&#x10c;" k="19" />
    <hkern u1="\" u2="&#x107;" k="19" />
    <hkern u1="\" u2="&#x106;" k="19" />
    <hkern u1="\" u2="&#xff;" k="130" />
    <hkern u1="\" u2="&#xfd;" k="130" />
    <hkern u1="\" u2="&#xfc;" k="19" />
    <hkern u1="\" u2="&#xfb;" k="19" />
    <hkern u1="\" u2="&#xfa;" k="19" />
    <hkern u1="\" u2="&#xf9;" k="19" />
    <hkern u1="\" u2="&#xf8;" k="19" />
    <hkern u1="\" u2="&#xf7;" k="28" />
    <hkern u1="\" u2="&#xf6;" k="19" />
    <hkern u1="\" u2="&#xf5;" k="19" />
    <hkern u1="\" u2="&#xf4;" k="19" />
    <hkern u1="\" u2="&#xf3;" k="19" />
    <hkern u1="\" u2="&#xf2;" k="19" />
    <hkern u1="\" u2="&#xf1;" k="9" />
    <hkern u1="\" u2="&#xf0;" k="9" />
    <hkern u1="\" u2="&#xef;" k="9" />
    <hkern u1="\" u2="&#xee;" k="9" />
    <hkern u1="\" u2="&#xed;" k="9" />
    <hkern u1="\" u2="&#xec;" k="9" />
    <hkern u1="\" u2="&#xeb;" k="9" />
    <hkern u1="\" u2="&#xea;" k="9" />
    <hkern u1="\" u2="&#xe9;" k="9" />
    <hkern u1="\" u2="&#xe8;" k="9" />
    <hkern u1="\" u2="&#xe7;" k="19" />
    <hkern u1="\" u2="&#xdd;" k="130" />
    <hkern u1="\" u2="&#xdc;" k="19" />
    <hkern u1="\" u2="&#xdb;" k="19" />
    <hkern u1="\" u2="&#xda;" k="19" />
    <hkern u1="\" u2="&#xd9;" k="19" />
    <hkern u1="\" u2="&#xd8;" k="19" />
    <hkern u1="\" u2="&#xd7;" k="28" />
    <hkern u1="\" u2="&#xd6;" k="19" />
    <hkern u1="\" u2="&#xd5;" k="19" />
    <hkern u1="\" u2="&#xd4;" k="19" />
    <hkern u1="\" u2="&#xd3;" k="19" />
    <hkern u1="\" u2="&#xd2;" k="19" />
    <hkern u1="\" u2="&#xd1;" k="9" />
    <hkern u1="\" u2="&#xd0;" k="9" />
    <hkern u1="\" u2="&#xcf;" k="9" />
    <hkern u1="\" u2="&#xce;" k="9" />
    <hkern u1="\" u2="&#xcd;" k="9" />
    <hkern u1="\" u2="&#xcc;" k="9" />
    <hkern u1="\" u2="&#xcb;" k="9" />
    <hkern u1="\" u2="&#xca;" k="9" />
    <hkern u1="\" u2="&#xc9;" k="9" />
    <hkern u1="\" u2="&#xc8;" k="9" />
    <hkern u1="\" u2="&#xc7;" k="19" />
    <hkern u1="\" u2="&#xbe;" k="74" />
    <hkern u1="\" u2="&#xbd;" k="74" />
    <hkern u1="\" u2="&#xbc;" k="74" />
    <hkern u1="\" u2="&#xbb;" k="28" />
    <hkern u1="\" u2="&#xba;" k="74" />
    <hkern u1="\" u2="&#xb9;" k="74" />
    <hkern u1="\" u2="&#xb7;" k="56" />
    <hkern u1="\" u2="&#xb4;" k="74" />
    <hkern u1="\" u2="&#xb3;" k="74" />
    <hkern u1="\" u2="&#xb2;" k="74" />
    <hkern u1="\" u2="&#xb1;" k="28" />
    <hkern u1="\" u2="&#xb0;" k="74" />
    <hkern u1="\" u2="&#xaf;" k="74" />
    <hkern u1="\" u2="&#xae;" k="74" />
    <hkern u1="\" u2="&#xad;" k="56" />
    <hkern u1="\" u2="&#xac;" k="56" />
    <hkern u1="\" u2="&#xab;" k="28" />
    <hkern u1="\" u2="&#xaa;" k="74" />
    <hkern u1="\" u2="&#xa9;" k="19" />
    <hkern u1="\" u2="&#xa8;" k="74" />
    <hkern u1="\" u2="&#xa7;" k="19" />
    <hkern u1="\" u2="&#xa5;" k="130" />
    <hkern u1="\" u2="&#xa1;" k="-37" />
    <hkern u1="\" u2="&#x7e;" k="56" />
    <hkern u1="\" u2="&#x7b;" k="56" />
    <hkern u1="\" u2="y" k="130" />
    <hkern u1="\" u2="w" k="112" />
    <hkern u1="\" u2="v" k="112" />
    <hkern u1="\" u2="u" k="19" />
    <hkern u1="\" u2="t" k="93" />
    <hkern u1="\" u2="s" k="19" />
    <hkern u1="\" u2="r" k="9" />
    <hkern u1="\" u2="q" k="19" />
    <hkern u1="\" u2="p" k="9" />
    <hkern u1="\" u2="o" k="19" />
    <hkern u1="\" u2="n" k="9" />
    <hkern u1="\" u2="m" k="9" />
    <hkern u1="\" u2="k" k="9" />
    <hkern u1="\" u2="j" k="19" />
    <hkern u1="\" u2="i" k="9" />
    <hkern u1="\" u2="h" k="9" />
    <hkern u1="\" u2="g" k="19" />
    <hkern u1="\" u2="f" k="9" />
    <hkern u1="\" u2="e" k="9" />
    <hkern u1="\" u2="d" k="9" />
    <hkern u1="\" u2="c" k="19" />
    <hkern u1="\" u2="b" k="9" />
    <hkern u1="\" u2="`" k="74" />
    <hkern u1="\" u2="^" k="74" />
    <hkern u1="\" u2="Y" k="130" />
    <hkern u1="\" u2="W" k="112" />
    <hkern u1="\" u2="V" k="112" />
    <hkern u1="\" u2="U" k="19" />
    <hkern u1="\" u2="T" k="93" />
    <hkern u1="\" u2="S" k="19" />
    <hkern u1="\" u2="R" k="9" />
    <hkern u1="\" u2="Q" k="19" />
    <hkern u1="\" u2="P" k="9" />
    <hkern u1="\" u2="O" k="19" />
    <hkern u1="\" u2="N" k="9" />
    <hkern u1="\" u2="M" k="9" />
    <hkern u1="\" u2="K" k="9" />
    <hkern u1="\" u2="J" k="19" />
    <hkern u1="\" u2="I" k="9" />
    <hkern u1="\" u2="H" k="9" />
    <hkern u1="\" u2="G" k="19" />
    <hkern u1="\" u2="F" k="9" />
    <hkern u1="\" u2="E" k="9" />
    <hkern u1="\" u2="D" k="9" />
    <hkern u1="\" u2="C" k="19" />
    <hkern u1="\" u2="B" k="9" />
    <hkern u1="\" u2="&#x40;" k="19" />
    <hkern u1="\" u2="&#x3e;" k="28" />
    <hkern u1="\" u2="&#x3d;" k="28" />
    <hkern u1="\" u2="&#x3c;" k="56" />
    <hkern u1="\" u2="&#x3b;" k="-37" />
    <hkern u1="\" u2="&#x38;" k="19" />
    <hkern u1="\" u2="&#x36;" k="19" />
    <hkern u1="\" u2="&#x35;" k="19" />
    <hkern u1="\" u2="&#x30;" k="19" />
    <hkern u1="\" u2="&#x2d;" k="56" />
    <hkern u1="\" u2="&#x2b;" k="28" />
    <hkern u1="\" u2="&#x2a;" k="74" />
    <hkern u1="\" u2="&#x27;" k="74" />
    <hkern u1="\" u2="&#x26;" k="19" />
    <hkern u1="\" u2="&#x25;" k="74" />
    <hkern u1="\" u2="&#x22;" k="74" />
    <hkern u1="\" u2="&#x221e;" k="37" />
    <hkern u1="\" u2="&#x192;" k="37" />
    <hkern u1="\" u2="&#xbf;" k="-37" />
    <hkern u1="\" u2="&#xb6;" k="74" />
    <hkern u1="\" u2="&#xa4;" k="47" />
    <hkern u1="\" u2="&#xa3;" k="19" />
    <hkern u1="\" u2="&#xa2;" k="37" />
    <hkern u1="\" u2="\" k="74" />
    <hkern u1="\" u2="&#x3f;" k="93" />
    <hkern u1="\" u2="&#x3a;" k="28" />
    <hkern u1="\" u2="&#x39;" k="19" />
    <hkern u1="\" u2="&#x37;" k="112" />
    <hkern u1="\" u2="&#x34;" k="9" />
    <hkern u1="\" u2="&#x33;" k="19" />
    <hkern u1="\" u2="&#x31;" k="37" />
    <hkern u1="\" u2="&#x2f;" k="-37" />
    <hkern u1="\" u2="&#x24;" k="65" />
    <hkern u1="\" u2="&#x23;" k="37" />
    <hkern u1="^" u2="&#x192;" k="56" />
    <hkern u1="^" u2="&#xbf;" k="112" />
    <hkern u1="^" u2="&#xa3;" k="37" />
    <hkern u1="^" u2="&#x34;" k="74" />
    <hkern u1="^" u2="&#x2f;" k="74" />
    <hkern u1="_" u2="&#xb6;" k="74" />
    <hkern u1="_" u2="\" k="74" />
    <hkern u1="_" u2="&#x3f;" k="74" />
    <hkern u1="_" u2="&#x37;" k="93" />
    <hkern u1="_" u2="&#x31;" k="37" />
    <hkern u1="_" u2="&#x24;" k="9" />
    <hkern u1="`" u2="&#x192;" k="56" />
    <hkern u1="`" u2="&#xbf;" k="112" />
    <hkern u1="`" u2="&#xa3;" k="37" />
    <hkern u1="`" u2="&#x34;" k="74" />
    <hkern u1="`" u2="&#x2f;" k="74" />
    <hkern u1="a" u2="&#x221e;" k="37" />
    <hkern u1="a" u2="&#x192;" k="37" />
    <hkern u1="a" u2="&#xb6;" k="65" />
    <hkern u1="a" u2="&#xa4;" k="56" />
    <hkern u1="a" u2="&#xa3;" k="19" />
    <hkern u1="a" u2="&#xa2;" k="47" />
    <hkern u1="a" u2="\" k="112" />
    <hkern u1="a" u2="&#x3f;" k="84" />
    <hkern u1="a" u2="&#x3a;" k="28" />
    <hkern u1="a" u2="&#x37;" k="112" />
    <hkern u1="a" u2="&#x34;" k="19" />
    <hkern u1="a" u2="&#x24;" k="74" />
    <hkern u1="a" u2="&#x23;" k="37" />
    <hkern u1="b" u2="\" k="19" />
    <hkern u1="b" u2="&#x2f;" k="19" />
    <hkern u1="c" u2="\" k="19" />
    <hkern u1="c" u2="&#x2f;" k="19" />
    <hkern u1="d" u2="\" k="19" />
    <hkern u1="d" u2="&#x2f;" k="19" />
    <hkern u1="e" u2="&#x221e;" k="19" />
    <hkern u1="e" u2="&#x192;" k="19" />
    <hkern u1="e" u2="&#xb6;" k="19" />
    <hkern u1="e" u2="&#xa4;" k="28" />
    <hkern u1="e" u2="&#xa2;" k="19" />
    <hkern u1="e" u2="&#x3a;" k="19" />
    <hkern u1="e" u2="&#x24;" k="19" />
    <hkern u1="e" u2="&#x23;" k="19" />
    <hkern u1="f" u2="&#x221e;" k="47" />
    <hkern u1="f" u2="&#x3c0;" k="56" />
    <hkern u1="f" u2="&#x192;" k="74" />
    <hkern u1="f" u2="&#xbf;" k="186" />
    <hkern u1="f" u2="&#xb6;" k="28" />
    <hkern u1="f" u2="&#xa4;" k="56" />
    <hkern u1="f" u2="&#xa3;" k="74" />
    <hkern u1="f" u2="&#xa2;" k="47" />
    <hkern u1="f" u2="&#x39;" k="28" />
    <hkern u1="f" u2="&#x37;" k="19" />
    <hkern u1="f" u2="&#x34;" k="93" />
    <hkern u1="f" u2="&#x33;" k="28" />
    <hkern u1="f" u2="&#x32;" k="28" />
    <hkern u1="f" u2="&#x2f;" k="112" />
    <hkern u1="f" u2="&#x24;" k="47" />
    <hkern u1="f" u2="&#x23;" k="47" />
    <hkern u1="g" u2="&#x3f;" k="19" />
    <hkern u1="g" u2="&#x32;" k="-9" />
    <hkern u1="g" u2="&#x31;" k="19" />
    <hkern u1="j" u2="&#x2f;" k="19" />
    <hkern u1="k" u2="&#x221e;" k="47" />
    <hkern u1="k" u2="&#x192;" k="37" />
    <hkern u1="k" u2="&#xb6;" k="9" />
    <hkern u1="k" u2="&#xa4;" k="65" />
    <hkern u1="k" u2="&#xa3;" k="19" />
    <hkern u1="k" u2="&#xa2;" k="47" />
    <hkern u1="k" u2="&#x3a;" k="28" />
    <hkern u1="k" u2="&#x39;" k="19" />
    <hkern u1="k" u2="&#x34;" k="37" />
    <hkern u1="k" u2="&#x33;" k="19" />
    <hkern u1="k" u2="&#x32;" k="9" />
    <hkern u1="k" u2="&#x24;" k="56" />
    <hkern u1="k" u2="&#x23;" k="56" />
    <hkern u1="l" u2="&#x221e;" k="19" />
    <hkern u1="l" u2="&#xb6;" k="102" />
    <hkern u1="l" u2="&#xa4;" k="47" />
    <hkern u1="l" u2="&#xa2;" k="19" />
    <hkern u1="l" u2="\" k="140" />
    <hkern u1="l" u2="&#x3f;" k="140" />
    <hkern u1="l" u2="&#x37;" k="140" />
    <hkern u1="l" u2="&#x31;" k="74" />
    <hkern u1="l" u2="&#x24;" k="19" />
    <hkern u1="o" u2="\" k="19" />
    <hkern u1="o" u2="&#x2f;" k="19" />
    <hkern u1="p" u2="&#xbf;" k="74" />
    <hkern u1="p" u2="&#x2f;" k="74" />
    <hkern u1="q" u2="\" k="19" />
    <hkern u1="q" u2="&#x2f;" k="19" />
    <hkern u1="r" u2="&#x3c0;" k="-19" />
    <hkern u1="r" u2="\" k="19" />
    <hkern u1="s" u2="\" k="19" />
    <hkern u1="s" u2="&#x2f;" k="19" />
    <hkern u1="t" u2="&#x221e;" k="19" />
    <hkern u1="t" u2="&#x3c0;" k="47" />
    <hkern u1="t" u2="&#x192;" k="74" />
    <hkern u1="t" u2="&#xbf;" k="112" />
    <hkern u1="t" u2="&#xa4;" k="28" />
    <hkern u1="t" u2="&#xa3;" k="56" />
    <hkern u1="t" u2="&#xa2;" k="19" />
    <hkern u1="t" u2="&#x3a;" k="37" />
    <hkern u1="t" u2="&#x34;" k="65" />
    <hkern u1="t" u2="&#x2f;" k="93" />
    <hkern u1="t" u2="&#x24;" k="9" />
    <hkern u1="t" u2="&#x23;" k="19" />
    <hkern u1="v" u2="&#x221e;" k="37" />
    <hkern u1="v" u2="&#x3c0;" k="19" />
    <hkern u1="v" u2="&#x192;" k="84" />
    <hkern u1="v" u2="&#xbf;" k="112" />
    <hkern u1="v" u2="&#xa4;" k="47" />
    <hkern u1="v" u2="&#xa3;" k="65" />
    <hkern u1="v" u2="&#xa2;" k="28" />
    <hkern u1="v" u2="&#x3a;" k="28" />
    <hkern u1="v" u2="&#x39;" k="9" />
    <hkern u1="v" u2="&#x34;" k="84" />
    <hkern u1="v" u2="&#x33;" k="9" />
    <hkern u1="v" u2="&#x32;" k="9" />
    <hkern u1="v" u2="&#x2f;" k="112" />
    <hkern u1="v" u2="&#x24;" k="37" />
    <hkern u1="v" u2="&#x23;" k="37" />
    <hkern u1="w" u2="&#x221e;" k="37" />
    <hkern u1="w" u2="&#x3c0;" k="19" />
    <hkern u1="w" u2="&#x192;" k="84" />
    <hkern u1="w" u2="&#xbf;" k="112" />
    <hkern u1="w" u2="&#xa4;" k="47" />
    <hkern u1="w" u2="&#xa3;" k="65" />
    <hkern u1="w" u2="&#xa2;" k="28" />
    <hkern u1="w" u2="&#x3a;" k="28" />
    <hkern u1="w" u2="&#x39;" k="9" />
    <hkern u1="w" u2="&#x34;" k="84" />
    <hkern u1="w" u2="&#x33;" k="9" />
    <hkern u1="w" u2="&#x32;" k="9" />
    <hkern u1="w" u2="&#x2f;" k="112" />
    <hkern u1="w" u2="&#x24;" k="37" />
    <hkern u1="w" u2="&#x23;" k="37" />
    <hkern u1="x" u2="&#x221e;" k="47" />
    <hkern u1="x" u2="&#x192;" k="37" />
    <hkern u1="x" u2="&#xb6;" k="9" />
    <hkern u1="x" u2="&#xa4;" k="65" />
    <hkern u1="x" u2="&#xa3;" k="19" />
    <hkern u1="x" u2="&#xa2;" k="47" />
    <hkern u1="x" u2="&#x3a;" k="28" />
    <hkern u1="x" u2="&#x39;" k="19" />
    <hkern u1="x" u2="&#x34;" k="37" />
    <hkern u1="x" u2="&#x33;" k="19" />
    <hkern u1="x" u2="&#x32;" k="9" />
    <hkern u1="x" u2="&#x24;" k="56" />
    <hkern u1="x" u2="&#x23;" k="56" />
    <hkern u1="y" u2="&#x221e;" k="74" />
    <hkern u1="y" u2="&#x3c0;" k="47" />
    <hkern u1="y" u2="&#x192;" k="121" />
    <hkern u1="y" u2="&#xbf;" k="168" />
    <hkern u1="y" u2="&#xb6;" k="19" />
    <hkern u1="y" u2="&#xa4;" k="84" />
    <hkern u1="y" u2="&#xa3;" k="93" />
    <hkern u1="y" u2="&#xa2;" k="65" />
    <hkern u1="y" u2="&#x3a;" k="47" />
    <hkern u1="y" u2="&#x39;" k="19" />
    <hkern u1="y" u2="&#x34;" k="130" />
    <hkern u1="y" u2="&#x33;" k="19" />
    <hkern u1="y" u2="&#x32;" k="19" />
    <hkern u1="y" u2="&#x2f;" k="130" />
    <hkern u1="y" u2="&#x24;" k="65" />
    <hkern u1="y" u2="&#x23;" k="74" />
    <hkern u1="z" u2="&#x221e;" k="19" />
    <hkern u1="z" u2="&#x192;" k="19" />
    <hkern u1="z" u2="&#xa4;" k="37" />
    <hkern u1="z" u2="&#xa3;" k="37" />
    <hkern u1="z" u2="&#xa2;" k="19" />
    <hkern u1="z" u2="&#x3a;" k="19" />
    <hkern u1="z" u2="&#x34;" k="37" />
    <hkern u1="z" u2="&#x2f;" k="37" />
    <hkern u1="z" u2="&#x24;" k="19" />
    <hkern u1="z" u2="&#x23;" k="19" />
    <hkern u1="&#x7b;" u2="&#x221e;" k="19" />
    <hkern u1="&#x7b;" u2="&#x192;" k="19" />
    <hkern u1="&#x7b;" u2="&#xa4;" k="19" />
    <hkern u1="&#x7b;" u2="&#xa3;" k="19" />
    <hkern u1="&#x7b;" u2="&#xa2;" k="19" />
    <hkern u1="&#x7b;" u2="&#x3a;" k="19" />
    <hkern u1="&#x7b;" u2="&#x34;" k="9" />
    <hkern u1="&#x7b;" u2="&#x24;" k="19" />
    <hkern u1="&#x7b;" u2="&#x23;" k="19" />
    <hkern u1="&#x7d;" u2="&#x192;" k="19" />
    <hkern u1="&#x7d;" u2="&#xbf;" k="37" />
    <hkern u1="&#x7d;" u2="\" k="56" />
    <hkern u1="&#x7d;" u2="&#x3f;" k="19" />
    <hkern u1="&#x7d;" u2="&#x37;" k="74" />
    <hkern u1="&#x7d;" u2="&#x33;" k="19" />
    <hkern u1="&#x7d;" u2="&#x2f;" k="56" />
    <hkern u1="&#x7e;" u2="&#x192;" k="19" />
    <hkern u1="&#x7e;" u2="&#xbf;" k="37" />
    <hkern u1="&#x7e;" u2="\" k="56" />
    <hkern u1="&#x7e;" u2="&#x3f;" k="19" />
    <hkern u1="&#x7e;" u2="&#x37;" k="74" />
    <hkern u1="&#x7e;" u2="&#x33;" k="19" />
    <hkern u1="&#x7e;" u2="&#x2f;" k="56" />
    <hkern u1="&#xa1;" u2="\" k="37" />
    <hkern u1="&#xa1;" u2="&#x37;" k="56" />
    <hkern u1="&#xa1;" u2="&#x2f;" k="-37" />
    <hkern u1="&#xa2;" g2="ae.ss01" k="47" />
    <hkern u1="&#xa2;" g2="atilde.ss01" k="47" />
    <hkern u1="&#xa2;" g2="aring.ss01" k="47" />
    <hkern u1="&#xa2;" g2="aogonek.ss01" k="47" />
    <hkern u1="&#xa2;" g2="amacron.ss01" k="47" />
    <hkern u1="&#xa2;" g2="agrave.ss01" k="47" />
    <hkern u1="&#xa2;" g2="adieresis.ss01" k="47" />
    <hkern u1="&#xa2;" g2="acircumflex.ss01" k="47" />
    <hkern u1="&#xa2;" g2="abreve.ss01" k="47" />
    <hkern u1="&#xa2;" g2="aacute.ss01" k="47" />
    <hkern u1="&#xa2;" g2="a.ss01" k="47" />
    <hkern u1="&#xa2;" g2="AE.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Atilde.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Aring.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Aogonek.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Amacron.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Agrave.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Adieresis.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Acircumflex.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Abreve.ss01" k="47" />
    <hkern u1="&#xa2;" g2="Aacute.ss01" k="47" />
    <hkern u1="&#xa2;" g2="A.ss01" k="47" />
    <hkern u1="&#xa2;" u2="&#x20ac;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ef9;" k="65" />
    <hkern u1="&#xa2;" u2="&#x1ef8;" k="65" />
    <hkern u1="&#xa2;" u2="&#x1ef7;" k="65" />
    <hkern u1="&#xa2;" u2="&#x1ef6;" k="65" />
    <hkern u1="&#xa2;" u2="&#x1ef5;" k="65" />
    <hkern u1="&#xa2;" u2="&#x1ef4;" k="65" />
    <hkern u1="&#xa2;" u2="&#x1ef3;" k="65" />
    <hkern u1="&#xa2;" u2="&#x1ef2;" k="65" />
    <hkern u1="&#xa2;" u2="&#x1ee3;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ee2;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ee1;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ee0;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1edf;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ede;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1edd;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1edc;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1edb;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1eda;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed9;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed8;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed7;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed6;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed5;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed4;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed3;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed2;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed1;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ed0;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ecf;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ece;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ecd;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1ecc;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1eb7;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eb6;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eb5;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eb4;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eb3;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eb2;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eb1;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eb0;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eaf;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eae;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ead;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eac;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eab;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1eaa;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea9;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea8;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea7;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea6;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea5;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea4;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea3;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea2;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea1;" k="47" />
    <hkern u1="&#xa2;" u2="&#x1ea0;" k="47" />
    <hkern u1="&#xa2;" u2="&#x21b;" k="19" />
    <hkern u1="&#xa2;" u2="&#x21a;" k="19" />
    <hkern u1="&#xa2;" u2="&#x1a1;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x1a0;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x17e;" k="19" />
    <hkern u1="&#xa2;" u2="&#x17d;" k="19" />
    <hkern u1="&#xa2;" u2="&#x17c;" k="19" />
    <hkern u1="&#xa2;" u2="&#x17b;" k="19" />
    <hkern u1="&#xa2;" u2="&#x17a;" k="19" />
    <hkern u1="&#xa2;" u2="&#x179;" k="19" />
    <hkern u1="&#xa2;" u2="&#x178;" k="65" />
    <hkern u1="&#xa2;" u2="&#x165;" k="19" />
    <hkern u1="&#xa2;" u2="&#x164;" k="19" />
    <hkern u1="&#xa2;" u2="&#x163;" k="19" />
    <hkern u1="&#xa2;" u2="&#x162;" k="19" />
    <hkern u1="&#xa2;" u2="&#x153;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x152;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x151;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x150;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x14d;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x14c;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x123;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x122;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x11f;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x11e;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x10d;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x10c;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x107;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x106;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x105;" k="47" />
    <hkern u1="&#xa2;" u2="&#x104;" k="47" />
    <hkern u1="&#xa2;" u2="&#x103;" k="47" />
    <hkern u1="&#xa2;" u2="&#x102;" k="47" />
    <hkern u1="&#xa2;" u2="&#x101;" k="47" />
    <hkern u1="&#xa2;" u2="&#x100;" k="47" />
    <hkern u1="&#xa2;" u2="&#xff;" k="65" />
    <hkern u1="&#xa2;" u2="&#xfd;" k="65" />
    <hkern u1="&#xa2;" u2="&#xf8;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xf6;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xf5;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xf4;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xf3;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xf2;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xe7;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xe6;" k="47" />
    <hkern u1="&#xa2;" u2="&#xe5;" k="47" />
    <hkern u1="&#xa2;" u2="&#xe4;" k="47" />
    <hkern u1="&#xa2;" u2="&#xe3;" k="47" />
    <hkern u1="&#xa2;" u2="&#xe2;" k="47" />
    <hkern u1="&#xa2;" u2="&#xe1;" k="47" />
    <hkern u1="&#xa2;" u2="&#xe0;" k="47" />
    <hkern u1="&#xa2;" u2="&#xdd;" k="65" />
    <hkern u1="&#xa2;" u2="&#xd8;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xd6;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xd5;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xd4;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xd3;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xd2;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xc7;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xc6;" k="47" />
    <hkern u1="&#xa2;" u2="&#xc5;" k="47" />
    <hkern u1="&#xa2;" u2="&#xc4;" k="47" />
    <hkern u1="&#xa2;" u2="&#xc3;" k="47" />
    <hkern u1="&#xa2;" u2="&#xc2;" k="47" />
    <hkern u1="&#xa2;" u2="&#xc1;" k="47" />
    <hkern u1="&#xa2;" u2="&#xc0;" k="47" />
    <hkern u1="&#xa2;" u2="&#xa9;" k="-19" />
    <hkern u1="&#xa2;" u2="&#xa5;" k="65" />
    <hkern u1="&#xa2;" u2="z" k="19" />
    <hkern u1="&#xa2;" u2="y" k="65" />
    <hkern u1="&#xa2;" u2="x" k="47" />
    <hkern u1="&#xa2;" u2="w" k="28" />
    <hkern u1="&#xa2;" u2="v" k="28" />
    <hkern u1="&#xa2;" u2="t" k="19" />
    <hkern u1="&#xa2;" u2="q" k="-19" />
    <hkern u1="&#xa2;" u2="o" k="-19" />
    <hkern u1="&#xa2;" u2="g" k="-19" />
    <hkern u1="&#xa2;" u2="c" k="-19" />
    <hkern u1="&#xa2;" u2="a" k="47" />
    <hkern u1="&#xa2;" u2="Z" k="19" />
    <hkern u1="&#xa2;" u2="Y" k="65" />
    <hkern u1="&#xa2;" u2="X" k="47" />
    <hkern u1="&#xa2;" u2="W" k="28" />
    <hkern u1="&#xa2;" u2="V" k="28" />
    <hkern u1="&#xa2;" u2="T" k="19" />
    <hkern u1="&#xa2;" u2="Q" k="-19" />
    <hkern u1="&#xa2;" u2="O" k="-19" />
    <hkern u1="&#xa2;" u2="G" k="-19" />
    <hkern u1="&#xa2;" u2="C" k="-19" />
    <hkern u1="&#xa2;" u2="A" k="47" />
    <hkern u1="&#xa2;" u2="&#x40;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x38;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x36;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x30;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x26;" k="-19" />
    <hkern u1="&#xa2;" u2="&#x192;" k="19" />
    <hkern u1="&#xa2;" u2="&#xa3;" k="19" />
    <hkern u1="&#xa2;" u2="\" k="37" />
    <hkern u1="&#xa2;" u2="&#x37;" k="19" />
    <hkern u1="&#xa2;" u2="&#x2f;" k="37" />
    <hkern u1="&#xa3;" g2="nine.dnom" k="19" />
    <hkern u1="&#xa3;" g2="eight.dnom" k="19" />
    <hkern u1="&#xa3;" g2="seven.dnom" k="19" />
    <hkern u1="&#xa3;" g2="six.dnom" k="19" />
    <hkern u1="&#xa3;" g2="five.dnom" k="19" />
    <hkern u1="&#xa3;" g2="four.dnom" k="19" />
    <hkern u1="&#xa3;" g2="three.dnom" k="19" />
    <hkern u1="&#xa3;" g2="two.dnom" k="19" />
    <hkern u1="&#xa3;" g2="one.dnom" k="19" />
    <hkern u1="&#xa3;" g2="zero.dnom" k="19" />
    <hkern u1="&#xa3;" g2="j.ss01" k="19" />
    <hkern u1="&#xa3;" g2="ae.ss01" k="74" />
    <hkern u1="&#xa3;" g2="atilde.ss01" k="56" />
    <hkern u1="&#xa3;" g2="aring.ss01" k="56" />
    <hkern u1="&#xa3;" g2="aogonek.ss01" k="56" />
    <hkern u1="&#xa3;" g2="amacron.ss01" k="56" />
    <hkern u1="&#xa3;" g2="agrave.ss01" k="56" />
    <hkern u1="&#xa3;" g2="adieresis.ss01" k="56" />
    <hkern u1="&#xa3;" g2="acircumflex.ss01" k="56" />
    <hkern u1="&#xa3;" g2="abreve.ss01" k="56" />
    <hkern u1="&#xa3;" g2="aacute.ss01" k="56" />
    <hkern u1="&#xa3;" g2="a.ss01" k="56" />
    <hkern u1="&#xa3;" g2="J.ss01" k="19" />
    <hkern u1="&#xa3;" g2="AE.ss01" k="74" />
    <hkern u1="&#xa3;" g2="Atilde.ss01" k="56" />
    <hkern u1="&#xa3;" g2="Aring.ss01" k="56" />
    <hkern u1="&#xa3;" g2="Aogonek.ss01" k="56" />
    <hkern u1="&#xa3;" g2="Amacron.ss01" k="56" />
    <hkern u1="&#xa3;" g2="Agrave.ss01" k="56" />
    <hkern u1="&#xa3;" g2="Adieresis.ss01" k="56" />
    <hkern u1="&#xa3;" g2="Acircumflex.ss01" k="56" />
    <hkern u1="&#xa3;" g2="Abreve.ss01" k="56" />
    <hkern u1="&#xa3;" g2="Aacute.ss01" k="56" />
    <hkern u1="&#xa3;" g2="A.ss01" k="56" />
    <hkern u1="&#xa3;" u2="&#x2265;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2264;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2260;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2248;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2219;" k="37" />
    <hkern u1="&#xa3;" u2="&#x2212;" k="37" />
    <hkern u1="&#xa3;" u2="&#x2089;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2088;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2087;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2086;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2085;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2084;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2083;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2082;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2081;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2080;" k="19" />
    <hkern u1="&#xa3;" u2="&#x203a;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2039;" k="19" />
    <hkern u1="&#xa3;" u2="&#x2022;" k="37" />
    <hkern u1="&#xa3;" u2="&#x2014;" k="37" />
    <hkern u1="&#xa3;" u2="&#x2013;" k="37" />
    <hkern u1="&#xa3;" u2="&#x1ef9;" k="19" />
    <hkern u1="&#xa3;" u2="&#x1ef8;" k="19" />
    <hkern u1="&#xa3;" u2="&#x1ef7;" k="19" />
    <hkern u1="&#xa3;" u2="&#x1ef6;" k="19" />
    <hkern u1="&#xa3;" u2="&#x1ef5;" k="19" />
    <hkern u1="&#xa3;" u2="&#x1ef4;" k="19" />
    <hkern u1="&#xa3;" u2="&#x1ef3;" k="19" />
    <hkern u1="&#xa3;" u2="&#x1ef2;" k="19" />
    <hkern u1="&#xa3;" u2="&#x1eb7;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eb6;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eb5;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eb4;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eb3;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eb2;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eb1;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eb0;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eaf;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eae;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ead;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eac;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eab;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1eaa;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea9;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea8;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea7;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea6;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea5;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea4;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea3;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea2;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea1;" k="56" />
    <hkern u1="&#xa3;" u2="&#x1ea0;" k="56" />
    <hkern u1="&#xa3;" u2="&#x21b;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x21a;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x17e;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x17d;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x17c;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x17b;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x17a;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x179;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x178;" k="19" />
    <hkern u1="&#xa3;" u2="&#x165;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x164;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x163;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x162;" k="-19" />
    <hkern u1="&#xa3;" u2="&#x105;" k="56" />
    <hkern u1="&#xa3;" u2="&#x104;" k="56" />
    <hkern u1="&#xa3;" u2="&#x103;" k="56" />
    <hkern u1="&#xa3;" u2="&#x102;" k="56" />
    <hkern u1="&#xa3;" u2="&#x101;" k="56" />
    <hkern u1="&#xa3;" u2="&#x100;" k="56" />
    <hkern u1="&#xa3;" u2="&#xff;" k="19" />
    <hkern u1="&#xa3;" u2="&#xfd;" k="19" />
    <hkern u1="&#xa3;" u2="&#xf7;" k="19" />
    <hkern u1="&#xa3;" u2="&#xe6;" k="74" />
    <hkern u1="&#xa3;" u2="&#xe5;" k="56" />
    <hkern u1="&#xa3;" u2="&#xe4;" k="56" />
    <hkern u1="&#xa3;" u2="&#xe3;" k="56" />
    <hkern u1="&#xa3;" u2="&#xe2;" k="56" />
    <hkern u1="&#xa3;" u2="&#xe1;" k="56" />
    <hkern u1="&#xa3;" u2="&#xe0;" k="56" />
    <hkern u1="&#xa3;" u2="&#xdd;" k="19" />
    <hkern u1="&#xa3;" u2="&#xd7;" k="19" />
    <hkern u1="&#xa3;" u2="&#xc6;" k="74" />
    <hkern u1="&#xa3;" u2="&#xc5;" k="56" />
    <hkern u1="&#xa3;" u2="&#xc4;" k="56" />
    <hkern u1="&#xa3;" u2="&#xc3;" k="56" />
    <hkern u1="&#xa3;" u2="&#xc2;" k="56" />
    <hkern u1="&#xa3;" u2="&#xc1;" k="56" />
    <hkern u1="&#xa3;" u2="&#xc0;" k="56" />
    <hkern u1="&#xa3;" u2="&#xbb;" k="19" />
    <hkern u1="&#xa3;" u2="&#xb7;" k="37" />
    <hkern u1="&#xa3;" u2="&#xb1;" k="19" />
    <hkern u1="&#xa3;" u2="&#xad;" k="37" />
    <hkern u1="&#xa3;" u2="&#xac;" k="37" />
    <hkern u1="&#xa3;" u2="&#xab;" k="19" />
    <hkern u1="&#xa3;" u2="&#xa5;" k="19" />
    <hkern u1="&#xa3;" u2="&#x7e;" k="37" />
    <hkern u1="&#xa3;" u2="&#x7b;" k="37" />
    <hkern u1="&#xa3;" u2="z" k="-19" />
    <hkern u1="&#xa3;" u2="y" k="19" />
    <hkern u1="&#xa3;" u2="x" k="19" />
    <hkern u1="&#xa3;" u2="w" k="19" />
    <hkern u1="&#xa3;" u2="v" k="19" />
    <hkern u1="&#xa3;" u2="t" k="-19" />
    <hkern u1="&#xa3;" u2="j" k="19" />
    <hkern u1="&#xa3;" u2="a" k="56" />
    <hkern u1="&#xa3;" u2="Z" k="-19" />
    <hkern u1="&#xa3;" u2="Y" k="19" />
    <hkern u1="&#xa3;" u2="X" k="19" />
    <hkern u1="&#xa3;" u2="W" k="19" />
    <hkern u1="&#xa3;" u2="V" k="19" />
    <hkern u1="&#xa3;" u2="T" k="-19" />
    <hkern u1="&#xa3;" u2="J" k="19" />
    <hkern u1="&#xa3;" u2="A" k="56" />
    <hkern u1="&#xa3;" u2="&#x3e;" k="19" />
    <hkern u1="&#xa3;" u2="&#x3d;" k="19" />
    <hkern u1="&#xa3;" u2="&#x3c;" k="37" />
    <hkern u1="&#xa3;" u2="&#x2d;" k="37" />
    <hkern u1="&#xa3;" u2="&#x2b;" k="19" />
    <hkern u1="&#xa3;" u2="&#x192;" k="37" />
    <hkern u1="&#xa3;" u2="&#xa4;" k="19" />
    <hkern u1="&#xa3;" u2="&#xa3;" k="19" />
    <hkern u1="&#xa3;" u2="&#xa2;" k="19" />
    <hkern u1="&#xa3;" u2="\" k="19" />
    <hkern u1="&#xa3;" u2="&#x3a;" k="19" />
    <hkern u1="&#xa3;" u2="&#x34;" k="37" />
    <hkern u1="&#xa3;" u2="&#x2f;" k="19" />
    <hkern u1="&#xa4;" g2="j.ss01" k="19" />
    <hkern u1="&#xa4;" g2="ae.ss01" k="84" />
    <hkern u1="&#xa4;" g2="atilde.ss01" k="56" />
    <hkern u1="&#xa4;" g2="aring.ss01" k="56" />
    <hkern u1="&#xa4;" g2="aogonek.ss01" k="56" />
    <hkern u1="&#xa4;" g2="amacron.ss01" k="56" />
    <hkern u1="&#xa4;" g2="agrave.ss01" k="56" />
    <hkern u1="&#xa4;" g2="adieresis.ss01" k="56" />
    <hkern u1="&#xa4;" g2="acircumflex.ss01" k="56" />
    <hkern u1="&#xa4;" g2="abreve.ss01" k="56" />
    <hkern u1="&#xa4;" g2="aacute.ss01" k="56" />
    <hkern u1="&#xa4;" g2="a.ss01" k="56" />
    <hkern u1="&#xa4;" g2="i.loclTRK" k="19" />
    <hkern u1="&#xa4;" g2="J.ss01" k="19" />
    <hkern u1="&#xa4;" g2="AE.ss01" k="84" />
    <hkern u1="&#xa4;" g2="Atilde.ss01" k="56" />
    <hkern u1="&#xa4;" g2="Aring.ss01" k="56" />
    <hkern u1="&#xa4;" g2="Aogonek.ss01" k="56" />
    <hkern u1="&#xa4;" g2="Amacron.ss01" k="56" />
    <hkern u1="&#xa4;" g2="Agrave.ss01" k="56" />
    <hkern u1="&#xa4;" g2="Adieresis.ss01" k="56" />
    <hkern u1="&#xa4;" g2="Acircumflex.ss01" k="56" />
    <hkern u1="&#xa4;" g2="Abreve.ss01" k="56" />
    <hkern u1="&#xa4;" g2="Aacute.ss01" k="56" />
    <hkern u1="&#xa4;" g2="A.ss01" k="56" />
    <hkern u1="&#xa4;" g2="fi" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ef9;" k="84" />
    <hkern u1="&#xa4;" u2="&#x1ef8;" k="84" />
    <hkern u1="&#xa4;" u2="&#x1ef7;" k="84" />
    <hkern u1="&#xa4;" u2="&#x1ef6;" k="84" />
    <hkern u1="&#xa4;" u2="&#x1ef5;" k="84" />
    <hkern u1="&#xa4;" u2="&#x1ef4;" k="84" />
    <hkern u1="&#xa4;" u2="&#x1ef3;" k="84" />
    <hkern u1="&#xa4;" u2="&#x1ef2;" k="84" />
    <hkern u1="&#xa4;" u2="&#x1ecb;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1eca;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec9;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec8;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec7;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec6;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec5;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec4;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec3;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec2;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec1;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ec0;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ebf;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ebe;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ebd;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ebc;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1ebb;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1eba;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1eb9;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1eb8;" k="19" />
    <hkern u1="&#xa4;" u2="&#x1eb7;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eb6;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eb5;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eb4;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eb3;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eb2;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eb1;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eb0;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eaf;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eae;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ead;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eac;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eab;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1eaa;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea9;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea8;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea7;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea6;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea5;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea4;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea3;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea2;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea1;" k="56" />
    <hkern u1="&#xa4;" u2="&#x1ea0;" k="56" />
    <hkern u1="&#xa4;" u2="&#x21b;" k="28" />
    <hkern u1="&#xa4;" u2="&#x21a;" k="28" />
    <hkern u1="&#xa4;" u2="&#x17e;" k="37" />
    <hkern u1="&#xa4;" u2="&#x17d;" k="37" />
    <hkern u1="&#xa4;" u2="&#x17c;" k="37" />
    <hkern u1="&#xa4;" u2="&#x17b;" k="37" />
    <hkern u1="&#xa4;" u2="&#x17a;" k="37" />
    <hkern u1="&#xa4;" u2="&#x179;" k="37" />
    <hkern u1="&#xa4;" u2="&#x178;" k="84" />
    <hkern u1="&#xa4;" u2="&#x165;" k="28" />
    <hkern u1="&#xa4;" u2="&#x164;" k="28" />
    <hkern u1="&#xa4;" u2="&#x163;" k="28" />
    <hkern u1="&#xa4;" u2="&#x162;" k="28" />
    <hkern u1="&#xa4;" u2="&#x159;" k="19" />
    <hkern u1="&#xa4;" u2="&#x158;" k="19" />
    <hkern u1="&#xa4;" u2="&#x157;" k="19" />
    <hkern u1="&#xa4;" u2="&#x156;" k="19" />
    <hkern u1="&#xa4;" u2="&#x155;" k="19" />
    <hkern u1="&#xa4;" u2="&#x154;" k="19" />
    <hkern u1="&#xa4;" u2="&#x148;" k="19" />
    <hkern u1="&#xa4;" u2="&#x147;" k="19" />
    <hkern u1="&#xa4;" u2="&#x146;" k="19" />
    <hkern u1="&#xa4;" u2="&#x145;" k="19" />
    <hkern u1="&#xa4;" u2="&#x144;" k="19" />
    <hkern u1="&#xa4;" u2="&#x143;" k="19" />
    <hkern u1="&#xa4;" u2="&#x137;" k="19" />
    <hkern u1="&#xa4;" u2="&#x136;" k="19" />
    <hkern u1="&#xa4;" u2="&#x130;" k="19" />
    <hkern u1="&#xa4;" u2="&#x12f;" k="19" />
    <hkern u1="&#xa4;" u2="&#x12e;" k="19" />
    <hkern u1="&#xa4;" u2="&#x12b;" k="19" />
    <hkern u1="&#xa4;" u2="&#x12a;" k="19" />
    <hkern u1="&#xa4;" u2="&#x129;" k="19" />
    <hkern u1="&#xa4;" u2="&#x128;" k="19" />
    <hkern u1="&#xa4;" u2="&#x11b;" k="19" />
    <hkern u1="&#xa4;" u2="&#x11a;" k="19" />
    <hkern u1="&#xa4;" u2="&#x119;" k="19" />
    <hkern u1="&#xa4;" u2="&#x118;" k="19" />
    <hkern u1="&#xa4;" u2="&#x117;" k="19" />
    <hkern u1="&#xa4;" u2="&#x116;" k="19" />
    <hkern u1="&#xa4;" u2="&#x113;" k="19" />
    <hkern u1="&#xa4;" u2="&#x112;" k="19" />
    <hkern u1="&#xa4;" u2="&#x111;" k="19" />
    <hkern u1="&#xa4;" u2="&#x110;" k="19" />
    <hkern u1="&#xa4;" u2="&#x10f;" k="19" />
    <hkern u1="&#xa4;" u2="&#x10e;" k="19" />
    <hkern u1="&#xa4;" u2="&#x105;" k="56" />
    <hkern u1="&#xa4;" u2="&#x104;" k="56" />
    <hkern u1="&#xa4;" u2="&#x103;" k="56" />
    <hkern u1="&#xa4;" u2="&#x102;" k="56" />
    <hkern u1="&#xa4;" u2="&#x101;" k="56" />
    <hkern u1="&#xa4;" u2="&#x100;" k="56" />
    <hkern u1="&#xa4;" u2="&#xff;" k="84" />
    <hkern u1="&#xa4;" u2="&#xfd;" k="84" />
    <hkern u1="&#xa4;" u2="&#xf1;" k="19" />
    <hkern u1="&#xa4;" u2="&#xf0;" k="19" />
    <hkern u1="&#xa4;" u2="&#xef;" k="19" />
    <hkern u1="&#xa4;" u2="&#xee;" k="19" />
    <hkern u1="&#xa4;" u2="&#xed;" k="19" />
    <hkern u1="&#xa4;" u2="&#xec;" k="19" />
    <hkern u1="&#xa4;" u2="&#xeb;" k="19" />
    <hkern u1="&#xa4;" u2="&#xea;" k="19" />
    <hkern u1="&#xa4;" u2="&#xe9;" k="19" />
    <hkern u1="&#xa4;" u2="&#xe8;" k="19" />
    <hkern u1="&#xa4;" u2="&#xe6;" k="84" />
    <hkern u1="&#xa4;" u2="&#xe5;" k="56" />
    <hkern u1="&#xa4;" u2="&#xe4;" k="56" />
    <hkern u1="&#xa4;" u2="&#xe3;" k="56" />
    <hkern u1="&#xa4;" u2="&#xe2;" k="56" />
    <hkern u1="&#xa4;" u2="&#xe1;" k="56" />
    <hkern u1="&#xa4;" u2="&#xe0;" k="56" />
    <hkern u1="&#xa4;" u2="&#xdd;" k="84" />
    <hkern u1="&#xa4;" u2="&#xd1;" k="19" />
    <hkern u1="&#xa4;" u2="&#xd0;" k="19" />
    <hkern u1="&#xa4;" u2="&#xcf;" k="19" />
    <hkern u1="&#xa4;" u2="&#xce;" k="19" />
    <hkern u1="&#xa4;" u2="&#xcd;" k="19" />
    <hkern u1="&#xa4;" u2="&#xcc;" k="19" />
    <hkern u1="&#xa4;" u2="&#xcb;" k="19" />
    <hkern u1="&#xa4;" u2="&#xca;" k="19" />
    <hkern u1="&#xa4;" u2="&#xc9;" k="19" />
    <hkern u1="&#xa4;" u2="&#xc8;" k="19" />
    <hkern u1="&#xa4;" u2="&#xc6;" k="84" />
    <hkern u1="&#xa4;" u2="&#xc5;" k="56" />
    <hkern u1="&#xa4;" u2="&#xc4;" k="56" />
    <hkern u1="&#xa4;" u2="&#xc3;" k="56" />
    <hkern u1="&#xa4;" u2="&#xc2;" k="56" />
    <hkern u1="&#xa4;" u2="&#xc1;" k="56" />
    <hkern u1="&#xa4;" u2="&#xc0;" k="56" />
    <hkern u1="&#xa4;" u2="&#xa5;" k="84" />
    <hkern u1="&#xa4;" u2="z" k="37" />
    <hkern u1="&#xa4;" u2="y" k="84" />
    <hkern u1="&#xa4;" u2="x" k="65" />
    <hkern u1="&#xa4;" u2="w" k="47" />
    <hkern u1="&#xa4;" u2="v" k="47" />
    <hkern u1="&#xa4;" u2="t" k="28" />
    <hkern u1="&#xa4;" u2="r" k="19" />
    <hkern u1="&#xa4;" u2="p" k="19" />
    <hkern u1="&#xa4;" u2="n" k="19" />
    <hkern u1="&#xa4;" u2="m" k="19" />
    <hkern u1="&#xa4;" u2="k" k="19" />
    <hkern u1="&#xa4;" u2="j" k="19" />
    <hkern u1="&#xa4;" u2="i" k="19" />
    <hkern u1="&#xa4;" u2="h" k="19" />
    <hkern u1="&#xa4;" u2="f" k="19" />
    <hkern u1="&#xa4;" u2="e" k="19" />
    <hkern u1="&#xa4;" u2="d" k="19" />
    <hkern u1="&#xa4;" u2="b" k="19" />
    <hkern u1="&#xa4;" u2="a" k="56" />
    <hkern u1="&#xa4;" u2="Z" k="37" />
    <hkern u1="&#xa4;" u2="Y" k="84" />
    <hkern u1="&#xa4;" u2="X" k="65" />
    <hkern u1="&#xa4;" u2="W" k="47" />
    <hkern u1="&#xa4;" u2="V" k="47" />
    <hkern u1="&#xa4;" u2="T" k="28" />
    <hkern u1="&#xa4;" u2="R" k="19" />
    <hkern u1="&#xa4;" u2="P" k="19" />
    <hkern u1="&#xa4;" u2="N" k="19" />
    <hkern u1="&#xa4;" u2="M" k="19" />
    <hkern u1="&#xa4;" u2="K" k="19" />
    <hkern u1="&#xa4;" u2="J" k="19" />
    <hkern u1="&#xa4;" u2="I" k="19" />
    <hkern u1="&#xa4;" u2="H" k="19" />
    <hkern u1="&#xa4;" u2="F" k="19" />
    <hkern u1="&#xa4;" u2="E" k="19" />
    <hkern u1="&#xa4;" u2="D" k="19" />
    <hkern u1="&#xa4;" u2="B" k="19" />
    <hkern u1="&#xa4;" u2="A" k="56" />
    <hkern u1="&#xa4;" u2="&#x20ac;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ee3;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ee2;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ee1;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ee0;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1edf;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ede;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1edd;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1edc;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1edb;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1eda;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed9;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed8;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed7;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed6;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed5;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed4;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed3;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed2;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed1;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ed0;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ecf;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ece;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ecd;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1ecc;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1a1;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x1a0;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x153;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x152;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x151;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x150;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x14d;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x14c;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x123;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x122;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x11f;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x11e;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x10d;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x10c;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x107;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x106;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xf8;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xf6;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xf5;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xf4;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xf3;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xf2;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xe7;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xd8;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xd6;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xd5;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xd4;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xd3;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xd2;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xc7;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xa9;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x7d;" k="19" />
    <hkern u1="&#xa4;" u2="q" k="-19" />
    <hkern u1="&#xa4;" u2="o" k="-19" />
    <hkern u1="&#xa4;" u2="g" k="-19" />
    <hkern u1="&#xa4;" u2="c" k="-19" />
    <hkern u1="&#xa4;" u2="]" k="19" />
    <hkern u1="&#xa4;" u2="Q" k="-19" />
    <hkern u1="&#xa4;" u2="O" k="-19" />
    <hkern u1="&#xa4;" u2="G" k="-19" />
    <hkern u1="&#xa4;" u2="C" k="-19" />
    <hkern u1="&#xa4;" u2="&#x40;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x38;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x36;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x30;" k="-19" />
    <hkern u1="&#xa4;" u2="&#x29;" k="19" />
    <hkern u1="&#xa4;" u2="&#x26;" k="-19" />
    <hkern u1="&#xa4;" u2="&#xbf;" k="28" />
    <hkern u1="&#xa4;" u2="\" k="47" />
    <hkern u1="&#xa4;" u2="&#x3f;" k="19" />
    <hkern u1="&#xa4;" u2="&#x37;" k="28" />
    <hkern u1="&#xa4;" u2="&#x33;" k="19" />
    <hkern u1="&#xa4;" u2="&#x31;" k="19" />
    <hkern u1="&#xa4;" u2="&#x2f;" k="47" />
    <hkern u1="&#xa5;" u2="&#x221e;" k="74" />
    <hkern u1="&#xa5;" u2="&#x3c0;" k="47" />
    <hkern u1="&#xa5;" u2="&#x192;" k="121" />
    <hkern u1="&#xa5;" u2="&#xbf;" k="168" />
    <hkern u1="&#xa5;" u2="&#xb6;" k="19" />
    <hkern u1="&#xa5;" u2="&#xa4;" k="84" />
    <hkern u1="&#xa5;" u2="&#xa3;" k="93" />
    <hkern u1="&#xa5;" u2="&#xa2;" k="65" />
    <hkern u1="&#xa5;" u2="&#x3a;" k="47" />
    <hkern u1="&#xa5;" u2="&#x39;" k="19" />
    <hkern u1="&#xa5;" u2="&#x34;" k="130" />
    <hkern u1="&#xa5;" u2="&#x33;" k="19" />
    <hkern u1="&#xa5;" u2="&#x32;" k="19" />
    <hkern u1="&#xa5;" u2="&#x2f;" k="130" />
    <hkern u1="&#xa5;" u2="&#x24;" k="65" />
    <hkern u1="&#xa5;" u2="&#x23;" k="74" />
    <hkern u1="&#xa7;" u2="\" k="19" />
    <hkern u1="&#xa7;" u2="&#x2f;" k="19" />
    <hkern u1="&#xa8;" u2="&#x192;" k="56" />
    <hkern u1="&#xa8;" u2="&#xbf;" k="112" />
    <hkern u1="&#xa8;" u2="&#xa3;" k="37" />
    <hkern u1="&#xa8;" u2="&#x34;" k="74" />
    <hkern u1="&#xa8;" u2="&#x2f;" k="74" />
    <hkern u1="&#xa9;" u2="\" k="19" />
    <hkern u1="&#xa9;" u2="&#x2f;" k="19" />
    <hkern u1="&#xaa;" u2="&#xbf;" k="74" />
    <hkern u1="&#xaa;" u2="&#xa3;" k="19" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="74" />
    <hkern u1="&#xab;" u2="&#x192;" k="9" />
    <hkern u1="&#xab;" u2="&#xbf;" k="19" />
    <hkern u1="&#xab;" u2="\" k="28" />
    <hkern u1="&#xab;" u2="&#x3f;" k="9" />
    <hkern u1="&#xab;" u2="&#x37;" k="37" />
    <hkern u1="&#xab;" u2="&#x33;" k="9" />
    <hkern u1="&#xab;" u2="&#x2f;" k="28" />
    <hkern u1="&#xac;" u2="&#x192;" k="9" />
    <hkern u1="&#xac;" u2="&#xbf;" k="19" />
    <hkern u1="&#xac;" u2="\" k="28" />
    <hkern u1="&#xac;" u2="&#x3f;" k="9" />
    <hkern u1="&#xac;" u2="&#x37;" k="37" />
    <hkern u1="&#xac;" u2="&#x33;" k="9" />
    <hkern u1="&#xac;" u2="&#x2f;" k="28" />
    <hkern u1="&#xad;" u2="&#x192;" k="19" />
    <hkern u1="&#xad;" u2="&#xbf;" k="37" />
    <hkern u1="&#xad;" u2="\" k="56" />
    <hkern u1="&#xad;" u2="&#x3f;" k="19" />
    <hkern u1="&#xad;" u2="&#x37;" k="74" />
    <hkern u1="&#xad;" u2="&#x33;" k="19" />
    <hkern u1="&#xad;" u2="&#x2f;" k="56" />
    <hkern u1="&#xae;" u2="&#x192;" k="56" />
    <hkern u1="&#xae;" u2="&#xbf;" k="112" />
    <hkern u1="&#xae;" u2="&#xa3;" k="37" />
    <hkern u1="&#xae;" u2="&#x34;" k="74" />
    <hkern u1="&#xae;" u2="&#x2f;" k="74" />
    <hkern u1="&#xaf;" u2="&#x192;" k="56" />
    <hkern u1="&#xaf;" u2="&#xbf;" k="112" />
    <hkern u1="&#xaf;" u2="&#xa3;" k="37" />
    <hkern u1="&#xaf;" u2="&#x34;" k="74" />
    <hkern u1="&#xaf;" u2="&#x2f;" k="74" />
    <hkern u1="&#xb0;" u2="&#x192;" k="56" />
    <hkern u1="&#xb0;" u2="&#xbf;" k="112" />
    <hkern u1="&#xb0;" u2="&#xa3;" k="37" />
    <hkern u1="&#xb0;" u2="&#x34;" k="74" />
    <hkern u1="&#xb0;" u2="&#x2f;" k="74" />
    <hkern u1="&#xb1;" u2="&#x192;" k="9" />
    <hkern u1="&#xb1;" u2="&#xbf;" k="19" />
    <hkern u1="&#xb1;" u2="\" k="28" />
    <hkern u1="&#xb1;" u2="&#x3f;" k="9" />
    <hkern u1="&#xb1;" u2="&#x37;" k="37" />
    <hkern u1="&#xb1;" u2="&#x33;" k="9" />
    <hkern u1="&#xb1;" u2="&#x2f;" k="28" />
    <hkern u1="&#xb2;" u2="&#xbf;" k="74" />
    <hkern u1="&#xb2;" u2="&#xa3;" k="19" />
    <hkern u1="&#xb2;" u2="&#x2f;" k="74" />
    <hkern u1="&#xb3;" u2="&#xbf;" k="74" />
    <hkern u1="&#xb3;" u2="&#xa3;" k="19" />
    <hkern u1="&#xb3;" u2="&#x2f;" k="74" />
    <hkern u1="&#xb4;" u2="&#x192;" k="56" />
    <hkern u1="&#xb4;" u2="&#xbf;" k="112" />
    <hkern u1="&#xb4;" u2="&#xa3;" k="37" />
    <hkern u1="&#xb4;" u2="&#x34;" k="74" />
    <hkern u1="&#xb4;" u2="&#x2f;" k="74" />
    <hkern u1="&#xb6;" g2="ae.ss01" k="28" />
    <hkern u1="&#xb6;" g2="atilde.ss01" k="37" />
    <hkern u1="&#xb6;" g2="aring.ss01" k="37" />
    <hkern u1="&#xb6;" g2="aogonek.ss01" k="37" />
    <hkern u1="&#xb6;" g2="amacron.ss01" k="37" />
    <hkern u1="&#xb6;" g2="agrave.ss01" k="37" />
    <hkern u1="&#xb6;" g2="adieresis.ss01" k="37" />
    <hkern u1="&#xb6;" g2="acircumflex.ss01" k="37" />
    <hkern u1="&#xb6;" g2="abreve.ss01" k="37" />
    <hkern u1="&#xb6;" g2="aacute.ss01" k="37" />
    <hkern u1="&#xb6;" g2="a.ss01" k="37" />
    <hkern u1="&#xb6;" g2="AE.ss01" k="28" />
    <hkern u1="&#xb6;" g2="Atilde.ss01" k="37" />
    <hkern u1="&#xb6;" g2="Aring.ss01" k="37" />
    <hkern u1="&#xb6;" g2="Aogonek.ss01" k="37" />
    <hkern u1="&#xb6;" g2="Amacron.ss01" k="37" />
    <hkern u1="&#xb6;" g2="Agrave.ss01" k="37" />
    <hkern u1="&#xb6;" g2="Adieresis.ss01" k="37" />
    <hkern u1="&#xb6;" g2="Acircumflex.ss01" k="37" />
    <hkern u1="&#xb6;" g2="Abreve.ss01" k="37" />
    <hkern u1="&#xb6;" g2="Aacute.ss01" k="37" />
    <hkern u1="&#xb6;" g2="A.ss01" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eb7;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eb6;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eb5;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eb4;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eb3;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eb2;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eb1;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eb0;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eaf;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eae;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ead;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eac;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eab;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1eaa;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea9;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea8;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea7;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea6;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea5;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea4;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea3;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea2;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea1;" k="37" />
    <hkern u1="&#xb6;" u2="&#x1ea0;" k="37" />
    <hkern u1="&#xb6;" u2="&#x105;" k="37" />
    <hkern u1="&#xb6;" u2="&#x104;" k="37" />
    <hkern u1="&#xb6;" u2="&#x103;" k="37" />
    <hkern u1="&#xb6;" u2="&#x102;" k="37" />
    <hkern u1="&#xb6;" u2="&#x101;" k="37" />
    <hkern u1="&#xb6;" u2="&#x100;" k="37" />
    <hkern u1="&#xb6;" u2="&#xe6;" k="28" />
    <hkern u1="&#xb6;" u2="&#xe5;" k="37" />
    <hkern u1="&#xb6;" u2="&#xe4;" k="37" />
    <hkern u1="&#xb6;" u2="&#xe3;" k="37" />
    <hkern u1="&#xb6;" u2="&#xe2;" k="37" />
    <hkern u1="&#xb6;" u2="&#xe1;" k="37" />
    <hkern u1="&#xb6;" u2="&#xe0;" k="37" />
    <hkern u1="&#xb6;" u2="&#xc6;" k="28" />
    <hkern u1="&#xb6;" u2="&#xc5;" k="37" />
    <hkern u1="&#xb6;" u2="&#xc4;" k="37" />
    <hkern u1="&#xb6;" u2="&#xc3;" k="37" />
    <hkern u1="&#xb6;" u2="&#xc2;" k="37" />
    <hkern u1="&#xb6;" u2="&#xc1;" k="37" />
    <hkern u1="&#xb6;" u2="&#xc0;" k="37" />
    <hkern u1="&#xb6;" u2="a" k="37" />
    <hkern u1="&#xb6;" u2="A" k="37" />
    <hkern u1="&#xb6;" u2="&#x192;" k="19" />
    <hkern u1="&#xb6;" u2="&#xa4;" k="19" />
    <hkern u1="&#xb6;" u2="&#xa3;" k="19" />
    <hkern u1="&#xb6;" u2="&#x2f;" k="9" />
    <hkern u1="&#xb7;" u2="&#x192;" k="19" />
    <hkern u1="&#xb7;" u2="&#xbf;" k="37" />
    <hkern u1="&#xb7;" u2="\" k="56" />
    <hkern u1="&#xb7;" u2="&#x3f;" k="19" />
    <hkern u1="&#xb7;" u2="&#x37;" k="74" />
    <hkern u1="&#xb7;" u2="&#x33;" k="19" />
    <hkern u1="&#xb7;" u2="&#x2f;" k="56" />
    <hkern u1="&#xb8;" u2="&#xb6;" k="74" />
    <hkern u1="&#xb8;" u2="\" k="74" />
    <hkern u1="&#xb8;" u2="&#x3f;" k="74" />
    <hkern u1="&#xb8;" u2="&#x37;" k="93" />
    <hkern u1="&#xb8;" u2="&#x31;" k="37" />
    <hkern u1="&#xb8;" u2="&#x24;" k="9" />
    <hkern u1="&#xb9;" u2="&#xbf;" k="74" />
    <hkern u1="&#xb9;" u2="&#xa3;" k="19" />
    <hkern u1="&#xb9;" u2="&#x2f;" k="74" />
    <hkern u1="&#xba;" u2="&#xbf;" k="74" />
    <hkern u1="&#xba;" u2="&#xa3;" k="19" />
    <hkern u1="&#xba;" u2="&#x2f;" k="74" />
    <hkern u1="&#xbb;" u2="&#x192;" k="9" />
    <hkern u1="&#xbb;" u2="&#xbf;" k="19" />
    <hkern u1="&#xbb;" u2="\" k="28" />
    <hkern u1="&#xbb;" u2="&#x3f;" k="9" />
    <hkern u1="&#xbb;" u2="&#x37;" k="37" />
    <hkern u1="&#xbb;" u2="&#x33;" k="9" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="28" />
    <hkern u1="&#xbc;" u2="\" k="74" />
    <hkern u1="&#xbc;" u2="&#x3f;" k="37" />
    <hkern u1="&#xbc;" u2="&#x37;" k="93" />
    <hkern u1="&#xbc;" u2="&#x31;" k="37" />
    <hkern u1="&#xbd;" u2="\" k="74" />
    <hkern u1="&#xbd;" u2="&#x3f;" k="37" />
    <hkern u1="&#xbd;" u2="&#x37;" k="93" />
    <hkern u1="&#xbd;" u2="&#x31;" k="37" />
    <hkern u1="&#xbe;" u2="\" k="74" />
    <hkern u1="&#xbe;" u2="&#x3f;" k="37" />
    <hkern u1="&#xbe;" u2="&#x37;" k="93" />
    <hkern u1="&#xbe;" u2="&#x31;" k="37" />
    <hkern u1="&#xbf;" g2="caron.alt" k="74" />
    <hkern u1="&#xbf;" g2="nine.numr" k="37" />
    <hkern u1="&#xbf;" g2="eight.numr" k="37" />
    <hkern u1="&#xbf;" g2="seven.numr" k="37" />
    <hkern u1="&#xbf;" g2="six.numr" k="37" />
    <hkern u1="&#xbf;" g2="five.numr" k="37" />
    <hkern u1="&#xbf;" g2="four.numr" k="37" />
    <hkern u1="&#xbf;" g2="three.numr" k="37" />
    <hkern u1="&#xbf;" g2="two.numr" k="37" />
    <hkern u1="&#xbf;" g2="one.numr" k="37" />
    <hkern u1="&#xbf;" g2="zero.numr" k="37" />
    <hkern u1="&#xbf;" g2="pi.ss01" k="-37" />
    <hkern u1="&#xbf;" g2="uring.ss01" k="19" />
    <hkern u1="&#xbf;" g2="uogonek.ss01" k="19" />
    <hkern u1="&#xbf;" g2="umacron.ss01" k="19" />
    <hkern u1="&#xbf;" g2="uhungarumlaut.ss01" k="19" />
    <hkern u1="&#xbf;" g2="ugrave.ss01" k="19" />
    <hkern u1="&#xbf;" g2="udieresis.ss01" k="19" />
    <hkern u1="&#xbf;" g2="ucircumflex.ss01" k="19" />
    <hkern u1="&#xbf;" g2="uacute.ss01" k="19" />
    <hkern u1="&#xbf;" g2="u.ss01" k="19" />
    <hkern u1="&#xbf;" g2="Uring.ss01" k="19" />
    <hkern u1="&#xbf;" g2="Uogonek.ss01" k="19" />
    <hkern u1="&#xbf;" g2="Umacron.ss01" k="19" />
    <hkern u1="&#xbf;" g2="Uhungarumlaut.ss01" k="19" />
    <hkern u1="&#xbf;" g2="Ugrave.ss01" k="19" />
    <hkern u1="&#xbf;" g2="Udieresis.ss01" k="19" />
    <hkern u1="&#xbf;" g2="Ucircumflex.ss01" k="19" />
    <hkern u1="&#xbf;" g2="Uacute.ss01" k="19" />
    <hkern u1="&#xbf;" g2="U.ss01" k="19" />
    <hkern u1="&#xbf;" u2="&#x2122;" k="74" />
    <hkern u1="&#xbf;" u2="&#x20ac;" k="19" />
    <hkern u1="&#xbf;" u2="&#x2079;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2078;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2077;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2076;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2075;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2074;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2070;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2030;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2021;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2020;" k="74" />
    <hkern u1="&#xbf;" u2="&#x201d;" k="74" />
    <hkern u1="&#xbf;" u2="&#x201c;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2019;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2018;" k="74" />
    <hkern u1="&#xbf;" u2="&#x1ef9;" k="168" />
    <hkern u1="&#xbf;" u2="&#x1ef8;" k="168" />
    <hkern u1="&#xbf;" u2="&#x1ef7;" k="168" />
    <hkern u1="&#xbf;" u2="&#x1ef6;" k="168" />
    <hkern u1="&#xbf;" u2="&#x1ef5;" k="168" />
    <hkern u1="&#xbf;" u2="&#x1ef4;" k="168" />
    <hkern u1="&#xbf;" u2="&#x1ef3;" k="168" />
    <hkern u1="&#xbf;" u2="&#x1ef2;" k="168" />
    <hkern u1="&#xbf;" u2="&#x1ef1;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ef0;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1eef;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1eee;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1eed;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1eec;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1eeb;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1eea;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee9;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee8;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee7;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee6;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee5;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee4;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee3;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee2;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee1;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ee0;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1edf;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ede;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1edd;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1edc;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1edb;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1eda;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed9;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed8;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed7;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed6;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed5;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed4;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed3;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed2;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed1;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ed0;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ecf;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ece;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ecd;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1ecc;" k="19" />
    <hkern u1="&#xbf;" u2="&#x2dd;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2dc;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2da;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2d9;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2d8;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2c9;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2c7;" k="74" />
    <hkern u1="&#xbf;" u2="&#x2c6;" k="74" />
    <hkern u1="&#xbf;" u2="&#x21b;" k="112" />
    <hkern u1="&#xbf;" u2="&#x21a;" k="112" />
    <hkern u1="&#xbf;" u2="&#x219;" k="19" />
    <hkern u1="&#xbf;" u2="&#x218;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1b0;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1af;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1a1;" k="19" />
    <hkern u1="&#xbf;" u2="&#x1a0;" k="19" />
    <hkern u1="&#xbf;" u2="&#x178;" k="168" />
    <hkern u1="&#xbf;" u2="&#x173;" k="19" />
    <hkern u1="&#xbf;" u2="&#x172;" k="19" />
    <hkern u1="&#xbf;" u2="&#x171;" k="19" />
    <hkern u1="&#xbf;" u2="&#x170;" k="19" />
    <hkern u1="&#xbf;" u2="&#x16f;" k="19" />
    <hkern u1="&#xbf;" u2="&#x16e;" k="19" />
    <hkern u1="&#xbf;" u2="&#x16b;" k="19" />
    <hkern u1="&#xbf;" u2="&#x16a;" k="19" />
    <hkern u1="&#xbf;" u2="&#x169;" k="19" />
    <hkern u1="&#xbf;" u2="&#x168;" k="19" />
    <hkern u1="&#xbf;" u2="&#x165;" k="112" />
    <hkern u1="&#xbf;" u2="&#x164;" k="112" />
    <hkern u1="&#xbf;" u2="&#x163;" k="112" />
    <hkern u1="&#xbf;" u2="&#x162;" k="112" />
    <hkern u1="&#xbf;" u2="&#x161;" k="19" />
    <hkern u1="&#xbf;" u2="&#x160;" k="19" />
    <hkern u1="&#xbf;" u2="&#x15f;" k="19" />
    <hkern u1="&#xbf;" u2="&#x15e;" k="19" />
    <hkern u1="&#xbf;" u2="&#x15b;" k="19" />
    <hkern u1="&#xbf;" u2="&#x15a;" k="19" />
    <hkern u1="&#xbf;" u2="&#x153;" k="19" />
    <hkern u1="&#xbf;" u2="&#x152;" k="19" />
    <hkern u1="&#xbf;" u2="&#x151;" k="19" />
    <hkern u1="&#xbf;" u2="&#x150;" k="19" />
    <hkern u1="&#xbf;" u2="&#x14d;" k="19" />
    <hkern u1="&#xbf;" u2="&#x14c;" k="19" />
    <hkern u1="&#xbf;" u2="&#x123;" k="19" />
    <hkern u1="&#xbf;" u2="&#x122;" k="19" />
    <hkern u1="&#xbf;" u2="&#x11f;" k="19" />
    <hkern u1="&#xbf;" u2="&#x11e;" k="19" />
    <hkern u1="&#xbf;" u2="&#x10d;" k="19" />
    <hkern u1="&#xbf;" u2="&#x10c;" k="19" />
    <hkern u1="&#xbf;" u2="&#x107;" k="19" />
    <hkern u1="&#xbf;" u2="&#x106;" k="19" />
    <hkern u1="&#xbf;" u2="&#xff;" k="168" />
    <hkern u1="&#xbf;" u2="&#xfd;" k="168" />
    <hkern u1="&#xbf;" u2="&#xfc;" k="19" />
    <hkern u1="&#xbf;" u2="&#xfb;" k="19" />
    <hkern u1="&#xbf;" u2="&#xfa;" k="19" />
    <hkern u1="&#xbf;" u2="&#xf9;" k="19" />
    <hkern u1="&#xbf;" u2="&#xf8;" k="19" />
    <hkern u1="&#xbf;" u2="&#xf6;" k="19" />
    <hkern u1="&#xbf;" u2="&#xf5;" k="19" />
    <hkern u1="&#xbf;" u2="&#xf4;" k="19" />
    <hkern u1="&#xbf;" u2="&#xf3;" k="19" />
    <hkern u1="&#xbf;" u2="&#xf2;" k="19" />
    <hkern u1="&#xbf;" u2="&#xe7;" k="19" />
    <hkern u1="&#xbf;" u2="&#xdd;" k="168" />
    <hkern u1="&#xbf;" u2="&#xdc;" k="19" />
    <hkern u1="&#xbf;" u2="&#xdb;" k="19" />
    <hkern u1="&#xbf;" u2="&#xda;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd9;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd8;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd6;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd5;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd4;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd3;" k="19" />
    <hkern u1="&#xbf;" u2="&#xd2;" k="19" />
    <hkern u1="&#xbf;" u2="&#xc7;" k="19" />
    <hkern u1="&#xbf;" u2="&#xbe;" k="74" />
    <hkern u1="&#xbf;" u2="&#xbd;" k="74" />
    <hkern u1="&#xbf;" u2="&#xbc;" k="74" />
    <hkern u1="&#xbf;" u2="&#xba;" k="37" />
    <hkern u1="&#xbf;" u2="&#xb9;" k="37" />
    <hkern u1="&#xbf;" u2="&#xb4;" k="74" />
    <hkern u1="&#xbf;" u2="&#xb3;" k="37" />
    <hkern u1="&#xbf;" u2="&#xb2;" k="37" />
    <hkern u1="&#xbf;" u2="&#xb0;" k="74" />
    <hkern u1="&#xbf;" u2="&#xaf;" k="74" />
    <hkern u1="&#xbf;" u2="&#xae;" k="74" />
    <hkern u1="&#xbf;" u2="&#xaa;" k="37" />
    <hkern u1="&#xbf;" u2="&#xa9;" k="19" />
    <hkern u1="&#xbf;" u2="&#xa8;" k="74" />
    <hkern u1="&#xbf;" u2="&#xa7;" k="19" />
    <hkern u1="&#xbf;" u2="&#xa5;" k="168" />
    <hkern u1="&#xbf;" u2="&#xa1;" k="-37" />
    <hkern u1="&#xbf;" u2="y" k="168" />
    <hkern u1="&#xbf;" u2="w" k="112" />
    <hkern u1="&#xbf;" u2="v" k="112" />
    <hkern u1="&#xbf;" u2="u" k="19" />
    <hkern u1="&#xbf;" u2="t" k="112" />
    <hkern u1="&#xbf;" u2="s" k="19" />
    <hkern u1="&#xbf;" u2="q" k="19" />
    <hkern u1="&#xbf;" u2="o" k="19" />
    <hkern u1="&#xbf;" u2="g" k="19" />
    <hkern u1="&#xbf;" u2="c" k="19" />
    <hkern u1="&#xbf;" u2="`" k="74" />
    <hkern u1="&#xbf;" u2="^" k="74" />
    <hkern u1="&#xbf;" u2="Y" k="168" />
    <hkern u1="&#xbf;" u2="W" k="112" />
    <hkern u1="&#xbf;" u2="V" k="112" />
    <hkern u1="&#xbf;" u2="U" k="19" />
    <hkern u1="&#xbf;" u2="T" k="112" />
    <hkern u1="&#xbf;" u2="S" k="19" />
    <hkern u1="&#xbf;" u2="Q" k="19" />
    <hkern u1="&#xbf;" u2="O" k="19" />
    <hkern u1="&#xbf;" u2="G" k="19" />
    <hkern u1="&#xbf;" u2="C" k="19" />
    <hkern u1="&#xbf;" u2="&#x40;" k="19" />
    <hkern u1="&#xbf;" u2="&#x3b;" k="-37" />
    <hkern u1="&#xbf;" u2="&#x38;" k="19" />
    <hkern u1="&#xbf;" u2="&#x36;" k="19" />
    <hkern u1="&#xbf;" u2="&#x35;" k="19" />
    <hkern u1="&#xbf;" u2="&#x30;" k="19" />
    <hkern u1="&#xbf;" u2="&#x2a;" k="74" />
    <hkern u1="&#xbf;" u2="&#x27;" k="74" />
    <hkern u1="&#xbf;" u2="&#x26;" k="19" />
    <hkern u1="&#xbf;" u2="&#x25;" k="74" />
    <hkern u1="&#xbf;" u2="&#x22;" k="74" />
    <hkern u1="&#xbf;" u2="&#x221e;" k="19" />
    <hkern u1="&#xbf;" u2="&#x192;" k="19" />
    <hkern u1="&#xbf;" u2="&#xb6;" k="74" />
    <hkern u1="&#xbf;" u2="&#xa4;" k="37" />
    <hkern u1="&#xbf;" u2="&#xa3;" k="19" />
    <hkern u1="&#xbf;" u2="&#xa2;" k="37" />
    <hkern u1="&#xbf;" u2="\" k="130" />
    <hkern u1="&#xbf;" u2="&#x3f;" k="74" />
    <hkern u1="&#xbf;" u2="&#x39;" k="19" />
    <hkern u1="&#xbf;" u2="&#x37;" k="112" />
    <hkern u1="&#xbf;" u2="&#x34;" k="47" />
    <hkern u1="&#xbf;" u2="&#x33;" k="19" />
    <hkern u1="&#xbf;" u2="&#x31;" k="37" />
    <hkern u1="&#xbf;" u2="&#x2f;" k="-37" />
    <hkern u1="&#xbf;" u2="&#x24;" k="56" />
    <hkern u1="&#xbf;" u2="&#x23;" k="37" />
    <hkern u1="&#xc0;" u2="&#x221e;" k="37" />
    <hkern u1="&#xc0;" u2="&#x192;" k="37" />
    <hkern u1="&#xc0;" u2="&#xb6;" k="65" />
    <hkern u1="&#xc0;" u2="&#xa4;" k="56" />
    <hkern u1="&#xc0;" u2="&#xa3;" k="19" />
    <hkern u1="&#xc0;" u2="&#xa2;" k="47" />
    <hkern u1="&#xc0;" u2="\" k="112" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="84" />
    <hkern u1="&#xc0;" u2="&#x3a;" k="28" />
    <hkern u1="&#xc0;" u2="&#x37;" k="112" />
    <hkern u1="&#xc0;" u2="&#x34;" k="19" />
    <hkern u1="&#xc0;" u2="&#x24;" k="74" />
    <hkern u1="&#xc0;" u2="&#x23;" k="37" />
    <hkern u1="&#xc1;" u2="&#x221e;" k="37" />
    <hkern u1="&#xc1;" u2="&#x192;" k="37" />
    <hkern u1="&#xc1;" u2="&#xb6;" k="65" />
    <hkern u1="&#xc1;" u2="&#xa4;" k="56" />
    <hkern u1="&#xc1;" u2="&#xa3;" k="19" />
    <hkern u1="&#xc1;" u2="&#xa2;" k="47" />
    <hkern u1="&#xc1;" u2="\" k="112" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="84" />
    <hkern u1="&#xc1;" u2="&#x3a;" k="28" />
    <hkern u1="&#xc1;" u2="&#x37;" k="112" />
    <hkern u1="&#xc1;" u2="&#x34;" k="19" />
    <hkern u1="&#xc1;" u2="&#x24;" k="74" />
    <hkern u1="&#xc1;" u2="&#x23;" k="37" />
    <hkern u1="&#xc2;" u2="&#x221e;" k="37" />
    <hkern u1="&#xc2;" u2="&#x192;" k="37" />
    <hkern u1="&#xc2;" u2="&#xb6;" k="65" />
    <hkern u1="&#xc2;" u2="&#xa4;" k="56" />
    <hkern u1="&#xc2;" u2="&#xa3;" k="19" />
    <hkern u1="&#xc2;" u2="&#xa2;" k="47" />
    <hkern u1="&#xc2;" u2="\" k="112" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="84" />
    <hkern u1="&#xc2;" u2="&#x3a;" k="28" />
    <hkern u1="&#xc2;" u2="&#x37;" k="112" />
    <hkern u1="&#xc2;" u2="&#x34;" k="19" />
    <hkern u1="&#xc2;" u2="&#x24;" k="74" />
    <hkern u1="&#xc2;" u2="&#x23;" k="37" />
    <hkern u1="&#xc3;" u2="&#x221e;" k="37" />
    <hkern u1="&#xc3;" u2="&#x192;" k="37" />
    <hkern u1="&#xc3;" u2="&#xb6;" k="65" />
    <hkern u1="&#xc3;" u2="&#xa4;" k="56" />
    <hkern u1="&#xc3;" u2="&#xa3;" k="19" />
    <hkern u1="&#xc3;" u2="&#xa2;" k="47" />
    <hkern u1="&#xc3;" u2="\" k="112" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="84" />
    <hkern u1="&#xc3;" u2="&#x3a;" k="28" />
    <hkern u1="&#xc3;" u2="&#x37;" k="112" />
    <hkern u1="&#xc3;" u2="&#x34;" k="19" />
    <hkern u1="&#xc3;" u2="&#x24;" k="74" />
    <hkern u1="&#xc3;" u2="&#x23;" k="37" />
    <hkern u1="&#xc4;" u2="&#x221e;" k="37" />
    <hkern u1="&#xc4;" u2="&#x192;" k="37" />
    <hkern u1="&#xc4;" u2="&#xb6;" k="65" />
    <hkern u1="&#xc4;" u2="&#xa4;" k="56" />
    <hkern u1="&#xc4;" u2="&#xa3;" k="19" />
    <hkern u1="&#xc4;" u2="&#xa2;" k="47" />
    <hkern u1="&#xc4;" u2="\" k="112" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="84" />
    <hkern u1="&#xc4;" u2="&#x3a;" k="28" />
    <hkern u1="&#xc4;" u2="&#x37;" k="112" />
    <hkern u1="&#xc4;" u2="&#x34;" k="19" />
    <hkern u1="&#xc4;" u2="&#x24;" k="74" />
    <hkern u1="&#xc4;" u2="&#x23;" k="37" />
    <hkern u1="&#xc5;" u2="&#x221e;" k="37" />
    <hkern u1="&#xc5;" u2="&#x192;" k="37" />
    <hkern u1="&#xc5;" u2="&#xb6;" k="65" />
    <hkern u1="&#xc5;" u2="&#xa4;" k="56" />
    <hkern u1="&#xc5;" u2="&#xa3;" k="19" />
    <hkern u1="&#xc5;" u2="&#xa2;" k="47" />
    <hkern u1="&#xc5;" u2="\" k="112" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="84" />
    <hkern u1="&#xc5;" u2="&#x3a;" k="28" />
    <hkern u1="&#xc5;" u2="&#x37;" k="112" />
    <hkern u1="&#xc5;" u2="&#x34;" k="19" />
    <hkern u1="&#xc5;" u2="&#x24;" k="74" />
    <hkern u1="&#xc5;" u2="&#x23;" k="37" />
    <hkern u1="&#xc6;" u2="&#x221e;" k="19" />
    <hkern u1="&#xc6;" u2="&#x192;" k="19" />
    <hkern u1="&#xc6;" u2="&#xb6;" k="19" />
    <hkern u1="&#xc6;" u2="&#xa4;" k="28" />
    <hkern u1="&#xc6;" u2="&#xa2;" k="19" />
    <hkern u1="&#xc6;" u2="&#x3a;" k="19" />
    <hkern u1="&#xc6;" u2="&#x24;" k="19" />
    <hkern u1="&#xc6;" u2="&#x23;" k="19" />
    <hkern u1="&#xc7;" u2="\" k="19" />
    <hkern u1="&#xc7;" u2="&#x2f;" k="19" />
    <hkern u1="&#xc8;" u2="&#x221e;" k="19" />
    <hkern u1="&#xc8;" u2="&#x192;" k="19" />
    <hkern u1="&#xc8;" u2="&#xb6;" k="19" />
    <hkern u1="&#xc8;" u2="&#xa4;" k="28" />
    <hkern u1="&#xc8;" u2="&#xa2;" k="19" />
    <hkern u1="&#xc8;" u2="&#x3a;" k="19" />
    <hkern u1="&#xc8;" u2="&#x24;" k="19" />
    <hkern u1="&#xc8;" u2="&#x23;" k="19" />
    <hkern u1="&#xc9;" u2="&#x221e;" k="19" />
    <hkern u1="&#xc9;" u2="&#x192;" k="19" />
    <hkern u1="&#xc9;" u2="&#xb6;" k="19" />
    <hkern u1="&#xc9;" u2="&#xa4;" k="28" />
    <hkern u1="&#xc9;" u2="&#xa2;" k="19" />
    <hkern u1="&#xc9;" u2="&#x3a;" k="19" />
    <hkern u1="&#xc9;" u2="&#x24;" k="19" />
    <hkern u1="&#xc9;" u2="&#x23;" k="19" />
    <hkern u1="&#xca;" u2="&#x221e;" k="19" />
    <hkern u1="&#xca;" u2="&#x192;" k="19" />
    <hkern u1="&#xca;" u2="&#xb6;" k="19" />
    <hkern u1="&#xca;" u2="&#xa4;" k="28" />
    <hkern u1="&#xca;" u2="&#xa2;" k="19" />
    <hkern u1="&#xca;" u2="&#x3a;" k="19" />
    <hkern u1="&#xca;" u2="&#x24;" k="19" />
    <hkern u1="&#xca;" u2="&#x23;" k="19" />
    <hkern u1="&#xcb;" u2="&#x221e;" k="19" />
    <hkern u1="&#xcb;" u2="&#x192;" k="19" />
    <hkern u1="&#xcb;" u2="&#xb6;" k="19" />
    <hkern u1="&#xcb;" u2="&#xa4;" k="28" />
    <hkern u1="&#xcb;" u2="&#xa2;" k="19" />
    <hkern u1="&#xcb;" u2="&#x3a;" k="19" />
    <hkern u1="&#xcb;" u2="&#x24;" k="19" />
    <hkern u1="&#xcb;" u2="&#x23;" k="19" />
    <hkern u1="&#xd0;" u2="\" k="19" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="19" />
    <hkern u1="&#xd2;" u2="\" k="19" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="19" />
    <hkern u1="&#xd3;" u2="\" k="19" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="19" />
    <hkern u1="&#xd4;" u2="\" k="19" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="19" />
    <hkern u1="&#xd5;" u2="\" k="19" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="19" />
    <hkern u1="&#xd6;" u2="\" k="19" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="19" />
    <hkern u1="&#xd7;" u2="&#x192;" k="9" />
    <hkern u1="&#xd7;" u2="&#xbf;" k="19" />
    <hkern u1="&#xd7;" u2="\" k="28" />
    <hkern u1="&#xd7;" u2="&#x3f;" k="9" />
    <hkern u1="&#xd7;" u2="&#x37;" k="37" />
    <hkern u1="&#xd7;" u2="&#x33;" k="9" />
    <hkern u1="&#xd7;" u2="&#x2f;" k="28" />
    <hkern u1="&#xd8;" u2="\" k="19" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="19" />
    <hkern u1="&#xdd;" u2="&#x221e;" k="74" />
    <hkern u1="&#xdd;" u2="&#x3c0;" k="47" />
    <hkern u1="&#xdd;" u2="&#x192;" k="121" />
    <hkern u1="&#xdd;" u2="&#xbf;" k="168" />
    <hkern u1="&#xdd;" u2="&#xb6;" k="19" />
    <hkern u1="&#xdd;" u2="&#xa4;" k="84" />
    <hkern u1="&#xdd;" u2="&#xa3;" k="93" />
    <hkern u1="&#xdd;" u2="&#xa2;" k="65" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="47" />
    <hkern u1="&#xdd;" u2="&#x39;" k="19" />
    <hkern u1="&#xdd;" u2="&#x34;" k="130" />
    <hkern u1="&#xdd;" u2="&#x33;" k="19" />
    <hkern u1="&#xdd;" u2="&#x32;" k="19" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="130" />
    <hkern u1="&#xdd;" u2="&#x24;" k="65" />
    <hkern u1="&#xdd;" u2="&#x23;" k="74" />
    <hkern u1="&#xde;" u2="&#x192;" k="19" />
    <hkern u1="&#xde;" u2="\" k="37" />
    <hkern u1="&#xde;" u2="&#x37;" k="19" />
    <hkern u1="&#xde;" u2="&#x2f;" k="37" />
    <hkern u1="&#xdf;" u2="\" k="19" />
    <hkern u1="&#xdf;" u2="&#x2f;" k="19" />
    <hkern u1="&#xe0;" u2="&#x221e;" k="37" />
    <hkern u1="&#xe0;" u2="&#x192;" k="37" />
    <hkern u1="&#xe0;" u2="&#xb6;" k="65" />
    <hkern u1="&#xe0;" u2="&#xa4;" k="56" />
    <hkern u1="&#xe0;" u2="&#xa3;" k="19" />
    <hkern u1="&#xe0;" u2="&#xa2;" k="47" />
    <hkern u1="&#xe0;" u2="\" k="112" />
    <hkern u1="&#xe0;" u2="&#x3f;" k="84" />
    <hkern u1="&#xe0;" u2="&#x3a;" k="28" />
    <hkern u1="&#xe0;" u2="&#x37;" k="112" />
    <hkern u1="&#xe0;" u2="&#x34;" k="19" />
    <hkern u1="&#xe0;" u2="&#x24;" k="74" />
    <hkern u1="&#xe0;" u2="&#x23;" k="37" />
    <hkern u1="&#xe1;" u2="&#x221e;" k="37" />
    <hkern u1="&#xe1;" u2="&#x192;" k="37" />
    <hkern u1="&#xe1;" u2="&#xb6;" k="65" />
    <hkern u1="&#xe1;" u2="&#xa4;" k="56" />
    <hkern u1="&#xe1;" u2="&#xa3;" k="19" />
    <hkern u1="&#xe1;" u2="&#xa2;" k="47" />
    <hkern u1="&#xe1;" u2="\" k="112" />
    <hkern u1="&#xe1;" u2="&#x3f;" k="84" />
    <hkern u1="&#xe1;" u2="&#x3a;" k="28" />
    <hkern u1="&#xe1;" u2="&#x37;" k="112" />
    <hkern u1="&#xe1;" u2="&#x34;" k="19" />
    <hkern u1="&#xe1;" u2="&#x24;" k="74" />
    <hkern u1="&#xe1;" u2="&#x23;" k="37" />
    <hkern u1="&#xe2;" u2="&#x221e;" k="37" />
    <hkern u1="&#xe2;" u2="&#x192;" k="37" />
    <hkern u1="&#xe2;" u2="&#xb6;" k="65" />
    <hkern u1="&#xe2;" u2="&#xa4;" k="56" />
    <hkern u1="&#xe2;" u2="&#xa3;" k="19" />
    <hkern u1="&#xe2;" u2="&#xa2;" k="47" />
    <hkern u1="&#xe2;" u2="\" k="112" />
    <hkern u1="&#xe2;" u2="&#x3f;" k="84" />
    <hkern u1="&#xe2;" u2="&#x3a;" k="28" />
    <hkern u1="&#xe2;" u2="&#x37;" k="112" />
    <hkern u1="&#xe2;" u2="&#x34;" k="19" />
    <hkern u1="&#xe2;" u2="&#x24;" k="74" />
    <hkern u1="&#xe2;" u2="&#x23;" k="37" />
    <hkern u1="&#xe3;" u2="&#x221e;" k="37" />
    <hkern u1="&#xe3;" u2="&#x192;" k="37" />
    <hkern u1="&#xe3;" u2="&#xb6;" k="65" />
    <hkern u1="&#xe3;" u2="&#xa4;" k="56" />
    <hkern u1="&#xe3;" u2="&#xa3;" k="19" />
    <hkern u1="&#xe3;" u2="&#xa2;" k="47" />
    <hkern u1="&#xe3;" u2="\" k="112" />
    <hkern u1="&#xe3;" u2="&#x3f;" k="84" />
    <hkern u1="&#xe3;" u2="&#x3a;" k="28" />
    <hkern u1="&#xe3;" u2="&#x37;" k="112" />
    <hkern u1="&#xe3;" u2="&#x34;" k="19" />
    <hkern u1="&#xe3;" u2="&#x24;" k="74" />
    <hkern u1="&#xe3;" u2="&#x23;" k="37" />
    <hkern u1="&#xe4;" u2="&#x221e;" k="37" />
    <hkern u1="&#xe4;" u2="&#x192;" k="37" />
    <hkern u1="&#xe4;" u2="&#xb6;" k="65" />
    <hkern u1="&#xe4;" u2="&#xa4;" k="56" />
    <hkern u1="&#xe4;" u2="&#xa3;" k="19" />
    <hkern u1="&#xe4;" u2="&#xa2;" k="47" />
    <hkern u1="&#xe4;" u2="\" k="112" />
    <hkern u1="&#xe4;" u2="&#x3f;" k="84" />
    <hkern u1="&#xe4;" u2="&#x3a;" k="28" />
    <hkern u1="&#xe4;" u2="&#x37;" k="112" />
    <hkern u1="&#xe4;" u2="&#x34;" k="19" />
    <hkern u1="&#xe4;" u2="&#x24;" k="74" />
    <hkern u1="&#xe4;" u2="&#x23;" k="37" />
    <hkern u1="&#xe5;" u2="&#x221e;" k="37" />
    <hkern u1="&#xe5;" u2="&#x192;" k="37" />
    <hkern u1="&#xe5;" u2="&#xb6;" k="65" />
    <hkern u1="&#xe5;" u2="&#xa4;" k="56" />
    <hkern u1="&#xe5;" u2="&#xa3;" k="19" />
    <hkern u1="&#xe5;" u2="&#xa2;" k="47" />
    <hkern u1="&#xe5;" u2="\" k="112" />
    <hkern u1="&#xe5;" u2="&#x3f;" k="84" />
    <hkern u1="&#xe5;" u2="&#x3a;" k="28" />
    <hkern u1="&#xe5;" u2="&#x37;" k="112" />
    <hkern u1="&#xe5;" u2="&#x34;" k="19" />
    <hkern u1="&#xe5;" u2="&#x24;" k="74" />
    <hkern u1="&#xe5;" u2="&#x23;" k="37" />
    <hkern u1="&#xe6;" u2="&#x221e;" k="19" />
    <hkern u1="&#xe6;" u2="&#x192;" k="19" />
    <hkern u1="&#xe6;" u2="&#xb6;" k="19" />
    <hkern u1="&#xe6;" u2="&#xa4;" k="28" />
    <hkern u1="&#xe6;" u2="&#xa2;" k="19" />
    <hkern u1="&#xe6;" u2="&#x3a;" k="19" />
    <hkern u1="&#xe6;" u2="&#x24;" k="19" />
    <hkern u1="&#xe6;" u2="&#x23;" k="19" />
    <hkern u1="&#xe7;" u2="\" k="19" />
    <hkern u1="&#xe7;" u2="&#x2f;" k="19" />
    <hkern u1="&#xe8;" u2="&#x221e;" k="19" />
    <hkern u1="&#xe8;" u2="&#x192;" k="19" />
    <hkern u1="&#xe8;" u2="&#xb6;" k="19" />
    <hkern u1="&#xe8;" u2="&#xa4;" k="28" />
    <hkern u1="&#xe8;" u2="&#xa2;" k="19" />
    <hkern u1="&#xe8;" u2="&#x3a;" k="19" />
    <hkern u1="&#xe8;" u2="&#x24;" k="19" />
    <hkern u1="&#xe8;" u2="&#x23;" k="19" />
    <hkern u1="&#xe9;" u2="&#x221e;" k="19" />
    <hkern u1="&#xe9;" u2="&#x192;" k="19" />
    <hkern u1="&#xe9;" u2="&#xb6;" k="19" />
    <hkern u1="&#xe9;" u2="&#xa4;" k="28" />
    <hkern u1="&#xe9;" u2="&#xa2;" k="19" />
    <hkern u1="&#xe9;" u2="&#x3a;" k="19" />
    <hkern u1="&#xe9;" u2="&#x24;" k="19" />
    <hkern u1="&#xe9;" u2="&#x23;" k="19" />
    <hkern u1="&#xea;" u2="&#x221e;" k="19" />
    <hkern u1="&#xea;" u2="&#x192;" k="19" />
    <hkern u1="&#xea;" u2="&#xb6;" k="19" />
    <hkern u1="&#xea;" u2="&#xa4;" k="28" />
    <hkern u1="&#xea;" u2="&#xa2;" k="19" />
    <hkern u1="&#xea;" u2="&#x3a;" k="19" />
    <hkern u1="&#xea;" u2="&#x24;" k="19" />
    <hkern u1="&#xea;" u2="&#x23;" k="19" />
    <hkern u1="&#xeb;" u2="&#x221e;" k="19" />
    <hkern u1="&#xeb;" u2="&#x192;" k="19" />
    <hkern u1="&#xeb;" u2="&#xb6;" k="19" />
    <hkern u1="&#xeb;" u2="&#xa4;" k="28" />
    <hkern u1="&#xeb;" u2="&#xa2;" k="19" />
    <hkern u1="&#xeb;" u2="&#x3a;" k="19" />
    <hkern u1="&#xeb;" u2="&#x24;" k="19" />
    <hkern u1="&#xeb;" u2="&#x23;" k="19" />
    <hkern u1="&#xf0;" u2="&#x2f;" k="19" />
    <hkern u1="&#xf2;" u2="\" k="19" />
    <hkern u1="&#xf2;" u2="&#x2f;" k="19" />
    <hkern u1="&#xf3;" u2="\" k="19" />
    <hkern u1="&#xf3;" u2="&#x2f;" k="19" />
    <hkern u1="&#xf4;" u2="\" k="19" />
    <hkern u1="&#xf4;" u2="&#x2f;" k="19" />
    <hkern u1="&#xf5;" u2="\" k="19" />
    <hkern u1="&#xf5;" u2="&#x2f;" k="19" />
    <hkern u1="&#xf6;" u2="\" k="19" />
    <hkern u1="&#xf6;" u2="&#x2f;" k="19" />
    <hkern u1="&#xf7;" u2="&#x192;" k="9" />
    <hkern u1="&#xf7;" u2="&#xbf;" k="19" />
    <hkern u1="&#xf7;" u2="\" k="28" />
    <hkern u1="&#xf7;" u2="&#x3f;" k="9" />
    <hkern u1="&#xf7;" u2="&#x37;" k="37" />
    <hkern u1="&#xf7;" u2="&#x33;" k="9" />
    <hkern u1="&#xf7;" u2="&#x2f;" k="28" />
    <hkern u1="&#xf8;" u2="\" k="19" />
    <hkern u1="&#xf8;" u2="&#x2f;" k="19" />
    <hkern u1="&#xfd;" u2="&#x221e;" k="74" />
    <hkern u1="&#xfd;" u2="&#x3c0;" k="47" />
    <hkern u1="&#xfd;" u2="&#x192;" k="121" />
    <hkern u1="&#xfd;" u2="&#xbf;" k="168" />
    <hkern u1="&#xfd;" u2="&#xb6;" k="19" />
    <hkern u1="&#xfd;" u2="&#xa4;" k="84" />
    <hkern u1="&#xfd;" u2="&#xa3;" k="93" />
    <hkern u1="&#xfd;" u2="&#xa2;" k="65" />
    <hkern u1="&#xfd;" u2="&#x3a;" k="47" />
    <hkern u1="&#xfd;" u2="&#x39;" k="19" />
    <hkern u1="&#xfd;" u2="&#x34;" k="130" />
    <hkern u1="&#xfd;" u2="&#x33;" k="19" />
    <hkern u1="&#xfd;" u2="&#x32;" k="19" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="130" />
    <hkern u1="&#xfd;" u2="&#x24;" k="65" />
    <hkern u1="&#xfd;" u2="&#x23;" k="74" />
    <hkern u1="&#xfe;" u2="&#x192;" k="19" />
    <hkern u1="&#xfe;" u2="\" k="37" />
    <hkern u1="&#xfe;" u2="&#x37;" k="19" />
    <hkern u1="&#xfe;" u2="&#x2f;" k="37" />
    <hkern u1="&#xff;" u2="&#x221e;" k="74" />
    <hkern u1="&#xff;" u2="&#x3c0;" k="47" />
    <hkern u1="&#xff;" u2="&#x192;" k="121" />
    <hkern u1="&#xff;" u2="&#xbf;" k="168" />
    <hkern u1="&#xff;" u2="&#xb6;" k="19" />
    <hkern u1="&#xff;" u2="&#xa4;" k="84" />
    <hkern u1="&#xff;" u2="&#xa3;" k="93" />
    <hkern u1="&#xff;" u2="&#xa2;" k="65" />
    <hkern u1="&#xff;" u2="&#x3a;" k="47" />
    <hkern u1="&#xff;" u2="&#x39;" k="19" />
    <hkern u1="&#xff;" u2="&#x34;" k="130" />
    <hkern u1="&#xff;" u2="&#x33;" k="19" />
    <hkern u1="&#xff;" u2="&#x32;" k="19" />
    <hkern u1="&#xff;" u2="&#x2f;" k="130" />
    <hkern u1="&#xff;" u2="&#x24;" k="65" />
    <hkern u1="&#xff;" u2="&#x23;" k="74" />
    <hkern u1="&#x100;" u2="&#x221e;" k="37" />
    <hkern u1="&#x100;" u2="&#x192;" k="37" />
    <hkern u1="&#x100;" u2="&#xb6;" k="65" />
    <hkern u1="&#x100;" u2="&#xa4;" k="56" />
    <hkern u1="&#x100;" u2="&#xa3;" k="19" />
    <hkern u1="&#x100;" u2="&#xa2;" k="47" />
    <hkern u1="&#x100;" u2="\" k="112" />
    <hkern u1="&#x100;" u2="&#x3f;" k="84" />
    <hkern u1="&#x100;" u2="&#x3a;" k="28" />
    <hkern u1="&#x100;" u2="&#x37;" k="112" />
    <hkern u1="&#x100;" u2="&#x34;" k="19" />
    <hkern u1="&#x100;" u2="&#x24;" k="74" />
    <hkern u1="&#x100;" u2="&#x23;" k="37" />
    <hkern u1="&#x101;" u2="&#x221e;" k="37" />
    <hkern u1="&#x101;" u2="&#x192;" k="37" />
    <hkern u1="&#x101;" u2="&#xb6;" k="65" />
    <hkern u1="&#x101;" u2="&#xa4;" k="56" />
    <hkern u1="&#x101;" u2="&#xa3;" k="19" />
    <hkern u1="&#x101;" u2="&#xa2;" k="47" />
    <hkern u1="&#x101;" u2="\" k="112" />
    <hkern u1="&#x101;" u2="&#x3f;" k="84" />
    <hkern u1="&#x101;" u2="&#x3a;" k="28" />
    <hkern u1="&#x101;" u2="&#x37;" k="112" />
    <hkern u1="&#x101;" u2="&#x34;" k="19" />
    <hkern u1="&#x101;" u2="&#x24;" k="74" />
    <hkern u1="&#x101;" u2="&#x23;" k="37" />
    <hkern u1="&#x102;" u2="&#x221e;" k="37" />
    <hkern u1="&#x102;" u2="&#x192;" k="37" />
    <hkern u1="&#x102;" u2="&#xb6;" k="65" />
    <hkern u1="&#x102;" u2="&#xa4;" k="56" />
    <hkern u1="&#x102;" u2="&#xa3;" k="19" />
    <hkern u1="&#x102;" u2="&#xa2;" k="47" />
    <hkern u1="&#x102;" u2="\" k="112" />
    <hkern u1="&#x102;" u2="&#x3f;" k="84" />
    <hkern u1="&#x102;" u2="&#x3a;" k="28" />
    <hkern u1="&#x102;" u2="&#x37;" k="112" />
    <hkern u1="&#x102;" u2="&#x34;" k="19" />
    <hkern u1="&#x102;" u2="&#x24;" k="74" />
    <hkern u1="&#x102;" u2="&#x23;" k="37" />
    <hkern u1="&#x103;" u2="&#x221e;" k="37" />
    <hkern u1="&#x103;" u2="&#x192;" k="37" />
    <hkern u1="&#x103;" u2="&#xb6;" k="65" />
    <hkern u1="&#x103;" u2="&#xa4;" k="56" />
    <hkern u1="&#x103;" u2="&#xa3;" k="19" />
    <hkern u1="&#x103;" u2="&#xa2;" k="47" />
    <hkern u1="&#x103;" u2="\" k="112" />
    <hkern u1="&#x103;" u2="&#x3f;" k="84" />
    <hkern u1="&#x103;" u2="&#x3a;" k="28" />
    <hkern u1="&#x103;" u2="&#x37;" k="112" />
    <hkern u1="&#x103;" u2="&#x34;" k="19" />
    <hkern u1="&#x103;" u2="&#x24;" k="74" />
    <hkern u1="&#x103;" u2="&#x23;" k="37" />
    <hkern u1="&#x104;" u2="&#x221e;" k="37" />
    <hkern u1="&#x104;" u2="&#x192;" k="37" />
    <hkern u1="&#x104;" u2="&#xb6;" k="65" />
    <hkern u1="&#x104;" u2="&#xa4;" k="56" />
    <hkern u1="&#x104;" u2="&#xa3;" k="19" />
    <hkern u1="&#x104;" u2="&#xa2;" k="47" />
    <hkern u1="&#x104;" u2="\" k="112" />
    <hkern u1="&#x104;" u2="&#x3f;" k="84" />
    <hkern u1="&#x104;" u2="&#x3a;" k="28" />
    <hkern u1="&#x104;" u2="&#x37;" k="112" />
    <hkern u1="&#x104;" u2="&#x34;" k="19" />
    <hkern u1="&#x104;" u2="&#x24;" k="74" />
    <hkern u1="&#x104;" u2="&#x23;" k="37" />
    <hkern u1="&#x105;" u2="&#x221e;" k="37" />
    <hkern u1="&#x105;" u2="&#x192;" k="37" />
    <hkern u1="&#x105;" u2="&#xb6;" k="65" />
    <hkern u1="&#x105;" u2="&#xa4;" k="56" />
    <hkern u1="&#x105;" u2="&#xa3;" k="19" />
    <hkern u1="&#x105;" u2="&#xa2;" k="47" />
    <hkern u1="&#x105;" u2="\" k="112" />
    <hkern u1="&#x105;" u2="&#x3f;" k="84" />
    <hkern u1="&#x105;" u2="&#x3a;" k="28" />
    <hkern u1="&#x105;" u2="&#x37;" k="112" />
    <hkern u1="&#x105;" u2="&#x34;" k="19" />
    <hkern u1="&#x105;" u2="&#x24;" k="74" />
    <hkern u1="&#x105;" u2="&#x23;" k="37" />
    <hkern u1="&#x106;" u2="\" k="19" />
    <hkern u1="&#x106;" u2="&#x2f;" k="19" />
    <hkern u1="&#x107;" u2="\" k="19" />
    <hkern u1="&#x107;" u2="&#x2f;" k="19" />
    <hkern u1="&#x10c;" u2="\" k="19" />
    <hkern u1="&#x10c;" u2="&#x2f;" k="19" />
    <hkern u1="&#x10d;" u2="\" k="19" />
    <hkern u1="&#x10d;" u2="&#x2f;" k="19" />
    <hkern u1="&#x10e;" u2="\" k="19" />
    <hkern u1="&#x10e;" u2="&#x2f;" k="19" />
    <hkern u1="&#x10f;" u2="\" k="19" />
    <hkern u1="&#x10f;" u2="&#x2f;" k="19" />
    <hkern u1="&#x110;" u2="\" k="19" />
    <hkern u1="&#x110;" u2="&#x2f;" k="19" />
    <hkern u1="&#x111;" u2="\" k="19" />
    <hkern u1="&#x111;" u2="&#x2f;" k="19" />
    <hkern u1="&#x112;" u2="&#x221e;" k="19" />
    <hkern u1="&#x112;" u2="&#x192;" k="19" />
    <hkern u1="&#x112;" u2="&#xb6;" k="19" />
    <hkern u1="&#x112;" u2="&#xa4;" k="28" />
    <hkern u1="&#x112;" u2="&#xa2;" k="19" />
    <hkern u1="&#x112;" u2="&#x3a;" k="19" />
    <hkern u1="&#x112;" u2="&#x24;" k="19" />
    <hkern u1="&#x112;" u2="&#x23;" k="19" />
    <hkern u1="&#x113;" u2="&#x221e;" k="19" />
    <hkern u1="&#x113;" u2="&#x192;" k="19" />
    <hkern u1="&#x113;" u2="&#xb6;" k="19" />
    <hkern u1="&#x113;" u2="&#xa4;" k="28" />
    <hkern u1="&#x113;" u2="&#xa2;" k="19" />
    <hkern u1="&#x113;" u2="&#x3a;" k="19" />
    <hkern u1="&#x113;" u2="&#x24;" k="19" />
    <hkern u1="&#x113;" u2="&#x23;" k="19" />
    <hkern u1="&#x116;" u2="&#x221e;" k="19" />
    <hkern u1="&#x116;" u2="&#x192;" k="19" />
    <hkern u1="&#x116;" u2="&#xb6;" k="19" />
    <hkern u1="&#x116;" u2="&#xa4;" k="28" />
    <hkern u1="&#x116;" u2="&#xa2;" k="19" />
    <hkern u1="&#x116;" u2="&#x3a;" k="19" />
    <hkern u1="&#x116;" u2="&#x24;" k="19" />
    <hkern u1="&#x116;" u2="&#x23;" k="19" />
    <hkern u1="&#x117;" u2="&#x221e;" k="19" />
    <hkern u1="&#x117;" u2="&#x192;" k="19" />
    <hkern u1="&#x117;" u2="&#xb6;" k="19" />
    <hkern u1="&#x117;" u2="&#xa4;" k="28" />
    <hkern u1="&#x117;" u2="&#xa2;" k="19" />
    <hkern u1="&#x117;" u2="&#x3a;" k="19" />
    <hkern u1="&#x117;" u2="&#x24;" k="19" />
    <hkern u1="&#x117;" u2="&#x23;" k="19" />
    <hkern u1="&#x118;" u2="&#x221e;" k="19" />
    <hkern u1="&#x118;" u2="&#x192;" k="19" />
    <hkern u1="&#x118;" u2="&#xb6;" k="19" />
    <hkern u1="&#x118;" u2="&#xa4;" k="28" />
    <hkern u1="&#x118;" u2="&#xa2;" k="19" />
    <hkern u1="&#x118;" u2="&#x3a;" k="19" />
    <hkern u1="&#x118;" u2="&#x24;" k="19" />
    <hkern u1="&#x118;" u2="&#x23;" k="19" />
    <hkern u1="&#x119;" u2="&#x221e;" k="19" />
    <hkern u1="&#x119;" u2="&#x192;" k="19" />
    <hkern u1="&#x119;" u2="&#xb6;" k="19" />
    <hkern u1="&#x119;" u2="&#xa4;" k="28" />
    <hkern u1="&#x119;" u2="&#xa2;" k="19" />
    <hkern u1="&#x119;" u2="&#x3a;" k="19" />
    <hkern u1="&#x119;" u2="&#x24;" k="19" />
    <hkern u1="&#x119;" u2="&#x23;" k="19" />
    <hkern u1="&#x11a;" u2="&#x221e;" k="19" />
    <hkern u1="&#x11a;" u2="&#x192;" k="19" />
    <hkern u1="&#x11a;" u2="&#xb6;" k="19" />
    <hkern u1="&#x11a;" u2="&#xa4;" k="28" />
    <hkern u1="&#x11a;" u2="&#xa2;" k="19" />
    <hkern u1="&#x11a;" u2="&#x3a;" k="19" />
    <hkern u1="&#x11a;" u2="&#x24;" k="19" />
    <hkern u1="&#x11a;" u2="&#x23;" k="19" />
    <hkern u1="&#x11b;" u2="&#x221e;" k="19" />
    <hkern u1="&#x11b;" u2="&#x192;" k="19" />
    <hkern u1="&#x11b;" u2="&#xb6;" k="19" />
    <hkern u1="&#x11b;" u2="&#xa4;" k="28" />
    <hkern u1="&#x11b;" u2="&#xa2;" k="19" />
    <hkern u1="&#x11b;" u2="&#x3a;" k="19" />
    <hkern u1="&#x11b;" u2="&#x24;" k="19" />
    <hkern u1="&#x11b;" u2="&#x23;" k="19" />
    <hkern u1="&#x11e;" u2="&#x3f;" k="19" />
    <hkern u1="&#x11e;" u2="&#x32;" k="-9" />
    <hkern u1="&#x11e;" u2="&#x31;" k="19" />
    <hkern u1="&#x11f;" u2="&#x3f;" k="19" />
    <hkern u1="&#x11f;" u2="&#x32;" k="-9" />
    <hkern u1="&#x11f;" u2="&#x31;" k="19" />
    <hkern u1="&#x122;" u2="&#x3f;" k="19" />
    <hkern u1="&#x122;" u2="&#x32;" k="-9" />
    <hkern u1="&#x122;" u2="&#x31;" k="19" />
    <hkern u1="&#x123;" u2="&#x3f;" k="19" />
    <hkern u1="&#x123;" u2="&#x32;" k="-9" />
    <hkern u1="&#x123;" u2="&#x31;" k="19" />
    <hkern u1="&#x136;" u2="&#x221e;" k="47" />
    <hkern u1="&#x136;" u2="&#x192;" k="37" />
    <hkern u1="&#x136;" u2="&#xb6;" k="9" />
    <hkern u1="&#x136;" u2="&#xa4;" k="65" />
    <hkern u1="&#x136;" u2="&#xa3;" k="19" />
    <hkern u1="&#x136;" u2="&#xa2;" k="47" />
    <hkern u1="&#x136;" u2="&#x3a;" k="28" />
    <hkern u1="&#x136;" u2="&#x39;" k="19" />
    <hkern u1="&#x136;" u2="&#x34;" k="37" />
    <hkern u1="&#x136;" u2="&#x33;" k="19" />
    <hkern u1="&#x136;" u2="&#x32;" k="9" />
    <hkern u1="&#x136;" u2="&#x24;" k="56" />
    <hkern u1="&#x136;" u2="&#x23;" k="56" />
    <hkern u1="&#x137;" u2="&#x221e;" k="47" />
    <hkern u1="&#x137;" u2="&#x192;" k="37" />
    <hkern u1="&#x137;" u2="&#xb6;" k="9" />
    <hkern u1="&#x137;" u2="&#xa4;" k="65" />
    <hkern u1="&#x137;" u2="&#xa3;" k="19" />
    <hkern u1="&#x137;" u2="&#xa2;" k="47" />
    <hkern u1="&#x137;" u2="&#x3a;" k="28" />
    <hkern u1="&#x137;" u2="&#x39;" k="19" />
    <hkern u1="&#x137;" u2="&#x34;" k="37" />
    <hkern u1="&#x137;" u2="&#x33;" k="19" />
    <hkern u1="&#x137;" u2="&#x32;" k="9" />
    <hkern u1="&#x137;" u2="&#x24;" k="56" />
    <hkern u1="&#x137;" u2="&#x23;" k="56" />
    <hkern u1="&#x139;" u2="&#x221e;" k="19" />
    <hkern u1="&#x139;" u2="&#xb6;" k="102" />
    <hkern u1="&#x139;" u2="&#xa4;" k="47" />
    <hkern u1="&#x139;" u2="&#xa2;" k="19" />
    <hkern u1="&#x139;" u2="\" k="140" />
    <hkern u1="&#x139;" u2="&#x3f;" k="140" />
    <hkern u1="&#x139;" u2="&#x37;" k="140" />
    <hkern u1="&#x139;" u2="&#x31;" k="74" />
    <hkern u1="&#x139;" u2="&#x24;" k="19" />
    <hkern u1="&#x13a;" u2="&#x221e;" k="19" />
    <hkern u1="&#x13a;" u2="&#xb6;" k="102" />
    <hkern u1="&#x13a;" u2="&#xa4;" k="47" />
    <hkern u1="&#x13a;" u2="&#xa2;" k="19" />
    <hkern u1="&#x13a;" u2="\" k="140" />
    <hkern u1="&#x13a;" u2="&#x3f;" k="140" />
    <hkern u1="&#x13a;" u2="&#x37;" k="140" />
    <hkern u1="&#x13a;" u2="&#x31;" k="74" />
    <hkern u1="&#x13a;" u2="&#x24;" k="19" />
    <hkern u1="&#x13b;" u2="&#x221e;" k="19" />
    <hkern u1="&#x13b;" u2="&#xb6;" k="102" />
    <hkern u1="&#x13b;" u2="&#xa4;" k="47" />
    <hkern u1="&#x13b;" u2="&#xa2;" k="19" />
    <hkern u1="&#x13b;" u2="\" k="140" />
    <hkern u1="&#x13b;" u2="&#x3f;" k="140" />
    <hkern u1="&#x13b;" u2="&#x37;" k="140" />
    <hkern u1="&#x13b;" u2="&#x31;" k="74" />
    <hkern u1="&#x13b;" u2="&#x24;" k="19" />
    <hkern u1="&#x13c;" u2="&#x221e;" k="19" />
    <hkern u1="&#x13c;" u2="&#xb6;" k="102" />
    <hkern u1="&#x13c;" u2="&#xa4;" k="47" />
    <hkern u1="&#x13c;" u2="&#xa2;" k="19" />
    <hkern u1="&#x13c;" u2="\" k="140" />
    <hkern u1="&#x13c;" u2="&#x3f;" k="140" />
    <hkern u1="&#x13c;" u2="&#x37;" k="140" />
    <hkern u1="&#x13c;" u2="&#x31;" k="74" />
    <hkern u1="&#x13c;" u2="&#x24;" k="19" />
    <hkern u1="&#x13d;" u2="&#x221e;" k="19" />
    <hkern u1="&#x13d;" u2="&#xb6;" k="102" />
    <hkern u1="&#x13d;" u2="&#xa4;" k="47" />
    <hkern u1="&#x13d;" u2="&#xa2;" k="19" />
    <hkern u1="&#x13d;" u2="\" k="140" />
    <hkern u1="&#x13d;" u2="&#x3f;" k="140" />
    <hkern u1="&#x13d;" u2="&#x37;" k="140" />
    <hkern u1="&#x13d;" u2="&#x31;" k="74" />
    <hkern u1="&#x13d;" u2="&#x24;" k="19" />
    <hkern u1="&#x13e;" u2="&#x221e;" k="19" />
    <hkern u1="&#x13e;" u2="&#xb6;" k="102" />
    <hkern u1="&#x13e;" u2="&#xa4;" k="47" />
    <hkern u1="&#x13e;" u2="&#xa2;" k="19" />
    <hkern u1="&#x13e;" u2="\" k="140" />
    <hkern u1="&#x13e;" u2="&#x3f;" k="140" />
    <hkern u1="&#x13e;" u2="&#x37;" k="140" />
    <hkern u1="&#x13e;" u2="&#x31;" k="74" />
    <hkern u1="&#x13e;" u2="&#x24;" k="19" />
    <hkern u1="&#x141;" u2="&#x221e;" k="19" />
    <hkern u1="&#x141;" u2="&#xb6;" k="102" />
    <hkern u1="&#x141;" u2="&#xa4;" k="47" />
    <hkern u1="&#x141;" u2="&#xa2;" k="19" />
    <hkern u1="&#x141;" u2="\" k="140" />
    <hkern u1="&#x141;" u2="&#x3f;" k="140" />
    <hkern u1="&#x141;" u2="&#x37;" k="140" />
    <hkern u1="&#x141;" u2="&#x31;" k="74" />
    <hkern u1="&#x141;" u2="&#x24;" k="19" />
    <hkern u1="&#x142;" u2="&#x221e;" k="19" />
    <hkern u1="&#x142;" u2="&#xb6;" k="102" />
    <hkern u1="&#x142;" u2="&#xa4;" k="47" />
    <hkern u1="&#x142;" u2="&#xa2;" k="19" />
    <hkern u1="&#x142;" u2="\" k="140" />
    <hkern u1="&#x142;" u2="&#x3f;" k="140" />
    <hkern u1="&#x142;" u2="&#x37;" k="140" />
    <hkern u1="&#x142;" u2="&#x31;" k="74" />
    <hkern u1="&#x142;" u2="&#x24;" k="19" />
    <hkern u1="&#x14c;" u2="\" k="19" />
    <hkern u1="&#x14c;" u2="&#x2f;" k="19" />
    <hkern u1="&#x14d;" u2="\" k="19" />
    <hkern u1="&#x14d;" u2="&#x2f;" k="19" />
    <hkern u1="&#x150;" u2="\" k="19" />
    <hkern u1="&#x150;" u2="&#x2f;" k="19" />
    <hkern u1="&#x151;" u2="\" k="19" />
    <hkern u1="&#x151;" u2="&#x2f;" k="19" />
    <hkern u1="&#x152;" u2="&#x221e;" k="19" />
    <hkern u1="&#x152;" u2="&#x192;" k="19" />
    <hkern u1="&#x152;" u2="&#xb6;" k="19" />
    <hkern u1="&#x152;" u2="&#xa4;" k="28" />
    <hkern u1="&#x152;" u2="&#xa2;" k="19" />
    <hkern u1="&#x152;" u2="&#x3a;" k="19" />
    <hkern u1="&#x152;" u2="&#x24;" k="19" />
    <hkern u1="&#x152;" u2="&#x23;" k="19" />
    <hkern u1="&#x153;" u2="&#x221e;" k="19" />
    <hkern u1="&#x153;" u2="&#x192;" k="19" />
    <hkern u1="&#x153;" u2="&#xb6;" k="19" />
    <hkern u1="&#x153;" u2="&#xa4;" k="28" />
    <hkern u1="&#x153;" u2="&#xa2;" k="19" />
    <hkern u1="&#x153;" u2="&#x3a;" k="19" />
    <hkern u1="&#x153;" u2="&#x24;" k="19" />
    <hkern u1="&#x153;" u2="&#x23;" k="19" />
    <hkern u1="&#x154;" u2="&#x3c0;" k="-19" />
    <hkern u1="&#x154;" u2="\" k="19" />
    <hkern u1="&#x155;" u2="&#x3c0;" k="-19" />
    <hkern u1="&#x155;" u2="\" k="19" />
    <hkern u1="&#x156;" u2="&#x3c0;" k="-19" />
    <hkern u1="&#x156;" u2="\" k="19" />
    <hkern u1="&#x157;" u2="&#x3c0;" k="-19" />
    <hkern u1="&#x157;" u2="\" k="19" />
    <hkern u1="&#x158;" u2="&#x3c0;" k="-19" />
    <hkern u1="&#x158;" u2="\" k="19" />
    <hkern u1="&#x159;" u2="&#x3c0;" k="-19" />
    <hkern u1="&#x159;" u2="\" k="19" />
    <hkern u1="&#x15a;" u2="\" k="19" />
    <hkern u1="&#x15a;" u2="&#x2f;" k="19" />
    <hkern u1="&#x15b;" u2="\" k="19" />
    <hkern u1="&#x15b;" u2="&#x2f;" k="19" />
    <hkern u1="&#x15e;" u2="\" k="19" />
    <hkern u1="&#x15e;" u2="&#x2f;" k="19" />
    <hkern u1="&#x15f;" u2="\" k="19" />
    <hkern u1="&#x15f;" u2="&#x2f;" k="19" />
    <hkern u1="&#x160;" u2="\" k="19" />
    <hkern u1="&#x160;" u2="&#x2f;" k="19" />
    <hkern u1="&#x161;" u2="\" k="19" />
    <hkern u1="&#x161;" u2="&#x2f;" k="19" />
    <hkern u1="&#x162;" u2="&#x221e;" k="19" />
    <hkern u1="&#x162;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x162;" u2="&#x192;" k="74" />
    <hkern u1="&#x162;" u2="&#xbf;" k="112" />
    <hkern u1="&#x162;" u2="&#xa4;" k="28" />
    <hkern u1="&#x162;" u2="&#xa3;" k="56" />
    <hkern u1="&#x162;" u2="&#xa2;" k="19" />
    <hkern u1="&#x162;" u2="&#x3a;" k="37" />
    <hkern u1="&#x162;" u2="&#x34;" k="65" />
    <hkern u1="&#x162;" u2="&#x2f;" k="93" />
    <hkern u1="&#x162;" u2="&#x24;" k="9" />
    <hkern u1="&#x162;" u2="&#x23;" k="19" />
    <hkern u1="&#x163;" u2="&#x221e;" k="19" />
    <hkern u1="&#x163;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x163;" u2="&#x192;" k="74" />
    <hkern u1="&#x163;" u2="&#xbf;" k="112" />
    <hkern u1="&#x163;" u2="&#xa4;" k="28" />
    <hkern u1="&#x163;" u2="&#xa3;" k="56" />
    <hkern u1="&#x163;" u2="&#xa2;" k="19" />
    <hkern u1="&#x163;" u2="&#x3a;" k="37" />
    <hkern u1="&#x163;" u2="&#x34;" k="65" />
    <hkern u1="&#x163;" u2="&#x2f;" k="93" />
    <hkern u1="&#x163;" u2="&#x24;" k="9" />
    <hkern u1="&#x163;" u2="&#x23;" k="19" />
    <hkern u1="&#x164;" u2="&#x221e;" k="19" />
    <hkern u1="&#x164;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x164;" u2="&#x192;" k="74" />
    <hkern u1="&#x164;" u2="&#xbf;" k="112" />
    <hkern u1="&#x164;" u2="&#xa4;" k="28" />
    <hkern u1="&#x164;" u2="&#xa3;" k="56" />
    <hkern u1="&#x164;" u2="&#xa2;" k="19" />
    <hkern u1="&#x164;" u2="&#x3a;" k="37" />
    <hkern u1="&#x164;" u2="&#x34;" k="65" />
    <hkern u1="&#x164;" u2="&#x2f;" k="93" />
    <hkern u1="&#x164;" u2="&#x24;" k="9" />
    <hkern u1="&#x164;" u2="&#x23;" k="19" />
    <hkern u1="&#x165;" u2="&#x221e;" k="19" />
    <hkern u1="&#x165;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x165;" u2="&#x192;" k="74" />
    <hkern u1="&#x165;" u2="&#xbf;" k="112" />
    <hkern u1="&#x165;" u2="&#xa4;" k="28" />
    <hkern u1="&#x165;" u2="&#xa3;" k="56" />
    <hkern u1="&#x165;" u2="&#xa2;" k="19" />
    <hkern u1="&#x165;" u2="&#x3a;" k="37" />
    <hkern u1="&#x165;" u2="&#x34;" k="65" />
    <hkern u1="&#x165;" u2="&#x2f;" k="93" />
    <hkern u1="&#x165;" u2="&#x24;" k="9" />
    <hkern u1="&#x165;" u2="&#x23;" k="19" />
    <hkern u1="&#x178;" u2="&#x221e;" k="74" />
    <hkern u1="&#x178;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x178;" u2="&#x192;" k="121" />
    <hkern u1="&#x178;" u2="&#xbf;" k="168" />
    <hkern u1="&#x178;" u2="&#xb6;" k="19" />
    <hkern u1="&#x178;" u2="&#xa4;" k="84" />
    <hkern u1="&#x178;" u2="&#xa3;" k="93" />
    <hkern u1="&#x178;" u2="&#xa2;" k="65" />
    <hkern u1="&#x178;" u2="&#x3a;" k="47" />
    <hkern u1="&#x178;" u2="&#x39;" k="19" />
    <hkern u1="&#x178;" u2="&#x34;" k="130" />
    <hkern u1="&#x178;" u2="&#x33;" k="19" />
    <hkern u1="&#x178;" u2="&#x32;" k="19" />
    <hkern u1="&#x178;" u2="&#x2f;" k="130" />
    <hkern u1="&#x178;" u2="&#x24;" k="65" />
    <hkern u1="&#x178;" u2="&#x23;" k="74" />
    <hkern u1="&#x179;" u2="&#x221e;" k="19" />
    <hkern u1="&#x179;" u2="&#x192;" k="19" />
    <hkern u1="&#x179;" u2="&#xa4;" k="37" />
    <hkern u1="&#x179;" u2="&#xa3;" k="37" />
    <hkern u1="&#x179;" u2="&#xa2;" k="19" />
    <hkern u1="&#x179;" u2="&#x3a;" k="19" />
    <hkern u1="&#x179;" u2="&#x34;" k="37" />
    <hkern u1="&#x179;" u2="&#x2f;" k="37" />
    <hkern u1="&#x179;" u2="&#x24;" k="19" />
    <hkern u1="&#x179;" u2="&#x23;" k="19" />
    <hkern u1="&#x17a;" u2="&#x221e;" k="19" />
    <hkern u1="&#x17a;" u2="&#x192;" k="19" />
    <hkern u1="&#x17a;" u2="&#xa4;" k="37" />
    <hkern u1="&#x17a;" u2="&#xa3;" k="37" />
    <hkern u1="&#x17a;" u2="&#xa2;" k="19" />
    <hkern u1="&#x17a;" u2="&#x3a;" k="19" />
    <hkern u1="&#x17a;" u2="&#x34;" k="37" />
    <hkern u1="&#x17a;" u2="&#x2f;" k="37" />
    <hkern u1="&#x17a;" u2="&#x24;" k="19" />
    <hkern u1="&#x17a;" u2="&#x23;" k="19" />
    <hkern u1="&#x17b;" u2="&#x221e;" k="19" />
    <hkern u1="&#x17b;" u2="&#x192;" k="19" />
    <hkern u1="&#x17b;" u2="&#xa4;" k="37" />
    <hkern u1="&#x17b;" u2="&#xa3;" k="37" />
    <hkern u1="&#x17b;" u2="&#xa2;" k="19" />
    <hkern u1="&#x17b;" u2="&#x3a;" k="19" />
    <hkern u1="&#x17b;" u2="&#x34;" k="37" />
    <hkern u1="&#x17b;" u2="&#x2f;" k="37" />
    <hkern u1="&#x17b;" u2="&#x24;" k="19" />
    <hkern u1="&#x17b;" u2="&#x23;" k="19" />
    <hkern u1="&#x17c;" u2="&#x221e;" k="19" />
    <hkern u1="&#x17c;" u2="&#x192;" k="19" />
    <hkern u1="&#x17c;" u2="&#xa4;" k="37" />
    <hkern u1="&#x17c;" u2="&#xa3;" k="37" />
    <hkern u1="&#x17c;" u2="&#xa2;" k="19" />
    <hkern u1="&#x17c;" u2="&#x3a;" k="19" />
    <hkern u1="&#x17c;" u2="&#x34;" k="37" />
    <hkern u1="&#x17c;" u2="&#x2f;" k="37" />
    <hkern u1="&#x17c;" u2="&#x24;" k="19" />
    <hkern u1="&#x17c;" u2="&#x23;" k="19" />
    <hkern u1="&#x17d;" u2="&#x221e;" k="19" />
    <hkern u1="&#x17d;" u2="&#x192;" k="19" />
    <hkern u1="&#x17d;" u2="&#xa4;" k="37" />
    <hkern u1="&#x17d;" u2="&#xa3;" k="37" />
    <hkern u1="&#x17d;" u2="&#xa2;" k="19" />
    <hkern u1="&#x17d;" u2="&#x3a;" k="19" />
    <hkern u1="&#x17d;" u2="&#x34;" k="37" />
    <hkern u1="&#x17d;" u2="&#x2f;" k="37" />
    <hkern u1="&#x17d;" u2="&#x24;" k="19" />
    <hkern u1="&#x17d;" u2="&#x23;" k="19" />
    <hkern u1="&#x17e;" u2="&#x221e;" k="19" />
    <hkern u1="&#x17e;" u2="&#x192;" k="19" />
    <hkern u1="&#x17e;" u2="&#xa4;" k="37" />
    <hkern u1="&#x17e;" u2="&#xa3;" k="37" />
    <hkern u1="&#x17e;" u2="&#xa2;" k="19" />
    <hkern u1="&#x17e;" u2="&#x3a;" k="19" />
    <hkern u1="&#x17e;" u2="&#x34;" k="37" />
    <hkern u1="&#x17e;" u2="&#x2f;" k="37" />
    <hkern u1="&#x17e;" u2="&#x24;" k="19" />
    <hkern u1="&#x17e;" u2="&#x23;" k="19" />
    <hkern u1="&#x192;" g2="nine.dnom" k="93" />
    <hkern u1="&#x192;" g2="eight.dnom" k="93" />
    <hkern u1="&#x192;" g2="seven.dnom" k="93" />
    <hkern u1="&#x192;" g2="six.dnom" k="93" />
    <hkern u1="&#x192;" g2="five.dnom" k="93" />
    <hkern u1="&#x192;" g2="four.dnom" k="93" />
    <hkern u1="&#x192;" g2="three.dnom" k="93" />
    <hkern u1="&#x192;" g2="two.dnom" k="93" />
    <hkern u1="&#x192;" g2="one.dnom" k="93" />
    <hkern u1="&#x192;" g2="zero.dnom" k="93" />
    <hkern u1="&#x192;" g2="pi.ss01" k="37" />
    <hkern u1="&#x192;" g2="j.ss01" k="121" />
    <hkern u1="&#x192;" g2="ae.ss01" k="186" />
    <hkern u1="&#x192;" g2="atilde.ss01" k="140" />
    <hkern u1="&#x192;" g2="aring.ss01" k="140" />
    <hkern u1="&#x192;" g2="aogonek.ss01" k="140" />
    <hkern u1="&#x192;" g2="amacron.ss01" k="140" />
    <hkern u1="&#x192;" g2="agrave.ss01" k="140" />
    <hkern u1="&#x192;" g2="adieresis.ss01" k="140" />
    <hkern u1="&#x192;" g2="acircumflex.ss01" k="140" />
    <hkern u1="&#x192;" g2="abreve.ss01" k="140" />
    <hkern u1="&#x192;" g2="aacute.ss01" k="140" />
    <hkern u1="&#x192;" g2="a.ss01" k="140" />
    <hkern u1="&#x192;" g2="J.ss01" k="121" />
    <hkern u1="&#x192;" g2="AE.ss01" k="186" />
    <hkern u1="&#x192;" g2="Atilde.ss01" k="140" />
    <hkern u1="&#x192;" g2="Aring.ss01" k="140" />
    <hkern u1="&#x192;" g2="Aogonek.ss01" k="140" />
    <hkern u1="&#x192;" g2="Amacron.ss01" k="140" />
    <hkern u1="&#x192;" g2="Agrave.ss01" k="140" />
    <hkern u1="&#x192;" g2="Adieresis.ss01" k="140" />
    <hkern u1="&#x192;" g2="Acircumflex.ss01" k="140" />
    <hkern u1="&#x192;" g2="Abreve.ss01" k="140" />
    <hkern u1="&#x192;" g2="Aacute.ss01" k="140" />
    <hkern u1="&#x192;" g2="A.ss01" k="140" />
    <hkern u1="&#x192;" u2="&#x2265;" k="37" />
    <hkern u1="&#x192;" u2="&#x2264;" k="37" />
    <hkern u1="&#x192;" u2="&#x2260;" k="37" />
    <hkern u1="&#x192;" u2="&#x2248;" k="37" />
    <hkern u1="&#x192;" u2="&#x2219;" k="74" />
    <hkern u1="&#x192;" u2="&#x2212;" k="74" />
    <hkern u1="&#x192;" u2="&#x20ac;" k="19" />
    <hkern u1="&#x192;" u2="&#x2089;" k="93" />
    <hkern u1="&#x192;" u2="&#x2088;" k="93" />
    <hkern u1="&#x192;" u2="&#x2087;" k="93" />
    <hkern u1="&#x192;" u2="&#x2086;" k="93" />
    <hkern u1="&#x192;" u2="&#x2085;" k="93" />
    <hkern u1="&#x192;" u2="&#x2084;" k="93" />
    <hkern u1="&#x192;" u2="&#x2083;" k="93" />
    <hkern u1="&#x192;" u2="&#x2082;" k="93" />
    <hkern u1="&#x192;" u2="&#x2081;" k="93" />
    <hkern u1="&#x192;" u2="&#x2080;" k="93" />
    <hkern u1="&#x192;" u2="&#x203a;" k="37" />
    <hkern u1="&#x192;" u2="&#x2039;" k="37" />
    <hkern u1="&#x192;" u2="&#x2026;" k="112" />
    <hkern u1="&#x192;" u2="&#x2022;" k="74" />
    <hkern u1="&#x192;" u2="&#x201e;" k="112" />
    <hkern u1="&#x192;" u2="&#x201a;" k="112" />
    <hkern u1="&#x192;" u2="&#x2014;" k="74" />
    <hkern u1="&#x192;" u2="&#x2013;" k="74" />
    <hkern u1="&#x192;" u2="&#x1ee3;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ee2;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ee1;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ee0;" k="19" />
    <hkern u1="&#x192;" u2="&#x1edf;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ede;" k="19" />
    <hkern u1="&#x192;" u2="&#x1edd;" k="19" />
    <hkern u1="&#x192;" u2="&#x1edc;" k="19" />
    <hkern u1="&#x192;" u2="&#x1edb;" k="19" />
    <hkern u1="&#x192;" u2="&#x1eda;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed9;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed8;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed7;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed6;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed5;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed4;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed3;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed2;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed1;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ed0;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ecf;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ece;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ecd;" k="19" />
    <hkern u1="&#x192;" u2="&#x1ecc;" k="19" />
    <hkern u1="&#x192;" u2="&#x1eb7;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eb6;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eb5;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eb4;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eb3;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eb2;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eb1;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eb0;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eaf;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eae;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ead;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eac;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eab;" k="140" />
    <hkern u1="&#x192;" u2="&#x1eaa;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea9;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea8;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea7;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea6;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea5;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea4;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea3;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea2;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea1;" k="140" />
    <hkern u1="&#x192;" u2="&#x1ea0;" k="140" />
    <hkern u1="&#x192;" u2="&#x1e9e;" k="19" />
    <hkern u1="&#x192;" u2="&#x326;" k="112" />
    <hkern u1="&#x192;" u2="&#x2db;" k="112" />
    <hkern u1="&#x192;" u2="&#x219;" k="19" />
    <hkern u1="&#x192;" u2="&#x218;" k="19" />
    <hkern u1="&#x192;" u2="&#x1a1;" k="19" />
    <hkern u1="&#x192;" u2="&#x1a0;" k="19" />
    <hkern u1="&#x192;" u2="&#x161;" k="19" />
    <hkern u1="&#x192;" u2="&#x160;" k="19" />
    <hkern u1="&#x192;" u2="&#x15f;" k="19" />
    <hkern u1="&#x192;" u2="&#x15e;" k="19" />
    <hkern u1="&#x192;" u2="&#x15b;" k="19" />
    <hkern u1="&#x192;" u2="&#x15a;" k="19" />
    <hkern u1="&#x192;" u2="&#x153;" k="19" />
    <hkern u1="&#x192;" u2="&#x152;" k="19" />
    <hkern u1="&#x192;" u2="&#x151;" k="19" />
    <hkern u1="&#x192;" u2="&#x150;" k="19" />
    <hkern u1="&#x192;" u2="&#x14d;" k="19" />
    <hkern u1="&#x192;" u2="&#x14c;" k="19" />
    <hkern u1="&#x192;" u2="&#x123;" k="19" />
    <hkern u1="&#x192;" u2="&#x122;" k="19" />
    <hkern u1="&#x192;" u2="&#x11f;" k="19" />
    <hkern u1="&#x192;" u2="&#x11e;" k="19" />
    <hkern u1="&#x192;" u2="&#x10d;" k="19" />
    <hkern u1="&#x192;" u2="&#x10c;" k="19" />
    <hkern u1="&#x192;" u2="&#x107;" k="19" />
    <hkern u1="&#x192;" u2="&#x106;" k="19" />
    <hkern u1="&#x192;" u2="&#x105;" k="140" />
    <hkern u1="&#x192;" u2="&#x104;" k="140" />
    <hkern u1="&#x192;" u2="&#x103;" k="140" />
    <hkern u1="&#x192;" u2="&#x102;" k="140" />
    <hkern u1="&#x192;" u2="&#x101;" k="140" />
    <hkern u1="&#x192;" u2="&#x100;" k="140" />
    <hkern u1="&#x192;" u2="&#xf8;" k="19" />
    <hkern u1="&#x192;" u2="&#xf7;" k="37" />
    <hkern u1="&#x192;" u2="&#xf6;" k="19" />
    <hkern u1="&#x192;" u2="&#xf5;" k="19" />
    <hkern u1="&#x192;" u2="&#xf4;" k="19" />
    <hkern u1="&#x192;" u2="&#xf3;" k="19" />
    <hkern u1="&#x192;" u2="&#xf2;" k="19" />
    <hkern u1="&#x192;" u2="&#xe7;" k="19" />
    <hkern u1="&#x192;" u2="&#xe6;" k="186" />
    <hkern u1="&#x192;" u2="&#xe5;" k="140" />
    <hkern u1="&#x192;" u2="&#xe4;" k="140" />
    <hkern u1="&#x192;" u2="&#xe3;" k="140" />
    <hkern u1="&#x192;" u2="&#xe2;" k="140" />
    <hkern u1="&#x192;" u2="&#xe1;" k="140" />
    <hkern u1="&#x192;" u2="&#xe0;" k="140" />
    <hkern u1="&#x192;" u2="&#xdf;" k="19" />
    <hkern u1="&#x192;" u2="&#xd8;" k="19" />
    <hkern u1="&#x192;" u2="&#xd7;" k="37" />
    <hkern u1="&#x192;" u2="&#xd6;" k="19" />
    <hkern u1="&#x192;" u2="&#xd5;" k="19" />
    <hkern u1="&#x192;" u2="&#xd4;" k="19" />
    <hkern u1="&#x192;" u2="&#xd3;" k="19" />
    <hkern u1="&#x192;" u2="&#xd2;" k="19" />
    <hkern u1="&#x192;" u2="&#xc7;" k="19" />
    <hkern u1="&#x192;" u2="&#xc6;" k="186" />
    <hkern u1="&#x192;" u2="&#xc5;" k="140" />
    <hkern u1="&#x192;" u2="&#xc4;" k="140" />
    <hkern u1="&#x192;" u2="&#xc3;" k="140" />
    <hkern u1="&#x192;" u2="&#xc2;" k="140" />
    <hkern u1="&#x192;" u2="&#xc1;" k="140" />
    <hkern u1="&#x192;" u2="&#xc0;" k="140" />
    <hkern u1="&#x192;" u2="&#xbb;" k="37" />
    <hkern u1="&#x192;" u2="&#xb8;" k="112" />
    <hkern u1="&#x192;" u2="&#xb7;" k="74" />
    <hkern u1="&#x192;" u2="&#xb1;" k="37" />
    <hkern u1="&#x192;" u2="&#xad;" k="74" />
    <hkern u1="&#x192;" u2="&#xac;" k="74" />
    <hkern u1="&#x192;" u2="&#xab;" k="37" />
    <hkern u1="&#x192;" u2="&#xa9;" k="19" />
    <hkern u1="&#x192;" u2="&#xa7;" k="19" />
    <hkern u1="&#x192;" u2="&#xa1;" k="37" />
    <hkern u1="&#x192;" u2="&#x7e;" k="74" />
    <hkern u1="&#x192;" u2="&#x7b;" k="74" />
    <hkern u1="&#x192;" u2="s" k="19" />
    <hkern u1="&#x192;" u2="q" k="19" />
    <hkern u1="&#x192;" u2="o" k="19" />
    <hkern u1="&#x192;" u2="j" k="121" />
    <hkern u1="&#x192;" u2="g" k="19" />
    <hkern u1="&#x192;" u2="c" k="19" />
    <hkern u1="&#x192;" u2="a" k="140" />
    <hkern u1="&#x192;" u2="_" k="112" />
    <hkern u1="&#x192;" u2="S" k="19" />
    <hkern u1="&#x192;" u2="Q" k="19" />
    <hkern u1="&#x192;" u2="O" k="19" />
    <hkern u1="&#x192;" u2="J" k="121" />
    <hkern u1="&#x192;" u2="G" k="19" />
    <hkern u1="&#x192;" u2="C" k="19" />
    <hkern u1="&#x192;" u2="A" k="140" />
    <hkern u1="&#x192;" u2="&#x40;" k="19" />
    <hkern u1="&#x192;" u2="&#x3e;" k="37" />
    <hkern u1="&#x192;" u2="&#x3d;" k="37" />
    <hkern u1="&#x192;" u2="&#x3c;" k="74" />
    <hkern u1="&#x192;" u2="&#x3b;" k="37" />
    <hkern u1="&#x192;" u2="&#x38;" k="19" />
    <hkern u1="&#x192;" u2="&#x36;" k="19" />
    <hkern u1="&#x192;" u2="&#x30;" k="19" />
    <hkern u1="&#x192;" u2="&#x2e;" k="112" />
    <hkern u1="&#x192;" u2="&#x2d;" k="74" />
    <hkern u1="&#x192;" u2="&#x2c;" k="112" />
    <hkern u1="&#x192;" u2="&#x2b;" k="37" />
    <hkern u1="&#x192;" u2="&#x26;" k="19" />
    <hkern u1="&#x192;" u2="&#x221e;" k="47" />
    <hkern u1="&#x192;" u2="&#x3c0;" k="37" />
    <hkern u1="&#x192;" u2="&#x192;" k="93" />
    <hkern u1="&#x192;" u2="&#xbf;" k="149" />
    <hkern u1="&#x192;" u2="&#xa4;" k="37" />
    <hkern u1="&#x192;" u2="&#xa3;" k="56" />
    <hkern u1="&#x192;" u2="&#xa2;" k="56" />
    <hkern u1="&#x192;" u2="&#x3a;" k="65" />
    <hkern u1="&#x192;" u2="&#x34;" k="84" />
    <hkern u1="&#x192;" u2="&#x32;" k="19" />
    <hkern u1="&#x192;" u2="&#x2f;" k="140" />
    <hkern u1="&#x192;" u2="&#x24;" k="47" />
    <hkern u1="&#x192;" u2="&#x23;" k="56" />
    <hkern u1="&#x218;" u2="\" k="19" />
    <hkern u1="&#x218;" u2="&#x2f;" k="19" />
    <hkern u1="&#x219;" u2="\" k="19" />
    <hkern u1="&#x219;" u2="&#x2f;" k="19" />
    <hkern u1="&#x21a;" u2="&#x221e;" k="19" />
    <hkern u1="&#x21a;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x21a;" u2="&#x192;" k="74" />
    <hkern u1="&#x21a;" u2="&#xbf;" k="112" />
    <hkern u1="&#x21a;" u2="&#xa4;" k="28" />
    <hkern u1="&#x21a;" u2="&#xa3;" k="56" />
    <hkern u1="&#x21a;" u2="&#xa2;" k="19" />
    <hkern u1="&#x21a;" u2="&#x3a;" k="37" />
    <hkern u1="&#x21a;" u2="&#x34;" k="65" />
    <hkern u1="&#x21a;" u2="&#x2f;" k="93" />
    <hkern u1="&#x21a;" u2="&#x24;" k="9" />
    <hkern u1="&#x21a;" u2="&#x23;" k="19" />
    <hkern u1="&#x21b;" u2="&#x221e;" k="19" />
    <hkern u1="&#x21b;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x21b;" u2="&#x192;" k="74" />
    <hkern u1="&#x21b;" u2="&#xbf;" k="112" />
    <hkern u1="&#x21b;" u2="&#xa4;" k="28" />
    <hkern u1="&#x21b;" u2="&#xa3;" k="56" />
    <hkern u1="&#x21b;" u2="&#xa2;" k="19" />
    <hkern u1="&#x21b;" u2="&#x3a;" k="37" />
    <hkern u1="&#x21b;" u2="&#x34;" k="65" />
    <hkern u1="&#x21b;" u2="&#x2f;" k="93" />
    <hkern u1="&#x21b;" u2="&#x24;" k="9" />
    <hkern u1="&#x21b;" u2="&#x23;" k="19" />
    <hkern u1="&#x2c6;" u2="&#x192;" k="56" />
    <hkern u1="&#x2c6;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2c6;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2c6;" u2="&#x34;" k="74" />
    <hkern u1="&#x2c6;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2c7;" u2="&#x192;" k="56" />
    <hkern u1="&#x2c7;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2c7;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2c7;" u2="&#x34;" k="74" />
    <hkern u1="&#x2c7;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2c9;" u2="&#x192;" k="56" />
    <hkern u1="&#x2c9;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2c9;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2c9;" u2="&#x34;" k="74" />
    <hkern u1="&#x2c9;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2d8;" u2="&#x192;" k="56" />
    <hkern u1="&#x2d8;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2d8;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2d8;" u2="&#x34;" k="74" />
    <hkern u1="&#x2d8;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2d9;" u2="&#x192;" k="56" />
    <hkern u1="&#x2d9;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2d9;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2d9;" u2="&#x34;" k="74" />
    <hkern u1="&#x2d9;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2da;" u2="&#x192;" k="56" />
    <hkern u1="&#x2da;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2da;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2da;" u2="&#x34;" k="74" />
    <hkern u1="&#x2da;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2db;" u2="&#xb6;" k="74" />
    <hkern u1="&#x2db;" u2="\" k="74" />
    <hkern u1="&#x2db;" u2="&#x3f;" k="74" />
    <hkern u1="&#x2db;" u2="&#x37;" k="93" />
    <hkern u1="&#x2db;" u2="&#x31;" k="37" />
    <hkern u1="&#x2db;" u2="&#x24;" k="9" />
    <hkern u1="&#x2dc;" u2="&#x192;" k="56" />
    <hkern u1="&#x2dc;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2dc;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2dc;" u2="&#x34;" k="74" />
    <hkern u1="&#x2dc;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2dd;" u2="&#x192;" k="56" />
    <hkern u1="&#x2dd;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2dd;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2dd;" u2="&#x34;" k="74" />
    <hkern u1="&#x2dd;" u2="&#x2f;" k="74" />
    <hkern u1="&#x326;" u2="&#xb6;" k="74" />
    <hkern u1="&#x326;" u2="\" k="74" />
    <hkern u1="&#x326;" u2="&#x3f;" k="74" />
    <hkern u1="&#x326;" u2="&#x37;" k="93" />
    <hkern u1="&#x326;" u2="&#x31;" k="37" />
    <hkern u1="&#x326;" u2="&#x24;" k="9" />
    <hkern u1="&#x3c0;" u2="&#x221e;" k="19" />
    <hkern u1="&#x3c0;" u2="&#xb6;" k="28" />
    <hkern u1="&#x3c0;" u2="&#xa4;" k="28" />
    <hkern u1="&#x3c0;" u2="&#xa2;" k="19" />
    <hkern u1="&#x3c0;" u2="\" k="93" />
    <hkern u1="&#x3c0;" u2="&#x3f;" k="56" />
    <hkern u1="&#x3c0;" u2="&#x37;" k="74" />
    <hkern u1="&#x3c0;" u2="&#x31;" k="37" />
    <hkern u1="&#x3c0;" u2="&#x24;" k="37" />
    <hkern u1="&#x3c0;" u2="&#x23;" k="19" />
    <hkern u1="&#x1e9e;" u2="\" k="19" />
    <hkern u1="&#x1e9e;" u2="&#x2f;" k="19" />
    <hkern u1="&#x1ea0;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea0;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea0;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea0;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea0;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea0;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea0;" u2="\" k="112" />
    <hkern u1="&#x1ea0;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea0;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea0;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea0;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea0;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea0;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea1;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea1;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea1;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea1;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea1;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea1;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea1;" u2="\" k="112" />
    <hkern u1="&#x1ea1;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea1;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea1;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea1;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea1;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea1;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea2;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea2;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea2;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea2;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea2;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea2;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea2;" u2="\" k="112" />
    <hkern u1="&#x1ea2;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea2;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea2;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea2;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea2;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea2;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea3;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea3;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea3;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea3;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea3;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea3;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea3;" u2="\" k="112" />
    <hkern u1="&#x1ea3;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea3;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea3;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea3;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea3;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea3;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea4;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea4;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea4;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea4;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea4;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea4;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea4;" u2="\" k="112" />
    <hkern u1="&#x1ea4;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea4;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea4;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea4;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea4;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea4;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea5;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea5;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea5;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea5;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea5;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea5;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea5;" u2="\" k="112" />
    <hkern u1="&#x1ea5;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea5;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea5;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea5;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea5;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea5;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea6;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea6;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea6;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea6;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea6;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea6;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea6;" u2="\" k="112" />
    <hkern u1="&#x1ea6;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea6;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea6;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea6;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea6;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea6;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea7;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea7;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea7;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea7;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea7;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea7;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea7;" u2="\" k="112" />
    <hkern u1="&#x1ea7;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea7;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea7;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea7;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea7;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea7;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea8;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea8;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea8;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea8;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea8;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea8;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea8;" u2="\" k="112" />
    <hkern u1="&#x1ea8;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea8;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea8;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea8;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea8;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea8;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ea9;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ea9;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ea9;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ea9;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ea9;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ea9;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ea9;" u2="\" k="112" />
    <hkern u1="&#x1ea9;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ea9;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ea9;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ea9;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ea9;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ea9;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eaa;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eaa;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eaa;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eaa;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eaa;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eaa;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eaa;" u2="\" k="112" />
    <hkern u1="&#x1eaa;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eaa;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eaa;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eaa;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eaa;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eaa;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eab;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eab;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eab;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eab;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eab;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eab;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eab;" u2="\" k="112" />
    <hkern u1="&#x1eab;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eab;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eab;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eab;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eab;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eab;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eac;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eac;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eac;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eac;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eac;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eac;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eac;" u2="\" k="112" />
    <hkern u1="&#x1eac;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eac;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eac;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eac;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eac;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eac;" u2="&#x23;" k="37" />
    <hkern u1="&#x1ead;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1ead;" u2="&#x192;" k="37" />
    <hkern u1="&#x1ead;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1ead;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1ead;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1ead;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1ead;" u2="\" k="112" />
    <hkern u1="&#x1ead;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1ead;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1ead;" u2="&#x37;" k="112" />
    <hkern u1="&#x1ead;" u2="&#x34;" k="19" />
    <hkern u1="&#x1ead;" u2="&#x24;" k="74" />
    <hkern u1="&#x1ead;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eae;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eae;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eae;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eae;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eae;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eae;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eae;" u2="\" k="112" />
    <hkern u1="&#x1eae;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eae;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eae;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eae;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eae;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eae;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eaf;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eaf;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eaf;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eaf;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eaf;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eaf;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eaf;" u2="\" k="112" />
    <hkern u1="&#x1eaf;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eaf;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eaf;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eaf;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eaf;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eaf;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb0;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eb0;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eb0;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eb0;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eb0;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eb0;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eb0;" u2="\" k="112" />
    <hkern u1="&#x1eb0;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eb0;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eb0;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eb0;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eb0;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eb0;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb1;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eb1;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eb1;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eb1;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eb1;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eb1;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eb1;" u2="\" k="112" />
    <hkern u1="&#x1eb1;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eb1;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eb1;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eb1;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eb1;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eb1;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb2;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eb2;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eb2;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eb2;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eb2;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eb2;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eb2;" u2="\" k="112" />
    <hkern u1="&#x1eb2;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eb2;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eb2;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eb2;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eb2;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eb2;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb3;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eb3;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eb3;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eb3;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eb3;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eb3;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eb3;" u2="\" k="112" />
    <hkern u1="&#x1eb3;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eb3;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eb3;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eb3;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eb3;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eb3;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb4;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eb4;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eb4;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eb4;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eb4;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eb4;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eb4;" u2="\" k="112" />
    <hkern u1="&#x1eb4;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eb4;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eb4;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eb4;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eb4;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eb4;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb5;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eb5;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eb5;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eb5;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eb5;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eb5;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eb5;" u2="\" k="112" />
    <hkern u1="&#x1eb5;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eb5;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eb5;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eb5;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eb5;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eb5;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb6;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eb6;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eb6;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eb6;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eb6;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eb6;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eb6;" u2="\" k="112" />
    <hkern u1="&#x1eb6;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eb6;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eb6;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eb6;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eb6;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eb6;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb7;" u2="&#x221e;" k="37" />
    <hkern u1="&#x1eb7;" u2="&#x192;" k="37" />
    <hkern u1="&#x1eb7;" u2="&#xb6;" k="65" />
    <hkern u1="&#x1eb7;" u2="&#xa4;" k="56" />
    <hkern u1="&#x1eb7;" u2="&#xa3;" k="19" />
    <hkern u1="&#x1eb7;" u2="&#xa2;" k="47" />
    <hkern u1="&#x1eb7;" u2="\" k="112" />
    <hkern u1="&#x1eb7;" u2="&#x3f;" k="84" />
    <hkern u1="&#x1eb7;" u2="&#x3a;" k="28" />
    <hkern u1="&#x1eb7;" u2="&#x37;" k="112" />
    <hkern u1="&#x1eb7;" u2="&#x34;" k="19" />
    <hkern u1="&#x1eb7;" u2="&#x24;" k="74" />
    <hkern u1="&#x1eb7;" u2="&#x23;" k="37" />
    <hkern u1="&#x1eb8;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1eb8;" u2="&#x192;" k="19" />
    <hkern u1="&#x1eb8;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1eb8;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1eb8;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1eb8;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1eb8;" u2="&#x24;" k="19" />
    <hkern u1="&#x1eb8;" u2="&#x23;" k="19" />
    <hkern u1="&#x1eb9;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1eb9;" u2="&#x192;" k="19" />
    <hkern u1="&#x1eb9;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1eb9;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1eb9;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1eb9;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1eb9;" u2="&#x24;" k="19" />
    <hkern u1="&#x1eb9;" u2="&#x23;" k="19" />
    <hkern u1="&#x1eba;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1eba;" u2="&#x192;" k="19" />
    <hkern u1="&#x1eba;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1eba;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1eba;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1eba;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1eba;" u2="&#x24;" k="19" />
    <hkern u1="&#x1eba;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ebb;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ebb;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ebb;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ebb;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ebb;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ebb;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ebb;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ebb;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ebc;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ebc;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ebc;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ebc;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ebc;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ebc;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ebc;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ebc;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ebd;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ebd;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ebd;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ebd;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ebd;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ebd;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ebd;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ebd;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ebe;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ebe;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ebe;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ebe;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ebe;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ebe;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ebe;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ebe;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ebf;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ebf;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ebf;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ebf;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ebf;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ebf;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ebf;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ebf;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ec0;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ec0;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ec0;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ec0;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ec0;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ec0;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ec0;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ec0;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ec1;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ec1;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ec1;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ec1;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ec1;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ec1;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ec1;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ec1;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ec2;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ec2;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ec2;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ec2;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ec2;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ec2;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ec2;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ec2;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ec3;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ec3;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ec3;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ec3;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ec3;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ec3;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ec3;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ec3;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ec4;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ec4;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ec4;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ec4;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ec4;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ec4;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ec4;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ec4;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ec5;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ec5;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ec5;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ec5;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ec5;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ec5;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ec5;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ec5;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ec6;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ec6;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ec6;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ec6;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ec6;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ec6;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ec6;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ec6;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ec7;" u2="&#x221e;" k="19" />
    <hkern u1="&#x1ec7;" u2="&#x192;" k="19" />
    <hkern u1="&#x1ec7;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ec7;" u2="&#xa4;" k="28" />
    <hkern u1="&#x1ec7;" u2="&#xa2;" k="19" />
    <hkern u1="&#x1ec7;" u2="&#x3a;" k="19" />
    <hkern u1="&#x1ec7;" u2="&#x24;" k="19" />
    <hkern u1="&#x1ec7;" u2="&#x23;" k="19" />
    <hkern u1="&#x1ef2;" u2="&#x221e;" k="74" />
    <hkern u1="&#x1ef2;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x1ef2;" u2="&#x192;" k="121" />
    <hkern u1="&#x1ef2;" u2="&#xbf;" k="168" />
    <hkern u1="&#x1ef2;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ef2;" u2="&#xa4;" k="84" />
    <hkern u1="&#x1ef2;" u2="&#xa3;" k="93" />
    <hkern u1="&#x1ef2;" u2="&#xa2;" k="65" />
    <hkern u1="&#x1ef2;" u2="&#x3a;" k="47" />
    <hkern u1="&#x1ef2;" u2="&#x39;" k="19" />
    <hkern u1="&#x1ef2;" u2="&#x34;" k="130" />
    <hkern u1="&#x1ef2;" u2="&#x33;" k="19" />
    <hkern u1="&#x1ef2;" u2="&#x32;" k="19" />
    <hkern u1="&#x1ef2;" u2="&#x2f;" k="130" />
    <hkern u1="&#x1ef2;" u2="&#x24;" k="65" />
    <hkern u1="&#x1ef2;" u2="&#x23;" k="74" />
    <hkern u1="&#x1ef3;" u2="&#x221e;" k="74" />
    <hkern u1="&#x1ef3;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x1ef3;" u2="&#x192;" k="121" />
    <hkern u1="&#x1ef3;" u2="&#xbf;" k="168" />
    <hkern u1="&#x1ef3;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ef3;" u2="&#xa4;" k="84" />
    <hkern u1="&#x1ef3;" u2="&#xa3;" k="93" />
    <hkern u1="&#x1ef3;" u2="&#xa2;" k="65" />
    <hkern u1="&#x1ef3;" u2="&#x3a;" k="47" />
    <hkern u1="&#x1ef3;" u2="&#x39;" k="19" />
    <hkern u1="&#x1ef3;" u2="&#x34;" k="130" />
    <hkern u1="&#x1ef3;" u2="&#x33;" k="19" />
    <hkern u1="&#x1ef3;" u2="&#x32;" k="19" />
    <hkern u1="&#x1ef3;" u2="&#x2f;" k="130" />
    <hkern u1="&#x1ef3;" u2="&#x24;" k="65" />
    <hkern u1="&#x1ef3;" u2="&#x23;" k="74" />
    <hkern u1="&#x1ef4;" u2="&#x221e;" k="74" />
    <hkern u1="&#x1ef4;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x1ef4;" u2="&#x192;" k="121" />
    <hkern u1="&#x1ef4;" u2="&#xbf;" k="168" />
    <hkern u1="&#x1ef4;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ef4;" u2="&#xa4;" k="84" />
    <hkern u1="&#x1ef4;" u2="&#xa3;" k="93" />
    <hkern u1="&#x1ef4;" u2="&#xa2;" k="65" />
    <hkern u1="&#x1ef4;" u2="&#x3a;" k="47" />
    <hkern u1="&#x1ef4;" u2="&#x39;" k="19" />
    <hkern u1="&#x1ef4;" u2="&#x34;" k="130" />
    <hkern u1="&#x1ef4;" u2="&#x33;" k="19" />
    <hkern u1="&#x1ef4;" u2="&#x32;" k="19" />
    <hkern u1="&#x1ef4;" u2="&#x2f;" k="130" />
    <hkern u1="&#x1ef4;" u2="&#x24;" k="65" />
    <hkern u1="&#x1ef4;" u2="&#x23;" k="74" />
    <hkern u1="&#x1ef5;" u2="&#x221e;" k="74" />
    <hkern u1="&#x1ef5;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x1ef5;" u2="&#x192;" k="121" />
    <hkern u1="&#x1ef5;" u2="&#xbf;" k="168" />
    <hkern u1="&#x1ef5;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ef5;" u2="&#xa4;" k="84" />
    <hkern u1="&#x1ef5;" u2="&#xa3;" k="93" />
    <hkern u1="&#x1ef5;" u2="&#xa2;" k="65" />
    <hkern u1="&#x1ef5;" u2="&#x3a;" k="47" />
    <hkern u1="&#x1ef5;" u2="&#x39;" k="19" />
    <hkern u1="&#x1ef5;" u2="&#x34;" k="130" />
    <hkern u1="&#x1ef5;" u2="&#x33;" k="19" />
    <hkern u1="&#x1ef5;" u2="&#x32;" k="19" />
    <hkern u1="&#x1ef5;" u2="&#x2f;" k="130" />
    <hkern u1="&#x1ef5;" u2="&#x24;" k="65" />
    <hkern u1="&#x1ef5;" u2="&#x23;" k="74" />
    <hkern u1="&#x1ef6;" u2="&#x221e;" k="74" />
    <hkern u1="&#x1ef6;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x1ef6;" u2="&#x192;" k="121" />
    <hkern u1="&#x1ef6;" u2="&#xbf;" k="168" />
    <hkern u1="&#x1ef6;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ef6;" u2="&#xa4;" k="84" />
    <hkern u1="&#x1ef6;" u2="&#xa3;" k="93" />
    <hkern u1="&#x1ef6;" u2="&#xa2;" k="65" />
    <hkern u1="&#x1ef6;" u2="&#x3a;" k="47" />
    <hkern u1="&#x1ef6;" u2="&#x39;" k="19" />
    <hkern u1="&#x1ef6;" u2="&#x34;" k="130" />
    <hkern u1="&#x1ef6;" u2="&#x33;" k="19" />
    <hkern u1="&#x1ef6;" u2="&#x32;" k="19" />
    <hkern u1="&#x1ef6;" u2="&#x2f;" k="130" />
    <hkern u1="&#x1ef6;" u2="&#x24;" k="65" />
    <hkern u1="&#x1ef6;" u2="&#x23;" k="74" />
    <hkern u1="&#x1ef7;" u2="&#x221e;" k="74" />
    <hkern u1="&#x1ef7;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x1ef7;" u2="&#x192;" k="121" />
    <hkern u1="&#x1ef7;" u2="&#xbf;" k="168" />
    <hkern u1="&#x1ef7;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ef7;" u2="&#xa4;" k="84" />
    <hkern u1="&#x1ef7;" u2="&#xa3;" k="93" />
    <hkern u1="&#x1ef7;" u2="&#xa2;" k="65" />
    <hkern u1="&#x1ef7;" u2="&#x3a;" k="47" />
    <hkern u1="&#x1ef7;" u2="&#x39;" k="19" />
    <hkern u1="&#x1ef7;" u2="&#x34;" k="130" />
    <hkern u1="&#x1ef7;" u2="&#x33;" k="19" />
    <hkern u1="&#x1ef7;" u2="&#x32;" k="19" />
    <hkern u1="&#x1ef7;" u2="&#x2f;" k="130" />
    <hkern u1="&#x1ef7;" u2="&#x24;" k="65" />
    <hkern u1="&#x1ef7;" u2="&#x23;" k="74" />
    <hkern u1="&#x1ef8;" u2="&#x221e;" k="74" />
    <hkern u1="&#x1ef8;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x1ef8;" u2="&#x192;" k="121" />
    <hkern u1="&#x1ef8;" u2="&#xbf;" k="168" />
    <hkern u1="&#x1ef8;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ef8;" u2="&#xa4;" k="84" />
    <hkern u1="&#x1ef8;" u2="&#xa3;" k="93" />
    <hkern u1="&#x1ef8;" u2="&#xa2;" k="65" />
    <hkern u1="&#x1ef8;" u2="&#x3a;" k="47" />
    <hkern u1="&#x1ef8;" u2="&#x39;" k="19" />
    <hkern u1="&#x1ef8;" u2="&#x34;" k="130" />
    <hkern u1="&#x1ef8;" u2="&#x33;" k="19" />
    <hkern u1="&#x1ef8;" u2="&#x32;" k="19" />
    <hkern u1="&#x1ef8;" u2="&#x2f;" k="130" />
    <hkern u1="&#x1ef8;" u2="&#x24;" k="65" />
    <hkern u1="&#x1ef8;" u2="&#x23;" k="74" />
    <hkern u1="&#x1ef9;" u2="&#x221e;" k="74" />
    <hkern u1="&#x1ef9;" u2="&#x3c0;" k="47" />
    <hkern u1="&#x1ef9;" u2="&#x192;" k="121" />
    <hkern u1="&#x1ef9;" u2="&#xbf;" k="168" />
    <hkern u1="&#x1ef9;" u2="&#xb6;" k="19" />
    <hkern u1="&#x1ef9;" u2="&#xa4;" k="84" />
    <hkern u1="&#x1ef9;" u2="&#xa3;" k="93" />
    <hkern u1="&#x1ef9;" u2="&#xa2;" k="65" />
    <hkern u1="&#x1ef9;" u2="&#x3a;" k="47" />
    <hkern u1="&#x1ef9;" u2="&#x39;" k="19" />
    <hkern u1="&#x1ef9;" u2="&#x34;" k="130" />
    <hkern u1="&#x1ef9;" u2="&#x33;" k="19" />
    <hkern u1="&#x1ef9;" u2="&#x32;" k="19" />
    <hkern u1="&#x1ef9;" u2="&#x2f;" k="130" />
    <hkern u1="&#x1ef9;" u2="&#x24;" k="65" />
    <hkern u1="&#x1ef9;" u2="&#x23;" k="74" />
    <hkern u1="&#x2013;" u2="&#x192;" k="19" />
    <hkern u1="&#x2013;" u2="&#xbf;" k="37" />
    <hkern u1="&#x2013;" u2="\" k="56" />
    <hkern u1="&#x2013;" u2="&#x3f;" k="19" />
    <hkern u1="&#x2013;" u2="&#x37;" k="74" />
    <hkern u1="&#x2013;" u2="&#x33;" k="19" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="56" />
    <hkern u1="&#x2014;" u2="&#x192;" k="19" />
    <hkern u1="&#x2014;" u2="&#xbf;" k="37" />
    <hkern u1="&#x2014;" u2="\" k="56" />
    <hkern u1="&#x2014;" u2="&#x3f;" k="19" />
    <hkern u1="&#x2014;" u2="&#x37;" k="74" />
    <hkern u1="&#x2014;" u2="&#x33;" k="19" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="56" />
    <hkern u1="&#x2018;" u2="&#x192;" k="56" />
    <hkern u1="&#x2018;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2018;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2018;" u2="&#x34;" k="74" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2019;" u2="&#x192;" k="56" />
    <hkern u1="&#x2019;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2019;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2019;" u2="&#x34;" k="74" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="74" />
    <hkern u1="&#x201a;" u2="&#xb6;" k="74" />
    <hkern u1="&#x201a;" u2="\" k="74" />
    <hkern u1="&#x201a;" u2="&#x3f;" k="74" />
    <hkern u1="&#x201a;" u2="&#x37;" k="93" />
    <hkern u1="&#x201a;" u2="&#x31;" k="37" />
    <hkern u1="&#x201a;" u2="&#x24;" k="9" />
    <hkern u1="&#x201c;" u2="&#x192;" k="56" />
    <hkern u1="&#x201c;" u2="&#xbf;" k="112" />
    <hkern u1="&#x201c;" u2="&#xa3;" k="37" />
    <hkern u1="&#x201c;" u2="&#x34;" k="74" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="74" />
    <hkern u1="&#x201d;" u2="&#x192;" k="56" />
    <hkern u1="&#x201d;" u2="&#xbf;" k="112" />
    <hkern u1="&#x201d;" u2="&#xa3;" k="37" />
    <hkern u1="&#x201d;" u2="&#x34;" k="74" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="74" />
    <hkern u1="&#x201e;" u2="&#xb6;" k="74" />
    <hkern u1="&#x201e;" u2="\" k="74" />
    <hkern u1="&#x201e;" u2="&#x3f;" k="74" />
    <hkern u1="&#x201e;" u2="&#x37;" k="93" />
    <hkern u1="&#x201e;" u2="&#x31;" k="37" />
    <hkern u1="&#x201e;" u2="&#x24;" k="9" />
    <hkern u1="&#x2020;" u2="&#x192;" k="56" />
    <hkern u1="&#x2020;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2020;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2020;" u2="&#x34;" k="74" />
    <hkern u1="&#x2020;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2021;" u2="&#x192;" k="56" />
    <hkern u1="&#x2021;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2021;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2021;" u2="&#x34;" k="74" />
    <hkern u1="&#x2021;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2022;" u2="&#x192;" k="19" />
    <hkern u1="&#x2022;" u2="&#xbf;" k="37" />
    <hkern u1="&#x2022;" u2="\" k="56" />
    <hkern u1="&#x2022;" u2="&#x3f;" k="19" />
    <hkern u1="&#x2022;" u2="&#x37;" k="74" />
    <hkern u1="&#x2022;" u2="&#x33;" k="19" />
    <hkern u1="&#x2022;" u2="&#x2f;" k="56" />
    <hkern u1="&#x2026;" u2="&#xb6;" k="74" />
    <hkern u1="&#x2026;" u2="\" k="74" />
    <hkern u1="&#x2026;" u2="&#x3f;" k="74" />
    <hkern u1="&#x2026;" u2="&#x37;" k="93" />
    <hkern u1="&#x2026;" u2="&#x31;" k="37" />
    <hkern u1="&#x2026;" u2="&#x24;" k="9" />
    <hkern u1="&#x2030;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2030;" u2="&#x37;" k="93" />
    <hkern u1="&#x2030;" u2="&#x31;" k="37" />
    <hkern u1="&#x2039;" u2="&#x192;" k="9" />
    <hkern u1="&#x2039;" u2="&#xbf;" k="19" />
    <hkern u1="&#x2039;" u2="\" k="28" />
    <hkern u1="&#x2039;" u2="&#x3f;" k="9" />
    <hkern u1="&#x2039;" u2="&#x37;" k="37" />
    <hkern u1="&#x2039;" u2="&#x33;" k="9" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="28" />
    <hkern u1="&#x203a;" u2="&#x192;" k="9" />
    <hkern u1="&#x203a;" u2="&#xbf;" k="19" />
    <hkern u1="&#x203a;" u2="\" k="28" />
    <hkern u1="&#x203a;" u2="&#x3f;" k="9" />
    <hkern u1="&#x203a;" u2="&#x37;" k="37" />
    <hkern u1="&#x203a;" u2="&#x33;" k="9" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="28" />
    <hkern u1="&#x2070;" u2="&#xbf;" k="74" />
    <hkern u1="&#x2070;" u2="&#xa3;" k="19" />
    <hkern u1="&#x2070;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2074;" u2="&#xbf;" k="74" />
    <hkern u1="&#x2074;" u2="&#xa3;" k="19" />
    <hkern u1="&#x2074;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2075;" u2="&#xbf;" k="74" />
    <hkern u1="&#x2075;" u2="&#xa3;" k="19" />
    <hkern u1="&#x2075;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2076;" u2="&#xbf;" k="74" />
    <hkern u1="&#x2076;" u2="&#xa3;" k="19" />
    <hkern u1="&#x2076;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2077;" u2="&#xbf;" k="74" />
    <hkern u1="&#x2077;" u2="&#xa3;" k="19" />
    <hkern u1="&#x2077;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2078;" u2="&#xbf;" k="74" />
    <hkern u1="&#x2078;" u2="&#xa3;" k="19" />
    <hkern u1="&#x2078;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2079;" u2="&#xbf;" k="74" />
    <hkern u1="&#x2079;" u2="&#xa3;" k="19" />
    <hkern u1="&#x2079;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2080;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2080;" u2="&#x37;" k="93" />
    <hkern u1="&#x2080;" u2="&#x31;" k="37" />
    <hkern u1="&#x2081;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2081;" u2="&#x37;" k="93" />
    <hkern u1="&#x2081;" u2="&#x31;" k="37" />
    <hkern u1="&#x2082;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2082;" u2="&#x37;" k="93" />
    <hkern u1="&#x2082;" u2="&#x31;" k="37" />
    <hkern u1="&#x2083;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2083;" u2="&#x37;" k="93" />
    <hkern u1="&#x2083;" u2="&#x31;" k="37" />
    <hkern u1="&#x2084;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2084;" u2="&#x37;" k="93" />
    <hkern u1="&#x2084;" u2="&#x31;" k="37" />
    <hkern u1="&#x2085;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2085;" u2="&#x37;" k="93" />
    <hkern u1="&#x2085;" u2="&#x31;" k="37" />
    <hkern u1="&#x2086;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2086;" u2="&#x37;" k="93" />
    <hkern u1="&#x2086;" u2="&#x31;" k="37" />
    <hkern u1="&#x2087;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2087;" u2="&#x37;" k="93" />
    <hkern u1="&#x2087;" u2="&#x31;" k="37" />
    <hkern u1="&#x2088;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2088;" u2="&#x37;" k="93" />
    <hkern u1="&#x2088;" u2="&#x31;" k="37" />
    <hkern u1="&#x2089;" u2="&#x3f;" k="37" />
    <hkern u1="&#x2089;" u2="&#x37;" k="93" />
    <hkern u1="&#x2089;" u2="&#x31;" k="37" />
    <hkern u1="&#x20ac;" g2="ae.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="atilde.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="aring.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="aogonek.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="amacron.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="agrave.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="adieresis.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="acircumflex.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="abreve.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="aacute.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="a.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="AE.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Atilde.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Aring.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Aogonek.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Amacron.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Agrave.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Adieresis.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Acircumflex.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Abreve.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="Aacute.ss01" k="28" />
    <hkern u1="&#x20ac;" g2="A.ss01" k="28" />
    <hkern u1="&#x20ac;" u2="&#x2265;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x2264;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x2260;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x2248;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x2219;" k="37" />
    <hkern u1="&#x20ac;" u2="&#x2212;" k="37" />
    <hkern u1="&#x20ac;" u2="&#x203a;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x2039;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x2022;" k="37" />
    <hkern u1="&#x20ac;" u2="&#x2014;" k="37" />
    <hkern u1="&#x20ac;" u2="&#x2013;" k="37" />
    <hkern u1="&#x20ac;" u2="&#x1ef9;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x1ef8;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x1ef7;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x1ef6;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x1ef5;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x1ef4;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x1ef3;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x1ef2;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x1eb7;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eb6;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eb5;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eb4;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eb3;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eb2;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eb1;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eb0;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eaf;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eae;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ead;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eac;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eab;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1eaa;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea9;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea8;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea7;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea6;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea5;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea4;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea3;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea2;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea1;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x1ea0;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x178;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x105;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x104;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x103;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x102;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x101;" k="28" />
    <hkern u1="&#x20ac;" u2="&#x100;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xff;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xfd;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xf7;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xe6;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xe5;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xe4;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xe3;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xe2;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xe1;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xe0;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xdd;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xd7;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xc6;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xc5;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xc4;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xc3;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xc2;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xc1;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xc0;" k="28" />
    <hkern u1="&#x20ac;" u2="&#xbb;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xb7;" k="37" />
    <hkern u1="&#x20ac;" u2="&#xb1;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xad;" k="37" />
    <hkern u1="&#x20ac;" u2="&#xac;" k="37" />
    <hkern u1="&#x20ac;" u2="&#xab;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xa5;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x7e;" k="37" />
    <hkern u1="&#x20ac;" u2="&#x7b;" k="37" />
    <hkern u1="&#x20ac;" u2="y" k="19" />
    <hkern u1="&#x20ac;" u2="x" k="19" />
    <hkern u1="&#x20ac;" u2="w" k="9" />
    <hkern u1="&#x20ac;" u2="v" k="9" />
    <hkern u1="&#x20ac;" u2="a" k="28" />
    <hkern u1="&#x20ac;" u2="Y" k="19" />
    <hkern u1="&#x20ac;" u2="X" k="19" />
    <hkern u1="&#x20ac;" u2="W" k="9" />
    <hkern u1="&#x20ac;" u2="V" k="9" />
    <hkern u1="&#x20ac;" u2="A" k="28" />
    <hkern u1="&#x20ac;" u2="&#x3e;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x3d;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x3c;" k="37" />
    <hkern u1="&#x20ac;" u2="&#x2d;" k="37" />
    <hkern u1="&#x20ac;" u2="&#x2b;" k="19" />
    <hkern u1="&#x20ac;" u2="&#x221e;" k="19" />
    <hkern u1="&#x20ac;" u2="&#xa4;" k="19" />
    <hkern u1="&#x20ac;" u2="\" k="19" />
    <hkern u1="&#x20ac;" u2="&#x2f;" k="19" />
    <hkern u1="&#x2122;" u2="&#x192;" k="56" />
    <hkern u1="&#x2122;" u2="&#xbf;" k="112" />
    <hkern u1="&#x2122;" u2="&#xa3;" k="37" />
    <hkern u1="&#x2122;" u2="&#x34;" k="74" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="74" />
    <hkern u1="&#x2212;" u2="&#x192;" k="19" />
    <hkern u1="&#x2212;" u2="&#xbf;" k="37" />
    <hkern u1="&#x2212;" u2="\" k="56" />
    <hkern u1="&#x2212;" u2="&#x3f;" k="19" />
    <hkern u1="&#x2212;" u2="&#x37;" k="74" />
    <hkern u1="&#x2212;" u2="&#x33;" k="19" />
    <hkern u1="&#x2212;" u2="&#x2f;" k="56" />
    <hkern u1="&#x2219;" u2="&#x192;" k="19" />
    <hkern u1="&#x2219;" u2="&#xbf;" k="37" />
    <hkern u1="&#x2219;" u2="\" k="56" />
    <hkern u1="&#x2219;" u2="&#x3f;" k="19" />
    <hkern u1="&#x2219;" u2="&#x37;" k="74" />
    <hkern u1="&#x2219;" u2="&#x33;" k="19" />
    <hkern u1="&#x2219;" u2="&#x2f;" k="56" />
    <hkern u1="&#x221e;" u2="&#x192;" k="19" />
    <hkern u1="&#x221e;" u2="\" k="37" />
    <hkern u1="&#x221e;" u2="&#x37;" k="19" />
    <hkern u1="&#x221e;" u2="&#x2f;" k="37" />
    <hkern u1="&#x2248;" u2="&#x192;" k="9" />
    <hkern u1="&#x2248;" u2="&#xbf;" k="19" />
    <hkern u1="&#x2248;" u2="\" k="28" />
    <hkern u1="&#x2248;" u2="&#x3f;" k="9" />
    <hkern u1="&#x2248;" u2="&#x37;" k="37" />
    <hkern u1="&#x2248;" u2="&#x33;" k="9" />
    <hkern u1="&#x2248;" u2="&#x2f;" k="28" />
    <hkern u1="&#x2260;" u2="&#x192;" k="9" />
    <hkern u1="&#x2260;" u2="&#xbf;" k="19" />
    <hkern u1="&#x2260;" u2="\" k="28" />
    <hkern u1="&#x2260;" u2="&#x3f;" k="9" />
    <hkern u1="&#x2260;" u2="&#x37;" k="37" />
    <hkern u1="&#x2260;" u2="&#x33;" k="9" />
    <hkern u1="&#x2260;" u2="&#x2f;" k="28" />
    <hkern u1="&#x2264;" u2="&#x192;" k="9" />
    <hkern u1="&#x2264;" u2="&#xbf;" k="19" />
    <hkern u1="&#x2264;" u2="\" k="28" />
    <hkern u1="&#x2264;" u2="&#x3f;" k="9" />
    <hkern u1="&#x2264;" u2="&#x37;" k="37" />
    <hkern u1="&#x2264;" u2="&#x33;" k="9" />
    <hkern u1="&#x2264;" u2="&#x2f;" k="28" />
    <hkern u1="&#x2265;" u2="&#x192;" k="9" />
    <hkern u1="&#x2265;" u2="&#xbf;" k="19" />
    <hkern u1="&#x2265;" u2="\" k="28" />
    <hkern u1="&#x2265;" u2="&#x3f;" k="9" />
    <hkern u1="&#x2265;" u2="&#x37;" k="37" />
    <hkern u1="&#x2265;" u2="&#x33;" k="9" />
    <hkern u1="&#x2265;" u2="&#x2f;" k="28" />
    <hkern g1="A.ss01" u2="&#x221e;" k="37" />
    <hkern g1="A.ss01" u2="&#x192;" k="37" />
    <hkern g1="A.ss01" u2="&#xb6;" k="65" />
    <hkern g1="A.ss01" u2="&#xa4;" k="56" />
    <hkern g1="A.ss01" u2="&#xa3;" k="19" />
    <hkern g1="A.ss01" u2="&#xa2;" k="47" />
    <hkern g1="A.ss01" u2="\" k="112" />
    <hkern g1="A.ss01" u2="&#x3f;" k="84" />
    <hkern g1="A.ss01" u2="&#x3a;" k="28" />
    <hkern g1="A.ss01" u2="&#x37;" k="112" />
    <hkern g1="A.ss01" u2="&#x34;" k="19" />
    <hkern g1="A.ss01" u2="&#x24;" k="74" />
    <hkern g1="A.ss01" u2="&#x23;" k="37" />
    <hkern g1="Aacute.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Aacute.ss01" u2="&#x192;" k="37" />
    <hkern g1="Aacute.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Aacute.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Aacute.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Aacute.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Aacute.ss01" u2="\" k="112" />
    <hkern g1="Aacute.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Aacute.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Aacute.ss01" u2="&#x37;" k="112" />
    <hkern g1="Aacute.ss01" u2="&#x34;" k="19" />
    <hkern g1="Aacute.ss01" u2="&#x24;" k="74" />
    <hkern g1="Aacute.ss01" u2="&#x23;" k="37" />
    <hkern g1="Abreve.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Abreve.ss01" u2="&#x192;" k="37" />
    <hkern g1="Abreve.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Abreve.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Abreve.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Abreve.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Abreve.ss01" u2="\" k="112" />
    <hkern g1="Abreve.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Abreve.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Abreve.ss01" u2="&#x37;" k="112" />
    <hkern g1="Abreve.ss01" u2="&#x34;" k="19" />
    <hkern g1="Abreve.ss01" u2="&#x24;" k="74" />
    <hkern g1="Abreve.ss01" u2="&#x23;" k="37" />
    <hkern g1="Acircumflex.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Acircumflex.ss01" u2="&#x192;" k="37" />
    <hkern g1="Acircumflex.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Acircumflex.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Acircumflex.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Acircumflex.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Acircumflex.ss01" u2="\" k="112" />
    <hkern g1="Acircumflex.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Acircumflex.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Acircumflex.ss01" u2="&#x37;" k="112" />
    <hkern g1="Acircumflex.ss01" u2="&#x34;" k="19" />
    <hkern g1="Acircumflex.ss01" u2="&#x24;" k="74" />
    <hkern g1="Acircumflex.ss01" u2="&#x23;" k="37" />
    <hkern g1="Adieresis.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Adieresis.ss01" u2="&#x192;" k="37" />
    <hkern g1="Adieresis.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Adieresis.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Adieresis.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Adieresis.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Adieresis.ss01" u2="\" k="112" />
    <hkern g1="Adieresis.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Adieresis.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Adieresis.ss01" u2="&#x37;" k="112" />
    <hkern g1="Adieresis.ss01" u2="&#x34;" k="19" />
    <hkern g1="Adieresis.ss01" u2="&#x24;" k="74" />
    <hkern g1="Adieresis.ss01" u2="&#x23;" k="37" />
    <hkern g1="Agrave.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Agrave.ss01" u2="&#x192;" k="37" />
    <hkern g1="Agrave.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Agrave.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Agrave.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Agrave.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Agrave.ss01" u2="\" k="112" />
    <hkern g1="Agrave.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Agrave.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Agrave.ss01" u2="&#x37;" k="112" />
    <hkern g1="Agrave.ss01" u2="&#x34;" k="19" />
    <hkern g1="Agrave.ss01" u2="&#x24;" k="74" />
    <hkern g1="Agrave.ss01" u2="&#x23;" k="37" />
    <hkern g1="Amacron.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Amacron.ss01" u2="&#x192;" k="37" />
    <hkern g1="Amacron.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Amacron.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Amacron.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Amacron.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Amacron.ss01" u2="\" k="112" />
    <hkern g1="Amacron.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Amacron.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Amacron.ss01" u2="&#x37;" k="112" />
    <hkern g1="Amacron.ss01" u2="&#x34;" k="19" />
    <hkern g1="Amacron.ss01" u2="&#x24;" k="74" />
    <hkern g1="Amacron.ss01" u2="&#x23;" k="37" />
    <hkern g1="Aogonek.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Aogonek.ss01" u2="&#x192;" k="37" />
    <hkern g1="Aogonek.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Aogonek.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Aogonek.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Aogonek.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Aogonek.ss01" u2="\" k="112" />
    <hkern g1="Aogonek.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Aogonek.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Aogonek.ss01" u2="&#x37;" k="112" />
    <hkern g1="Aogonek.ss01" u2="&#x34;" k="19" />
    <hkern g1="Aogonek.ss01" u2="&#x24;" k="74" />
    <hkern g1="Aogonek.ss01" u2="&#x23;" k="37" />
    <hkern g1="Aring.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Aring.ss01" u2="&#x192;" k="37" />
    <hkern g1="Aring.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Aring.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Aring.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Aring.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Aring.ss01" u2="\" k="112" />
    <hkern g1="Aring.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Aring.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Aring.ss01" u2="&#x37;" k="112" />
    <hkern g1="Aring.ss01" u2="&#x34;" k="19" />
    <hkern g1="Aring.ss01" u2="&#x24;" k="74" />
    <hkern g1="Aring.ss01" u2="&#x23;" k="37" />
    <hkern g1="Atilde.ss01" u2="&#x221e;" k="37" />
    <hkern g1="Atilde.ss01" u2="&#x192;" k="37" />
    <hkern g1="Atilde.ss01" u2="&#xb6;" k="65" />
    <hkern g1="Atilde.ss01" u2="&#xa4;" k="56" />
    <hkern g1="Atilde.ss01" u2="&#xa3;" k="19" />
    <hkern g1="Atilde.ss01" u2="&#xa2;" k="47" />
    <hkern g1="Atilde.ss01" u2="\" k="112" />
    <hkern g1="Atilde.ss01" u2="&#x3f;" k="84" />
    <hkern g1="Atilde.ss01" u2="&#x3a;" k="28" />
    <hkern g1="Atilde.ss01" u2="&#x37;" k="112" />
    <hkern g1="Atilde.ss01" u2="&#x34;" k="19" />
    <hkern g1="Atilde.ss01" u2="&#x24;" k="74" />
    <hkern g1="Atilde.ss01" u2="&#x23;" k="37" />
    <hkern g1="AE.ss01" u2="&#x221e;" k="19" />
    <hkern g1="AE.ss01" u2="&#x192;" k="19" />
    <hkern g1="AE.ss01" u2="&#xb6;" k="19" />
    <hkern g1="AE.ss01" u2="&#xa4;" k="28" />
    <hkern g1="AE.ss01" u2="&#xa2;" k="19" />
    <hkern g1="AE.ss01" u2="&#x3a;" k="19" />
    <hkern g1="AE.ss01" u2="&#x24;" k="19" />
    <hkern g1="AE.ss01" u2="&#x23;" k="19" />
    <hkern g1="B.ss01" u2="\" k="19" />
    <hkern g1="B.ss01" u2="&#x2f;" k="19" />
    <hkern g1="D.ss01" u2="\" k="19" />
    <hkern g1="D.ss01" u2="&#x2f;" k="19" />
    <hkern g1="Eth.ss01" u2="\" k="19" />
    <hkern g1="Eth.ss01" u2="&#x2f;" k="19" />
    <hkern g1="Dcaron.ss01" u2="\" k="19" />
    <hkern g1="Dcaron.ss01" u2="&#x2f;" k="19" />
    <hkern g1="Dcroat.ss01" u2="\" k="19" />
    <hkern g1="Dcroat.ss01" u2="&#x2f;" k="19" />
    <hkern g1="E.ss01" u2="&#x221e;" k="19" />
    <hkern g1="E.ss01" u2="&#x192;" k="19" />
    <hkern g1="E.ss01" u2="&#xb6;" k="19" />
    <hkern g1="E.ss01" u2="&#xa4;" k="28" />
    <hkern g1="E.ss01" u2="&#xa2;" k="19" />
    <hkern g1="E.ss01" u2="&#x3a;" k="19" />
    <hkern g1="E.ss01" u2="&#x24;" k="19" />
    <hkern g1="E.ss01" u2="&#x23;" k="19" />
    <hkern g1="Eacute.ss01" u2="&#x221e;" k="19" />
    <hkern g1="Eacute.ss01" u2="&#x192;" k="19" />
    <hkern g1="Eacute.ss01" u2="&#xb6;" k="19" />
    <hkern g1="Eacute.ss01" u2="&#xa4;" k="28" />
    <hkern g1="Eacute.ss01" u2="&#xa2;" k="19" />
    <hkern g1="Eacute.ss01" u2="&#x3a;" k="19" />
    <hkern g1="Eacute.ss01" u2="&#x24;" k="19" />
    <hkern g1="Eacute.ss01" u2="&#x23;" k="19" />
    <hkern g1="Ecaron.ss01" u2="&#x221e;" k="19" />
    <hkern g1="Ecaron.ss01" u2="&#x192;" k="19" />
    <hkern g1="Ecaron.ss01" u2="&#xb6;" k="19" />
    <hkern g1="Ecaron.ss01" u2="&#xa4;" k="28" />
    <hkern g1="Ecaron.ss01" u2="&#xa2;" k="19" />
    <hkern g1="Ecaron.ss01" u2="&#x3a;" k="19" />
    <hkern g1="Ecaron.ss01" u2="&#x24;" k="19" />
    <hkern g1="Ecaron.ss01" u2="&#x23;" k="19" />
    <hkern g1="Ecircumflex.ss01" u2="&#x221e;" k="19" />
    <hkern g1="Ecircumflex.ss01" u2="&#x192;" k="19" />
    <hkern g1="Ecircumflex.ss01" u2="&#xb6;" k="19" />
    <hkern g1="Ecircumflex.ss01" u2="&#xa4;" k="28" />
    <hkern g1="Ecircumflex.ss01" u2="&#xa2;" k="19" />
    <hkern g1="Ecircumflex.ss01" u2="&#x3a;" k="19" />
    <hkern g1="Ecircumflex.ss01" u2="&#x24;" k="19" />
    <hkern g1="Ecircumflex.ss01" u2="&#x23;" k="19" />
    <hkern g1="Edieresis.ss01" u2="&#x221e;" k="19" />
    <hkern g1="Edieresis.ss01" u2="&#x192;" k="19" />
    <hkern g1="Edieresis.ss01" u2="&#xb6;" k="19" />
    <hkern g1="Edieresis.ss01" u2="&#xa4;" k="28" />
    <hkern g1="Edieresis.ss01" u2="&#xa2;" k="19" />
    <hkern g1="Edieresis.ss01" u2="&#x3a;" k="19" />
    <hkern g1="Edieresis.ss01" u2="&#x24;" k="19" />
    <hkern g1="Edieresis.ss01" u2="&#x23;" k="19" />
    <hkern g1="Edotaccent.ss01" u2="&#x221e;" k="19" />
    <hkern g1="Edotaccent.ss01" u2="&#x192;" k="19" />
    <hkern g1="Edotaccent.ss01" u2="&#xb6;" k="19" />
    <hkern g1="Edotaccent.ss01" u2="&#xa4;" k="28" />
    <hkern g1="Edotaccent.ss01" u2="&#xa2;" k="19" />
    <hkern g1="Edotaccent.ss01" u2="&#x3a;" k="19" />
    <hkern g1="Edotaccent.ss01" u2="&#x24;" k="19" />
    <hkern g1="Edotaccent.ss01" u2="&#x23;" k="19" />
    <hkern g1="Egrave.ss01" u2="&#x221e;" k="19" />
    <hkern g1="Egrave.ss01" u2="&#x192;" k="19" />
    <hkern g1="Egrave.ss01" u2="&#xb6;" k="19" />
    <hkern g1="Egrave.ss01" u2="&#xa4;" k="28" />
    <hkern g1="Egrave.ss01" u2="&#xa2;" k="19" />
    <hkern g1="Egrave.ss01" u2="&#x3a;" k="19" />
    <hkern g1="Egrave.ss01" u2="&#x24;" k="19" />
    <hkern g1="Egrave.ss01" u2="&#x23;" k="19" />
    <hkern g1="Emacron.ss01" u2="&#x221e;" k="19" />
    <hkern g1="Emacron.ss01" u2="&#x192;" k="19" />
    <hkern g1="Emacron.ss01" u2="&#xb6;" k="19" />
    <hkern g1="Emacron.ss01" u2="&#xa4;" k="28" />
    <hkern g1="Emacron.ss01" u2="&#xa2;" k="19" />
    <hkern g1="Emacron.ss01" u2="&#x3a;" k="19" />
    <hkern g1="Emacron.ss01" u2="&#x24;" k="19" />
    <hkern g1="Emacron.ss01" u2="&#x23;" k="19" />
    <hkern g1="Eogonek.ss01" u2="&#x221e;" k="19" />
    <hkern g1="Eogonek.ss01" u2="&#x192;" k="19" />
    <hkern g1="Eogonek.ss01" u2="&#xb6;" k="19" />
    <hkern g1="Eogonek.ss01" u2="&#xa4;" k="28" />
    <hkern g1="Eogonek.ss01" u2="&#xa2;" k="19" />
    <hkern g1="Eogonek.ss01" u2="&#x3a;" k="19" />
    <hkern g1="Eogonek.ss01" u2="&#x24;" k="19" />
    <hkern g1="Eogonek.ss01" u2="&#x23;" k="19" />
    <hkern g1="F.ss01" u2="&#x221e;" k="47" />
    <hkern g1="F.ss01" u2="&#x3c0;" k="56" />
    <hkern g1="F.ss01" u2="&#x192;" k="74" />
    <hkern g1="F.ss01" u2="&#xbf;" k="186" />
    <hkern g1="F.ss01" u2="&#xb6;" k="28" />
    <hkern g1="F.ss01" u2="&#xa4;" k="56" />
    <hkern g1="F.ss01" u2="&#xa3;" k="74" />
    <hkern g1="F.ss01" u2="&#xa2;" k="47" />
    <hkern g1="F.ss01" u2="&#x39;" k="28" />
    <hkern g1="F.ss01" u2="&#x37;" k="19" />
    <hkern g1="F.ss01" u2="&#x34;" k="93" />
    <hkern g1="F.ss01" u2="&#x33;" k="28" />
    <hkern g1="F.ss01" u2="&#x32;" k="28" />
    <hkern g1="F.ss01" u2="&#x2f;" k="112" />
    <hkern g1="F.ss01" u2="&#x24;" k="47" />
    <hkern g1="F.ss01" u2="&#x23;" k="47" />
    <hkern g1="J.ss01" u2="&#x2f;" k="19" />
    <hkern g1="K.ss01" u2="&#x221e;" k="47" />
    <hkern g1="K.ss01" u2="&#x192;" k="37" />
    <hkern g1="K.ss01" u2="&#xb6;" k="9" />
    <hkern g1="K.ss01" u2="&#xa4;" k="65" />
    <hkern g1="K.ss01" u2="&#xa3;" k="19" />
    <hkern g1="K.ss01" u2="&#xa2;" k="47" />
    <hkern g1="K.ss01" u2="&#x3a;" k="28" />
    <hkern g1="K.ss01" u2="&#x39;" k="19" />
    <hkern g1="K.ss01" u2="&#x34;" k="37" />
    <hkern g1="K.ss01" u2="&#x33;" k="19" />
    <hkern g1="K.ss01" u2="&#x32;" k="9" />
    <hkern g1="K.ss01" u2="&#x24;" k="56" />
    <hkern g1="K.ss01" u2="&#x23;" k="56" />
    <hkern g1="uni0136.ss01" u2="&#x221e;" k="47" />
    <hkern g1="uni0136.ss01" u2="&#x192;" k="37" />
    <hkern g1="uni0136.ss01" u2="&#xb6;" k="9" />
    <hkern g1="uni0136.ss01" u2="&#xa4;" k="65" />
    <hkern g1="uni0136.ss01" u2="&#xa3;" k="19" />
    <hkern g1="uni0136.ss01" u2="&#xa2;" k="47" />
    <hkern g1="uni0136.ss01" u2="&#x3a;" k="28" />
    <hkern g1="uni0136.ss01" u2="&#x39;" k="19" />
    <hkern g1="uni0136.ss01" u2="&#x34;" k="37" />
    <hkern g1="uni0136.ss01" u2="&#x33;" k="19" />
    <hkern g1="uni0136.ss01" u2="&#x32;" k="9" />
    <hkern g1="uni0136.ss01" u2="&#x24;" k="56" />
    <hkern g1="uni0136.ss01" u2="&#x23;" k="56" />
    <hkern g1="P.ss01" u2="&#xbf;" k="74" />
    <hkern g1="P.ss01" u2="&#x2f;" k="74" />
    <hkern g1="R.ss01" u2="&#x3c0;" k="-19" />
    <hkern g1="R.ss01" u2="\" k="19" />
    <hkern g1="Racute.ss01" u2="&#x3c0;" k="-19" />
    <hkern g1="Racute.ss01" u2="\" k="19" />
    <hkern g1="Rcaron.ss01" u2="&#x3c0;" k="-19" />
    <hkern g1="Rcaron.ss01" u2="\" k="19" />
    <hkern g1="uni0156.ss01" u2="&#x3c0;" k="-19" />
    <hkern g1="uni0156.ss01" u2="\" k="19" />
    <hkern g1="a.ss01" u2="&#x221e;" k="37" />
    <hkern g1="a.ss01" u2="&#x192;" k="37" />
    <hkern g1="a.ss01" u2="&#xb6;" k="65" />
    <hkern g1="a.ss01" u2="&#xa4;" k="56" />
    <hkern g1="a.ss01" u2="&#xa3;" k="19" />
    <hkern g1="a.ss01" u2="&#xa2;" k="47" />
    <hkern g1="a.ss01" u2="\" k="112" />
    <hkern g1="a.ss01" u2="&#x3f;" k="84" />
    <hkern g1="a.ss01" u2="&#x3a;" k="28" />
    <hkern g1="a.ss01" u2="&#x37;" k="112" />
    <hkern g1="a.ss01" u2="&#x34;" k="19" />
    <hkern g1="a.ss01" u2="&#x24;" k="74" />
    <hkern g1="a.ss01" u2="&#x23;" k="37" />
    <hkern g1="aacute.ss01" u2="&#x221e;" k="37" />
    <hkern g1="aacute.ss01" u2="&#x192;" k="37" />
    <hkern g1="aacute.ss01" u2="&#xb6;" k="65" />
    <hkern g1="aacute.ss01" u2="&#xa4;" k="56" />
    <hkern g1="aacute.ss01" u2="&#xa3;" k="19" />
    <hkern g1="aacute.ss01" u2="&#xa2;" k="47" />
    <hkern g1="aacute.ss01" u2="\" k="112" />
    <hkern g1="aacute.ss01" u2="&#x3f;" k="84" />
    <hkern g1="aacute.ss01" u2="&#x3a;" k="28" />
    <hkern g1="aacute.ss01" u2="&#x37;" k="112" />
    <hkern g1="aacute.ss01" u2="&#x34;" k="19" />
    <hkern g1="aacute.ss01" u2="&#x24;" k="74" />
    <hkern g1="aacute.ss01" u2="&#x23;" k="37" />
    <hkern g1="abreve.ss01" u2="&#x221e;" k="37" />
    <hkern g1="abreve.ss01" u2="&#x192;" k="37" />
    <hkern g1="abreve.ss01" u2="&#xb6;" k="65" />
    <hkern g1="abreve.ss01" u2="&#xa4;" k="56" />
    <hkern g1="abreve.ss01" u2="&#xa3;" k="19" />
    <hkern g1="abreve.ss01" u2="&#xa2;" k="47" />
    <hkern g1="abreve.ss01" u2="\" k="112" />
    <hkern g1="abreve.ss01" u2="&#x3f;" k="84" />
    <hkern g1="abreve.ss01" u2="&#x3a;" k="28" />
    <hkern g1="abreve.ss01" u2="&#x37;" k="112" />
    <hkern g1="abreve.ss01" u2="&#x34;" k="19" />
    <hkern g1="abreve.ss01" u2="&#x24;" k="74" />
    <hkern g1="abreve.ss01" u2="&#x23;" k="37" />
    <hkern g1="acircumflex.ss01" u2="&#x221e;" k="37" />
    <hkern g1="acircumflex.ss01" u2="&#x192;" k="37" />
    <hkern g1="acircumflex.ss01" u2="&#xb6;" k="65" />
    <hkern g1="acircumflex.ss01" u2="&#xa4;" k="56" />
    <hkern g1="acircumflex.ss01" u2="&#xa3;" k="19" />
    <hkern g1="acircumflex.ss01" u2="&#xa2;" k="47" />
    <hkern g1="acircumflex.ss01" u2="\" k="112" />
    <hkern g1="acircumflex.ss01" u2="&#x3f;" k="84" />
    <hkern g1="acircumflex.ss01" u2="&#x3a;" k="28" />
    <hkern g1="acircumflex.ss01" u2="&#x37;" k="112" />
    <hkern g1="acircumflex.ss01" u2="&#x34;" k="19" />
    <hkern g1="acircumflex.ss01" u2="&#x24;" k="74" />
    <hkern g1="acircumflex.ss01" u2="&#x23;" k="37" />
    <hkern g1="adieresis.ss01" u2="&#x221e;" k="37" />
    <hkern g1="adieresis.ss01" u2="&#x192;" k="37" />
    <hkern g1="adieresis.ss01" u2="&#xb6;" k="65" />
    <hkern g1="adieresis.ss01" u2="&#xa4;" k="56" />
    <hkern g1="adieresis.ss01" u2="&#xa3;" k="19" />
    <hkern g1="adieresis.ss01" u2="&#xa2;" k="47" />
    <hkern g1="adieresis.ss01" u2="\" k="112" />
    <hkern g1="adieresis.ss01" u2="&#x3f;" k="84" />
    <hkern g1="adieresis.ss01" u2="&#x3a;" k="28" />
    <hkern g1="adieresis.ss01" u2="&#x37;" k="112" />
    <hkern g1="adieresis.ss01" u2="&#x34;" k="19" />
    <hkern g1="adieresis.ss01" u2="&#x24;" k="74" />
    <hkern g1="adieresis.ss01" u2="&#x23;" k="37" />
    <hkern g1="agrave.ss01" u2="&#x221e;" k="37" />
    <hkern g1="agrave.ss01" u2="&#x192;" k="37" />
    <hkern g1="agrave.ss01" u2="&#xb6;" k="65" />
    <hkern g1="agrave.ss01" u2="&#xa4;" k="56" />
    <hkern g1="agrave.ss01" u2="&#xa3;" k="19" />
    <hkern g1="agrave.ss01" u2="&#xa2;" k="47" />
    <hkern g1="agrave.ss01" u2="\" k="112" />
    <hkern g1="agrave.ss01" u2="&#x3f;" k="84" />
    <hkern g1="agrave.ss01" u2="&#x3a;" k="28" />
    <hkern g1="agrave.ss01" u2="&#x37;" k="112" />
    <hkern g1="agrave.ss01" u2="&#x34;" k="19" />
    <hkern g1="agrave.ss01" u2="&#x24;" k="74" />
    <hkern g1="agrave.ss01" u2="&#x23;" k="37" />
    <hkern g1="amacron.ss01" u2="&#x221e;" k="37" />
    <hkern g1="amacron.ss01" u2="&#x192;" k="37" />
    <hkern g1="amacron.ss01" u2="&#xb6;" k="65" />
    <hkern g1="amacron.ss01" u2="&#xa4;" k="56" />
    <hkern g1="amacron.ss01" u2="&#xa3;" k="19" />
    <hkern g1="amacron.ss01" u2="&#xa2;" k="47" />
    <hkern g1="amacron.ss01" u2="\" k="112" />
    <hkern g1="amacron.ss01" u2="&#x3f;" k="84" />
    <hkern g1="amacron.ss01" u2="&#x3a;" k="28" />
    <hkern g1="amacron.ss01" u2="&#x37;" k="112" />
    <hkern g1="amacron.ss01" u2="&#x34;" k="19" />
    <hkern g1="amacron.ss01" u2="&#x24;" k="74" />
    <hkern g1="amacron.ss01" u2="&#x23;" k="37" />
    <hkern g1="aogonek.ss01" u2="&#x221e;" k="37" />
    <hkern g1="aogonek.ss01" u2="&#x192;" k="37" />
    <hkern g1="aogonek.ss01" u2="&#xb6;" k="65" />
    <hkern g1="aogonek.ss01" u2="&#xa4;" k="56" />
    <hkern g1="aogonek.ss01" u2="&#xa3;" k="19" />
    <hkern g1="aogonek.ss01" u2="&#xa2;" k="47" />
    <hkern g1="aogonek.ss01" u2="\" k="112" />
    <hkern g1="aogonek.ss01" u2="&#x3f;" k="84" />
    <hkern g1="aogonek.ss01" u2="&#x3a;" k="28" />
    <hkern g1="aogonek.ss01" u2="&#x37;" k="112" />
    <hkern g1="aogonek.ss01" u2="&#x34;" k="19" />
    <hkern g1="aogonek.ss01" u2="&#x24;" k="74" />
    <hkern g1="aogonek.ss01" u2="&#x23;" k="37" />
    <hkern g1="aring.ss01" u2="&#x221e;" k="37" />
    <hkern g1="aring.ss01" u2="&#x192;" k="37" />
    <hkern g1="aring.ss01" u2="&#xb6;" k="65" />
    <hkern g1="aring.ss01" u2="&#xa4;" k="56" />
    <hkern g1="aring.ss01" u2="&#xa3;" k="19" />
    <hkern g1="aring.ss01" u2="&#xa2;" k="47" />
    <hkern g1="aring.ss01" u2="\" k="112" />
    <hkern g1="aring.ss01" u2="&#x3f;" k="84" />
    <hkern g1="aring.ss01" u2="&#x3a;" k="28" />
    <hkern g1="aring.ss01" u2="&#x37;" k="112" />
    <hkern g1="aring.ss01" u2="&#x34;" k="19" />
    <hkern g1="aring.ss01" u2="&#x24;" k="74" />
    <hkern g1="aring.ss01" u2="&#x23;" k="37" />
    <hkern g1="atilde.ss01" u2="&#x221e;" k="37" />
    <hkern g1="atilde.ss01" u2="&#x192;" k="37" />
    <hkern g1="atilde.ss01" u2="&#xb6;" k="65" />
    <hkern g1="atilde.ss01" u2="&#xa4;" k="56" />
    <hkern g1="atilde.ss01" u2="&#xa3;" k="19" />
    <hkern g1="atilde.ss01" u2="&#xa2;" k="47" />
    <hkern g1="atilde.ss01" u2="\" k="112" />
    <hkern g1="atilde.ss01" u2="&#x3f;" k="84" />
    <hkern g1="atilde.ss01" u2="&#x3a;" k="28" />
    <hkern g1="atilde.ss01" u2="&#x37;" k="112" />
    <hkern g1="atilde.ss01" u2="&#x34;" k="19" />
    <hkern g1="atilde.ss01" u2="&#x24;" k="74" />
    <hkern g1="atilde.ss01" u2="&#x23;" k="37" />
    <hkern g1="ae.ss01" u2="&#x221e;" k="19" />
    <hkern g1="ae.ss01" u2="&#x192;" k="19" />
    <hkern g1="ae.ss01" u2="&#xb6;" k="19" />
    <hkern g1="ae.ss01" u2="&#xa4;" k="28" />
    <hkern g1="ae.ss01" u2="&#xa2;" k="19" />
    <hkern g1="ae.ss01" u2="&#x3a;" k="19" />
    <hkern g1="ae.ss01" u2="&#x24;" k="19" />
    <hkern g1="ae.ss01" u2="&#x23;" k="19" />
    <hkern g1="b.ss01" u2="\" k="19" />
    <hkern g1="b.ss01" u2="&#x2f;" k="19" />
    <hkern g1="d.ss01" u2="\" k="19" />
    <hkern g1="d.ss01" u2="&#x2f;" k="19" />
    <hkern g1="eth.ss01" u2="\" k="19" />
    <hkern g1="eth.ss01" u2="&#x2f;" k="19" />
    <hkern g1="dcaron.ss01" u2="\" k="19" />
    <hkern g1="dcaron.ss01" u2="&#x2f;" k="19" />
    <hkern g1="dcroat.ss01" u2="\" k="19" />
    <hkern g1="dcroat.ss01" u2="&#x2f;" k="19" />
    <hkern g1="e.ss01" u2="&#x221e;" k="19" />
    <hkern g1="e.ss01" u2="&#x192;" k="19" />
    <hkern g1="e.ss01" u2="&#xb6;" k="19" />
    <hkern g1="e.ss01" u2="&#xa4;" k="28" />
    <hkern g1="e.ss01" u2="&#xa2;" k="19" />
    <hkern g1="e.ss01" u2="&#x3a;" k="19" />
    <hkern g1="e.ss01" u2="&#x24;" k="19" />
    <hkern g1="e.ss01" u2="&#x23;" k="19" />
    <hkern g1="eacute.ss01" u2="&#x221e;" k="19" />
    <hkern g1="eacute.ss01" u2="&#x192;" k="19" />
    <hkern g1="eacute.ss01" u2="&#xb6;" k="19" />
    <hkern g1="eacute.ss01" u2="&#xa4;" k="28" />
    <hkern g1="eacute.ss01" u2="&#xa2;" k="19" />
    <hkern g1="eacute.ss01" u2="&#x3a;" k="19" />
    <hkern g1="eacute.ss01" u2="&#x24;" k="19" />
    <hkern g1="eacute.ss01" u2="&#x23;" k="19" />
    <hkern g1="ecaron.ss01" u2="&#x221e;" k="19" />
    <hkern g1="ecaron.ss01" u2="&#x192;" k="19" />
    <hkern g1="ecaron.ss01" u2="&#xb6;" k="19" />
    <hkern g1="ecaron.ss01" u2="&#xa4;" k="28" />
    <hkern g1="ecaron.ss01" u2="&#xa2;" k="19" />
    <hkern g1="ecaron.ss01" u2="&#x3a;" k="19" />
    <hkern g1="ecaron.ss01" u2="&#x24;" k="19" />
    <hkern g1="ecaron.ss01" u2="&#x23;" k="19" />
    <hkern g1="ecircumflex.ss01" u2="&#x221e;" k="19" />
    <hkern g1="ecircumflex.ss01" u2="&#x192;" k="19" />
    <hkern g1="ecircumflex.ss01" u2="&#xb6;" k="19" />
    <hkern g1="ecircumflex.ss01" u2="&#xa4;" k="28" />
    <hkern g1="ecircumflex.ss01" u2="&#xa2;" k="19" />
    <hkern g1="ecircumflex.ss01" u2="&#x3a;" k="19" />
    <hkern g1="ecircumflex.ss01" u2="&#x24;" k="19" />
    <hkern g1="ecircumflex.ss01" u2="&#x23;" k="19" />
    <hkern g1="edieresis.ss01" u2="&#x221e;" k="19" />
    <hkern g1="edieresis.ss01" u2="&#x192;" k="19" />
    <hkern g1="edieresis.ss01" u2="&#xb6;" k="19" />
    <hkern g1="edieresis.ss01" u2="&#xa4;" k="28" />
    <hkern g1="edieresis.ss01" u2="&#xa2;" k="19" />
    <hkern g1="edieresis.ss01" u2="&#x3a;" k="19" />
    <hkern g1="edieresis.ss01" u2="&#x24;" k="19" />
    <hkern g1="edieresis.ss01" u2="&#x23;" k="19" />
    <hkern g1="edotaccent.ss01" u2="&#x221e;" k="19" />
    <hkern g1="edotaccent.ss01" u2="&#x192;" k="19" />
    <hkern g1="edotaccent.ss01" u2="&#xb6;" k="19" />
    <hkern g1="edotaccent.ss01" u2="&#xa4;" k="28" />
    <hkern g1="edotaccent.ss01" u2="&#xa2;" k="19" />
    <hkern g1="edotaccent.ss01" u2="&#x3a;" k="19" />
    <hkern g1="edotaccent.ss01" u2="&#x24;" k="19" />
    <hkern g1="edotaccent.ss01" u2="&#x23;" k="19" />
    <hkern g1="egrave.ss01" u2="&#x221e;" k="19" />
    <hkern g1="egrave.ss01" u2="&#x192;" k="19" />
    <hkern g1="egrave.ss01" u2="&#xb6;" k="19" />
    <hkern g1="egrave.ss01" u2="&#xa4;" k="28" />
    <hkern g1="egrave.ss01" u2="&#xa2;" k="19" />
    <hkern g1="egrave.ss01" u2="&#x3a;" k="19" />
    <hkern g1="egrave.ss01" u2="&#x24;" k="19" />
    <hkern g1="egrave.ss01" u2="&#x23;" k="19" />
    <hkern g1="emacron.ss01" u2="&#x221e;" k="19" />
    <hkern g1="emacron.ss01" u2="&#x192;" k="19" />
    <hkern g1="emacron.ss01" u2="&#xb6;" k="19" />
    <hkern g1="emacron.ss01" u2="&#xa4;" k="28" />
    <hkern g1="emacron.ss01" u2="&#xa2;" k="19" />
    <hkern g1="emacron.ss01" u2="&#x3a;" k="19" />
    <hkern g1="emacron.ss01" u2="&#x24;" k="19" />
    <hkern g1="emacron.ss01" u2="&#x23;" k="19" />
    <hkern g1="eogonek.ss01" u2="&#x221e;" k="19" />
    <hkern g1="eogonek.ss01" u2="&#x192;" k="19" />
    <hkern g1="eogonek.ss01" u2="&#xb6;" k="19" />
    <hkern g1="eogonek.ss01" u2="&#xa4;" k="28" />
    <hkern g1="eogonek.ss01" u2="&#xa2;" k="19" />
    <hkern g1="eogonek.ss01" u2="&#x3a;" k="19" />
    <hkern g1="eogonek.ss01" u2="&#x24;" k="19" />
    <hkern g1="eogonek.ss01" u2="&#x23;" k="19" />
    <hkern g1="f.ss01" u2="&#x221e;" k="47" />
    <hkern g1="f.ss01" u2="&#x3c0;" k="56" />
    <hkern g1="f.ss01" u2="&#x192;" k="74" />
    <hkern g1="f.ss01" u2="&#xbf;" k="186" />
    <hkern g1="f.ss01" u2="&#xb6;" k="28" />
    <hkern g1="f.ss01" u2="&#xa4;" k="56" />
    <hkern g1="f.ss01" u2="&#xa3;" k="74" />
    <hkern g1="f.ss01" u2="&#xa2;" k="47" />
    <hkern g1="f.ss01" u2="&#x39;" k="28" />
    <hkern g1="f.ss01" u2="&#x37;" k="19" />
    <hkern g1="f.ss01" u2="&#x34;" k="93" />
    <hkern g1="f.ss01" u2="&#x33;" k="28" />
    <hkern g1="f.ss01" u2="&#x32;" k="28" />
    <hkern g1="f.ss01" u2="&#x2f;" k="112" />
    <hkern g1="f.ss01" u2="&#x24;" k="47" />
    <hkern g1="f.ss01" u2="&#x23;" k="47" />
    <hkern g1="j.ss01" u2="&#x2f;" k="19" />
    <hkern g1="k.ss01" u2="&#x221e;" k="47" />
    <hkern g1="k.ss01" u2="&#x192;" k="37" />
    <hkern g1="k.ss01" u2="&#xb6;" k="9" />
    <hkern g1="k.ss01" u2="&#xa4;" k="65" />
    <hkern g1="k.ss01" u2="&#xa3;" k="19" />
    <hkern g1="k.ss01" u2="&#xa2;" k="47" />
    <hkern g1="k.ss01" u2="&#x3a;" k="28" />
    <hkern g1="k.ss01" u2="&#x39;" k="19" />
    <hkern g1="k.ss01" u2="&#x34;" k="37" />
    <hkern g1="k.ss01" u2="&#x33;" k="19" />
    <hkern g1="k.ss01" u2="&#x32;" k="9" />
    <hkern g1="k.ss01" u2="&#x24;" k="56" />
    <hkern g1="k.ss01" u2="&#x23;" k="56" />
    <hkern g1="uni0137.ss01" u2="&#x221e;" k="47" />
    <hkern g1="uni0137.ss01" u2="&#x192;" k="37" />
    <hkern g1="uni0137.ss01" u2="&#xb6;" k="9" />
    <hkern g1="uni0137.ss01" u2="&#xa4;" k="65" />
    <hkern g1="uni0137.ss01" u2="&#xa3;" k="19" />
    <hkern g1="uni0137.ss01" u2="&#xa2;" k="47" />
    <hkern g1="uni0137.ss01" u2="&#x3a;" k="28" />
    <hkern g1="uni0137.ss01" u2="&#x39;" k="19" />
    <hkern g1="uni0137.ss01" u2="&#x34;" k="37" />
    <hkern g1="uni0137.ss01" u2="&#x33;" k="19" />
    <hkern g1="uni0137.ss01" u2="&#x32;" k="9" />
    <hkern g1="uni0137.ss01" u2="&#x24;" k="56" />
    <hkern g1="uni0137.ss01" u2="&#x23;" k="56" />
    <hkern g1="p.ss01" u2="&#xbf;" k="74" />
    <hkern g1="p.ss01" u2="&#x2f;" k="74" />
    <hkern g1="r.ss01" u2="&#x3c0;" k="-19" />
    <hkern g1="r.ss01" u2="\" k="19" />
    <hkern g1="racute.ss01" u2="&#x3c0;" k="-19" />
    <hkern g1="racute.ss01" u2="\" k="19" />
    <hkern g1="rcaron.ss01" u2="&#x3c0;" k="-19" />
    <hkern g1="rcaron.ss01" u2="\" k="19" />
    <hkern g1="uni0157.ss01" u2="&#x3c0;" k="-19" />
    <hkern g1="uni0157.ss01" u2="\" k="19" />
    <hkern g1="pi.ss01" u2="&#x221e;" k="19" />
    <hkern g1="pi.ss01" u2="&#xb6;" k="28" />
    <hkern g1="pi.ss01" u2="&#xa4;" k="28" />
    <hkern g1="pi.ss01" u2="&#xa2;" k="19" />
    <hkern g1="pi.ss01" u2="\" k="93" />
    <hkern g1="pi.ss01" u2="&#x3f;" k="56" />
    <hkern g1="pi.ss01" u2="&#x37;" k="74" />
    <hkern g1="pi.ss01" u2="&#x31;" k="37" />
    <hkern g1="pi.ss01" u2="&#x24;" k="37" />
    <hkern g1="pi.ss01" u2="&#x23;" k="19" />
    <hkern g1="zero.dnom" u2="&#x3f;" k="37" />
    <hkern g1="zero.dnom" u2="&#x37;" k="93" />
    <hkern g1="zero.dnom" u2="&#x31;" k="37" />
    <hkern g1="one.dnom" u2="&#x3f;" k="37" />
    <hkern g1="one.dnom" u2="&#x37;" k="93" />
    <hkern g1="one.dnom" u2="&#x31;" k="37" />
    <hkern g1="two.dnom" u2="&#x3f;" k="37" />
    <hkern g1="two.dnom" u2="&#x37;" k="93" />
    <hkern g1="two.dnom" u2="&#x31;" k="37" />
    <hkern g1="three.dnom" u2="&#x3f;" k="37" />
    <hkern g1="three.dnom" u2="&#x37;" k="93" />
    <hkern g1="three.dnom" u2="&#x31;" k="37" />
    <hkern g1="four.dnom" u2="&#x3f;" k="37" />
    <hkern g1="four.dnom" u2="&#x37;" k="93" />
    <hkern g1="four.dnom" u2="&#x31;" k="37" />
    <hkern g1="five.dnom" u2="&#x3f;" k="37" />
    <hkern g1="five.dnom" u2="&#x37;" k="93" />
    <hkern g1="five.dnom" u2="&#x31;" k="37" />
    <hkern g1="six.dnom" u2="&#x3f;" k="37" />
    <hkern g1="six.dnom" u2="&#x37;" k="93" />
    <hkern g1="six.dnom" u2="&#x31;" k="37" />
    <hkern g1="seven.dnom" u2="&#x3f;" k="37" />
    <hkern g1="seven.dnom" u2="&#x37;" k="93" />
    <hkern g1="seven.dnom" u2="&#x31;" k="37" />
    <hkern g1="eight.dnom" u2="&#x3f;" k="37" />
    <hkern g1="eight.dnom" u2="&#x37;" k="93" />
    <hkern g1="eight.dnom" u2="&#x31;" k="37" />
    <hkern g1="nine.dnom" u2="&#x3f;" k="37" />
    <hkern g1="nine.dnom" u2="&#x37;" k="93" />
    <hkern g1="nine.dnom" u2="&#x31;" k="37" />
    <hkern g1="zero.numr" u2="&#xbf;" k="74" />
    <hkern g1="zero.numr" u2="&#xa3;" k="19" />
    <hkern g1="zero.numr" u2="&#x2f;" k="74" />
    <hkern g1="one.numr" u2="&#xbf;" k="74" />
    <hkern g1="one.numr" u2="&#xa3;" k="19" />
    <hkern g1="one.numr" u2="&#x2f;" k="74" />
    <hkern g1="two.numr" u2="&#xbf;" k="74" />
    <hkern g1="two.numr" u2="&#xa3;" k="19" />
    <hkern g1="two.numr" u2="&#x2f;" k="74" />
    <hkern g1="three.numr" u2="&#xbf;" k="74" />
    <hkern g1="three.numr" u2="&#xa3;" k="19" />
    <hkern g1="three.numr" u2="&#x2f;" k="74" />
    <hkern g1="four.numr" u2="&#xbf;" k="74" />
    <hkern g1="four.numr" u2="&#xa3;" k="19" />
    <hkern g1="four.numr" u2="&#x2f;" k="74" />
    <hkern g1="five.numr" u2="&#xbf;" k="74" />
    <hkern g1="five.numr" u2="&#xa3;" k="19" />
    <hkern g1="five.numr" u2="&#x2f;" k="74" />
    <hkern g1="six.numr" u2="&#xbf;" k="74" />
    <hkern g1="six.numr" u2="&#xa3;" k="19" />
    <hkern g1="six.numr" u2="&#x2f;" k="74" />
    <hkern g1="seven.numr" u2="&#xbf;" k="74" />
    <hkern g1="seven.numr" u2="&#xa3;" k="19" />
    <hkern g1="seven.numr" u2="&#x2f;" k="74" />
    <hkern g1="eight.numr" u2="&#xbf;" k="74" />
    <hkern g1="eight.numr" u2="&#xa3;" k="19" />
    <hkern g1="eight.numr" u2="&#x2f;" k="74" />
    <hkern g1="nine.numr" u2="&#xbf;" k="74" />
    <hkern g1="nine.numr" u2="&#xa3;" k="19" />
    <hkern g1="nine.numr" u2="&#x2f;" k="74" />
    <hkern g1="caron.alt" u2="&#x192;" k="56" />
    <hkern g1="caron.alt" u2="&#xbf;" k="112" />
    <hkern g1="caron.alt" u2="&#xa3;" k="37" />
    <hkern g1="caron.alt" u2="&#x34;" k="74" />
    <hkern g1="caron.alt" u2="&#x2f;" k="74" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="quotedbl,quotesingle,asterisk,asciicircum,grave,dieresis,registered,overscore,degree,acute,circumflex,caron,uni02C9,breve,dotaccent,ring,tilde,hungarumlaut,quoteleft,quoteright,quotedblleft,quotedblright,dagger,daggerdbl,trademark,caron.alt"
	k="74" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="percent,onequarter,onehalf,threequarters,perthousand"
	k="74" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="ampersand,zero,six,eight,at,C,G,O,Q,c,g,o,q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Gbreve,gbreve,uni0122,uni0123,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe,Ohorn,ohorn,uni1ECC,uni1ECD,uni1ECE,uni1ECF,uni1ED0,uni1ED1,uni1ED2,uni1ED3,uni1ED4,uni1ED5,uni1ED6,uni1ED7,uni1ED8,uni1ED9,uni1EDA,uni1EDB,uni1EDC,uni1EDD,uni1EDE,uni1EDF,uni1EE0,uni1EE1,uni1EE2,uni1EE3,Euro"
	k="28" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="28" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="56" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="five,U.ss01,Uacute.ss01,Ucircumflex.ss01,Udieresis.ss01,Ugrave.ss01,Uhungarumlaut.ss01,Umacron.ss01,Uogonek.ss01,Uring.ss01,u.ss01,uacute.ss01,ucircumflex.ss01,udieresis.ss01,ugrave.ss01,uhungarumlaut.ss01,umacron.ss01,uogonek.ss01,uring.ss01"
	k="28" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="28" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="28" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="J,j,J.ss01,j.ss01"
	k="9" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	k="37" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="130" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Utilde,utilde,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek,Uhorn,uhorn,uni1EE4,uni1EE5,uni1EE6,uni1EE7,uni1EE8,uni1EE9,uni1EEA,uni1EEB,uni1EEC,uni1EED,uni1EEE,uni1EEF,uni1EF0,uni1EF1"
	k="28" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="V,W,v,w"
	k="130" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="X,x"
	k="37" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="121" />
    <hkern g1="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	g2="ordfeminine,uni00B2,uni00B3,uni00B9,ordmasculine,uni2070,uni2074,uni2075,uni2076,uni2077,uni2078,uni2079,zero.numr,one.numr,two.numr,three.numr,four.numr,five.numr,six.numr,seven.numr,eight.numr,nine.numr"
	k="74" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,AE.ss01,E.ss01,Eacute.ss01,Ecaron.ss01,Ecircumflex.ss01,Edieresis.ss01,Edotaccent.ss01,Egrave.ss01,Emacron.ss01,Eogonek.ss01,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01"
	g2="ampersand,zero,six,eight,at,C,G,O,Q,c,g,o,q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Gbreve,gbreve,uni0122,uni0123,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe,Ohorn,ohorn,uni1ECC,uni1ECD,uni1ECE,uni1ECF,uni1ED0,uni1ED1,uni1ED2,uni1ED3,uni1ED4,uni1ED5,uni1ED6,uni1ED7,uni1ED8,uni1ED9,uni1EDA,uni1EDB,uni1EDC,uni1EDD,uni1EDE,uni1EDF,uni1EE0,uni1EE1,uni1EE2,uni1EE3,Euro"
	k="19" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,AE.ss01,E.ss01,Eacute.ss01,Ecaron.ss01,Ecircumflex.ss01,Edieresis.ss01,Edotaccent.ss01,Egrave.ss01,Emacron.ss01,Eogonek.ss01,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="9" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,AE.ss01,E.ss01,Eacute.ss01,Ecaron.ss01,Ecircumflex.ss01,Edieresis.ss01,Edotaccent.ss01,Egrave.ss01,Emacron.ss01,Eogonek.ss01,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="19" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,AE.ss01,E.ss01,Eacute.ss01,Ecaron.ss01,Ecircumflex.ss01,Edieresis.ss01,Edotaccent.ss01,Egrave.ss01,Emacron.ss01,Eogonek.ss01,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="19" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,AE.ss01,E.ss01,Eacute.ss01,Ecaron.ss01,Ecircumflex.ss01,Edieresis.ss01,Edotaccent.ss01,Egrave.ss01,Emacron.ss01,Eogonek.ss01,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="9" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,AE.ss01,E.ss01,Eacute.ss01,Ecaron.ss01,Ecircumflex.ss01,Edieresis.ss01,Edotaccent.ss01,Egrave.ss01,Emacron.ss01,Eogonek.ss01,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01"
	g2="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	k="9" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,AE.ss01,E.ss01,Eacute.ss01,Ecaron.ss01,Ecircumflex.ss01,Edieresis.ss01,Edotaccent.ss01,Egrave.ss01,Emacron.ss01,Eogonek.ss01,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01"
	g2="V,W,v,w"
	k="19" />
    <hkern g1="E,e,AE,Egrave,Eacute,Ecircumflex,Edieresis,ae,egrave,eacute,ecircumflex,edieresis,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,OE,oe,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,AE.ss01,E.ss01,Eacute.ss01,Ecaron.ss01,Ecircumflex.ss01,Edieresis.ss01,Edotaccent.ss01,Egrave.ss01,Emacron.ss01,Eogonek.ss01,ae.ss01,e.ss01,eacute.ss01,ecaron.ss01,ecircumflex.ss01,edieresis.ss01,edotaccent.ss01,egrave.ss01,emacron.ss01,eogonek.ss01"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="19" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="ampersand,zero,six,eight,at,C,G,O,Q,c,g,o,q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Gbreve,gbreve,uni0122,uni0123,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe,Ohorn,ohorn,uni1ECC,uni1ECD,uni1ECE,uni1ECF,uni1ED0,uni1ED1,uni1ED2,uni1ED3,uni1ED4,uni1ED5,uni1ED6,uni1ED7,uni1ED8,uni1ED9,uni1EDA,uni1EDB,uni1EDC,uni1EDD,uni1EDE,uni1EDF,uni1EE0,uni1EE1,uni1EE2,uni1EE3,Euro"
	k="37" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="9" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="19" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="140" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="9" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="J,j,J.ss01,j.ss01"
	k="140" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	k="28" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="V,W,v,w"
	k="19" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="X,x"
	k="19" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="19" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="233" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	k="149" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="semicolon,exclamdown,pi.ss01"
	k="56" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="19" />
    <hkern g1="F,f,F.ss01,f.ss01"
	g2="germandbls,uni1E9E"
	k="28" />
    <hkern g1="G,g,Gbreve,gbreve,uni0122,uni0123"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="19" />
    <hkern g1="G,g,Gbreve,gbreve,uni0122,uni0123"
	g2="V,W,v,w"
	k="19" />
    <hkern g1="G,g,Gbreve,gbreve,uni0122,uni0123"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="19" />
    <hkern g1="J,j,eth,J.ss01,j.ss01"
	g2="five,U.ss01,Uacute.ss01,Ucircumflex.ss01,Udieresis.ss01,Ugrave.ss01,Uhungarumlaut.ss01,Umacron.ss01,Uogonek.ss01,Uring.ss01,u.ss01,uacute.ss01,ucircumflex.ss01,udieresis.ss01,ugrave.ss01,uhungarumlaut.ss01,umacron.ss01,uogonek.ss01,uring.ss01"
	k="-9" />
    <hkern g1="J,j,eth,J.ss01,j.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="37" />
    <hkern g1="J,j,eth,J.ss01,j.ss01"
	g2="X,x"
	k="9" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="ampersand,zero,six,eight,at,C,G,O,Q,c,g,o,q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Gbreve,gbreve,uni0122,uni0123,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe,Ohorn,ohorn,uni1ECC,uni1ECD,uni1ECE,uni1ECF,uni1ED0,uni1ED1,uni1ED2,uni1ED3,uni1ED4,uni1ED5,uni1ED6,uni1ED7,uni1ED8,uni1ED9,uni1EDA,uni1EDB,uni1EDC,uni1EDD,uni1EDE,uni1EDF,uni1EE0,uni1EE1,uni1EE2,uni1EE3,Euro"
	k="37" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="28" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="56" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="five,U.ss01,Uacute.ss01,Ucircumflex.ss01,Udieresis.ss01,Ugrave.ss01,Uhungarumlaut.ss01,Umacron.ss01,Uogonek.ss01,Uring.ss01,u.ss01,uacute.ss01,ucircumflex.ss01,udieresis.ss01,ugrave.ss01,uhungarumlaut.ss01,umacron.ss01,uogonek.ss01,uring.ss01"
	k="9" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="37" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="9" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="J,j,J.ss01,j.ss01"
	k="9" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	k="19" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="V,W,v,w"
	k="19" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="X,x"
	k="19" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="19" />
    <hkern g1="K,X,k,x,uni0136,uni0137,K.ss01,uni0136.ss01,k.ss01,uni0137.ss01"
	g2="germandbls,uni1E9E"
	k="9" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="quotedbl,quotesingle,asterisk,asciicircum,grave,dieresis,registered,overscore,degree,acute,circumflex,caron,uni02C9,breve,dotaccent,ring,tilde,hungarumlaut,quoteleft,quoteright,quotedblleft,quotedblright,dagger,daggerdbl,trademark,caron.alt"
	k="112" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="percent,onequarter,onehalf,threequarters,perthousand"
	k="112" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="37" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="74" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="37" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="37" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="158" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Utilde,utilde,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek,Uhorn,uhorn,uni1EE4,uni1EE5,uni1EE6,uni1EE7,uni1EE8,uni1EE9,uni1EEA,uni1EEB,uni1EEC,uni1EED,uni1EEE,uni1EEF,uni1EF0,uni1EF1"
	k="37" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="V,W,v,w"
	k="158" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="X,x"
	k="37" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="186" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="ordfeminine,uni00B2,uni00B3,uni00B9,ordmasculine,uni2070,uni2074,uni2075,uni2076,uni2077,uni2078,uni2079,zero.numr,one.numr,two.numr,three.numr,four.numr,five.numr,six.numr,seven.numr,eight.numr,nine.numr"
	k="112" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="19" />
    <hkern g1="L,l,Lacute,lacute,uni013B,uni013C,Lcaron,lcaron,Lslash,lslash"
	g2="parenright,bracketright,braceright"
	k="19" />
    <hkern g1="P,p,P.ss01,p.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="65" />
    <hkern g1="P,p,P.ss01,p.ss01"
	g2="J,j,J.ss01,j.ss01"
	k="74" />
    <hkern g1="P,p,P.ss01,p.ss01"
	g2="V,W,v,w"
	k="9" />
    <hkern g1="P,p,P.ss01,p.ss01"
	g2="X,x"
	k="19" />
    <hkern g1="P,p,P.ss01,p.ss01"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="19" />
    <hkern g1="P,p,P.ss01,p.ss01"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="112" />
    <hkern g1="P,p,P.ss01,p.ss01"
	g2="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	k="74" />
    <hkern g1="R,r,Racute,racute,uni0156,uni0157,Rcaron,rcaron,R.ss01,Racute.ss01,Rcaron.ss01,uni0156.ss01,r.ss01,racute.ss01,rcaron.ss01,uni0157.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="28" />
    <hkern g1="R,r,Racute,racute,uni0156,uni0157,Rcaron,rcaron,R.ss01,Racute.ss01,Rcaron.ss01,uni0156.ss01,r.ss01,racute.ss01,rcaron.ss01,uni0157.ss01"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="9" />
    <hkern g1="R,r,Racute,racute,uni0156,uni0157,Rcaron,rcaron,R.ss01,Racute.ss01,Rcaron.ss01,uni0156.ss01,r.ss01,racute.ss01,rcaron.ss01,uni0157.ss01"
	g2="J,j,J.ss01,j.ss01"
	k="19" />
    <hkern g1="R,r,Racute,racute,uni0156,uni0157,Rcaron,rcaron,R.ss01,Racute.ss01,Rcaron.ss01,uni0156.ss01,r.ss01,racute.ss01,rcaron.ss01,uni0157.ss01"
	g2="V,W,v,w"
	k="19" />
    <hkern g1="R,r,Racute,racute,uni0156,uni0157,Rcaron,rcaron,R.ss01,Racute.ss01,Rcaron.ss01,uni0156.ss01,r.ss01,racute.ss01,rcaron.ss01,uni0157.ss01"
	g2="X,x"
	k="19" />
    <hkern g1="R,r,Racute,racute,uni0156,uni0157,Rcaron,rcaron,R.ss01,Racute.ss01,Rcaron.ss01,uni0156.ss01,r.ss01,racute.ss01,rcaron.ss01,uni0157.ss01"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="37" />
    <hkern g1="R,r,Racute,racute,uni0156,uni0157,Rcaron,rcaron,R.ss01,Racute.ss01,Rcaron.ss01,uni0156.ss01,r.ss01,racute.ss01,rcaron.ss01,uni0157.ss01"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="19" />
    <hkern g1="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="37" />
    <hkern g1="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="9" />
    <hkern g1="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Utilde,utilde,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek,Uhorn,uhorn,uni1EE4,uni1EE5,uni1EE6,uni1EE7,uni1EE8,uni1EE9,uni1EEA,uni1EEB,uni1EEC,uni1EED,uni1EEE,uni1EEF,uni1EF0,uni1EF1"
	k="9" />
    <hkern g1="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	g2="V,W,v,w"
	k="9" />
    <hkern g1="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	g2="X,x"
	k="19" />
    <hkern g1="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="19" />
    <hkern g1="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="19" />
    <hkern g1="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="37" />
    <hkern g1="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="74" />
    <hkern g1="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="130" />
    <hkern g1="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="-19" />
    <hkern g1="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="140" />
    <hkern g1="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	g2="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	k="112" />
    <hkern g1="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	g2="semicolon,exclamdown,pi.ss01"
	k="37" />
    <hkern g1="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	g2="uni2080,uni2081,uni2082,uni2083,uni2084,uni2085,uni2086,uni2087,uni2088,uni2089,zero.dnom,one.dnom,two.dnom,three.dnom,four.dnom,five.dnom,six.dnom,seven.dnom,eight.dnom,nine.dnom"
	k="93" />
    <hkern g1="Thorn,thorn,infinity"
	g2="ampersand,zero,six,eight,at,C,G,O,Q,c,g,o,q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Gbreve,gbreve,uni0122,uni0123,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe,Ohorn,ohorn,uni1ECC,uni1ECD,uni1ECE,uni1ECF,uni1ED0,uni1ED1,uni1ED2,uni1ED3,uni1ED4,uni1ED5,uni1ED6,uni1ED7,uni1ED8,uni1ED9,uni1EDA,uni1EDB,uni1EDC,uni1EDD,uni1EDE,uni1EDF,uni1EE0,uni1EE1,uni1EE2,uni1EE3,Euro"
	k="-19" />
    <hkern g1="Thorn,thorn,infinity"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="37" />
    <hkern g1="Thorn,thorn,infinity"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="19" />
    <hkern g1="Thorn,thorn,infinity"
	g2="V,W,v,w"
	k="37" />
    <hkern g1="Thorn,thorn,infinity"
	g2="X,x"
	k="47" />
    <hkern g1="Thorn,thorn,infinity"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="74" />
    <hkern g1="Thorn,thorn,infinity"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="37" />
    <hkern g1="Thorn,thorn,infinity"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="19" />
    <hkern g1="Thorn,thorn,infinity"
	g2="parenright,bracketright,braceright"
	k="19" />
    <hkern g1="V,W,v,w"
	g2="ampersand,zero,six,eight,at,C,G,O,Q,c,g,o,q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Gbreve,gbreve,uni0122,uni0123,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe,Ohorn,ohorn,uni1ECC,uni1ECD,uni1ECE,uni1ECF,uni1ED0,uni1ED1,uni1ED2,uni1ED3,uni1ED4,uni1ED5,uni1ED6,uni1ED7,uni1ED8,uni1ED9,uni1EDA,uni1EDB,uni1EDC,uni1EDD,uni1EDE,uni1EDF,uni1EE0,uni1EE1,uni1EE2,uni1EE3,Euro"
	k="28" />
    <hkern g1="V,W,v,w"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="28" />
    <hkern g1="V,W,v,w"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="56" />
    <hkern g1="V,W,v,w"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="130" />
    <hkern g1="V,W,v,w"
	g2="J,j,J.ss01,j.ss01"
	k="84" />
    <hkern g1="V,W,v,w"
	g2="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	k="9" />
    <hkern g1="V,W,v,w"
	g2="V,W,v,w"
	k="28" />
    <hkern g1="V,W,v,w"
	g2="X,x"
	k="19" />
    <hkern g1="V,W,v,w"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="19" />
    <hkern g1="V,W,v,w"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="158" />
    <hkern g1="V,W,v,w"
	g2="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	k="112" />
    <hkern g1="V,W,v,w"
	g2="semicolon,exclamdown,pi.ss01"
	k="37" />
    <hkern g1="V,W,v,w"
	g2="germandbls,uni1E9E"
	k="9" />
    <hkern g1="V,W,v,w"
	g2="uni2080,uni2081,uni2082,uni2083,uni2084,uni2085,uni2086,uni2087,uni2088,uni2089,zero.dnom,one.dnom,two.dnom,three.dnom,four.dnom,five.dnom,six.dnom,seven.dnom,eight.dnom,nine.dnom"
	k="74" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="ampersand,zero,six,eight,at,C,G,O,Q,c,g,o,q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Gbreve,gbreve,uni0122,uni0123,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe,Ohorn,ohorn,uni1ECC,uni1ECD,uni1ECE,uni1ECF,uni1ED0,uni1ED1,uni1ED2,uni1ED3,uni1ED4,uni1ED5,uni1ED6,uni1ED7,uni1ED8,uni1ED9,uni1EDA,uni1EDB,uni1EDC,uni1EDD,uni1EDE,uni1EDF,uni1EE0,uni1EE1,uni1EE2,uni1EE3,Euro"
	k="37" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="47" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="89" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="121" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="J,j,J.ss01,j.ss01"
	k="102" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="S,s,section,Sacute,sacute,Scedilla,scedilla,Scaron,scaron,uni0218,uni0219"
	k="19" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="V,W,v,w"
	k="19" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="X,x"
	k="19" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="186" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	k="130" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="semicolon,exclamdown,pi.ss01"
	k="65" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="germandbls,uni1E9E"
	k="19" />
    <hkern g1="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	g2="uni2080,uni2081,uni2082,uni2083,uni2084,uni2085,uni2086,uni2087,uni2088,uni2089,zero.dnom,one.dnom,two.dnom,three.dnom,four.dnom,five.dnom,six.dnom,seven.dnom,eight.dnom,nine.dnom"
	k="112" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="28" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="56" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="37" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="J,j,J.ss01,j.ss01"
	k="19" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="X,x"
	k="19" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="56" />
    <hkern g1="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="19" />
    <hkern g1="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	g2="percent,onequarter,onehalf,threequarters,perthousand"
	k="74" />
    <hkern g1="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="112" />
    <hkern g1="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	g2="V,W,v,w"
	k="112" />
    <hkern g1="comma,period,underscore,cedilla,ogonek,uni0326,quotesinglbase,quotedblbase,ellipsis"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="130" />
    <hkern g1="five,six"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="19" />
    <hkern g1="five,six"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="19" />
    <hkern g1="five,six"
	g2="X,x"
	k="9" />
    <hkern g1="five,six"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="19" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="56" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="J,j,J.ss01,j.ss01"
	k="74" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="74" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="V,W,v,w"
	k="56" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="X,x"
	k="56" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="93" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="93" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="56" />
    <hkern g1="hyphen,greater,braceright,asciitilde,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	g2="parenright,bracketright,braceright"
	k="19" />
    <hkern g1="ordfeminine,uni00B2,uni00B3,uni00B9,ordmasculine,uni2070,uni2074,uni2075,uni2076,uni2077,uni2078,uni2079,zero.numr,one.numr,two.numr,three.numr,four.numr,five.numr,six.numr,seven.numr,eight.numr,nine.numr"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="74" />
    <hkern g1="ordfeminine,uni00B2,uni00B3,uni00B9,ordmasculine,uni2070,uni2074,uni2075,uni2076,uni2077,uni2078,uni2079,zero.numr,one.numr,two.numr,three.numr,four.numr,five.numr,six.numr,seven.numr,eight.numr,nine.numr"
	g2="J,j,J.ss01,j.ss01"
	k="74" />
    <hkern g1="ordfeminine,uni00B2,uni00B3,uni00B9,ordmasculine,uni2070,uni2074,uni2075,uni2076,uni2077,uni2078,uni2079,zero.numr,one.numr,two.numr,three.numr,four.numr,five.numr,six.numr,seven.numr,eight.numr,nine.numr"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="112" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="9" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="19" />
    <hkern g1="parenleft,bracketleft,braceleft"
	g2="J,j,J.ss01,j.ss01"
	k="19" />
    <hkern g1="percent,onequarter,onehalf,threequarters"
	g2="quotedbl,quotesingle,asterisk,asciicircum,grave,dieresis,registered,overscore,degree,acute,circumflex,caron,uni02C9,breve,dotaccent,ring,tilde,hungarumlaut,quoteleft,quoteright,quotedblleft,quotedblright,dagger,daggerdbl,trademark,caron.alt"
	k="74" />
    <hkern g1="percent,onequarter,onehalf,threequarters"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="93" />
    <hkern g1="percent,onequarter,onehalf,threequarters"
	g2="V,W,v,w"
	k="74" />
    <hkern g1="percent,onequarter,onehalf,threequarters"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="102" />
    <hkern g1="perthousand,uni2080,uni2081,uni2082,uni2083,uni2084,uni2085,uni2086,uni2087,uni2088,uni2089,zero.dnom,one.dnom,two.dnom,three.dnom,four.dnom,five.dnom,six.dnom,seven.dnom,eight.dnom,nine.dnom"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="93" />
    <hkern g1="perthousand,uni2080,uni2081,uni2082,uni2083,uni2084,uni2085,uni2086,uni2087,uni2088,uni2089,zero.dnom,one.dnom,two.dnom,three.dnom,four.dnom,five.dnom,six.dnom,seven.dnom,eight.dnom,nine.dnom"
	g2="V,W,v,w"
	k="74" />
    <hkern g1="perthousand,uni2080,uni2081,uni2082,uni2083,uni2084,uni2085,uni2086,uni2087,uni2088,uni2089,zero.dnom,one.dnom,two.dnom,three.dnom,four.dnom,five.dnom,six.dnom,seven.dnom,eight.dnom,nine.dnom"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="112" />
    <hkern g1="pi,pi.ss01"
	g2="quotedbl,quotesingle,asterisk,asciicircum,grave,dieresis,registered,overscore,degree,acute,circumflex,caron,uni02C9,breve,dotaccent,ring,tilde,hungarumlaut,quoteleft,quoteright,quotedblleft,quotedblright,dagger,daggerdbl,trademark,caron.alt"
	k="56" />
    <hkern g1="pi,pi.ss01"
	g2="percent,onequarter,onehalf,threequarters,perthousand"
	k="37" />
    <hkern g1="pi,pi.ss01"
	g2="plus,equal,greater,guillemotleft,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	k="19" />
    <hkern g1="pi,pi.ss01"
	g2="hyphen,less,braceleft,asciitilde,logicalnot,uni00AD,middot,endash,emdash,bullet,minus,uni2219"
	k="37" />
    <hkern g1="pi,pi.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="19" />
    <hkern g1="pi,pi.ss01"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="19" />
    <hkern g1="pi,pi.ss01"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="93" />
    <hkern g1="pi,pi.ss01"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Utilde,utilde,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek,Uhorn,uhorn,uni1EE4,uni1EE5,uni1EE6,uni1EE7,uni1EE8,uni1EE9,uni1EEA,uni1EEB,uni1EEC,uni1EED,uni1EEE,uni1EEF,uni1EF0,uni1EF1"
	k="19" />
    <hkern g1="pi,pi.ss01"
	g2="V,W,v,w"
	k="93" />
    <hkern g1="pi,pi.ss01"
	g2="X,x"
	k="19" />
    <hkern g1="pi,pi.ss01"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="121" />
    <hkern g1="pi,pi.ss01"
	g2="ordfeminine,uni00B2,uni00B3,uni00B9,ordmasculine,uni2070,uni2074,uni2075,uni2076,uni2077,uni2078,uni2079,zero.numr,one.numr,two.numr,three.numr,four.numr,five.numr,six.numr,seven.numr,eight.numr,nine.numr"
	k="56" />
    <hkern g1="pi,pi.ss01"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="19" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="28" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="J,j,J.ss01,j.ss01"
	k="37" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="37" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="V,W,v,w"
	k="28" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="X,x"
	k="28" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="47" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="47" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="Z,z,Zacute,zacute,Zdotaccent,zdotaccent,Zcaron,zcaron"
	k="28" />
    <hkern g1="plus,less,equal,guillemotleft,logicalnot,plusminus,guillemotright,multiply,divide,guilsinglleft,guilsinglright,approxequal,notequal,lessequal,greaterequal"
	g2="parenright,bracketright,braceright"
	k="9" />
    <hkern g1="quotedbl,quotesingle,asterisk,asciicircum,grave,dieresis,registered,overscore,degree,acute,circumflex,caron,uni02C9,breve,dotaccent,ring,tilde,hungarumlaut,quoteleft,quoteright,quotedblleft,quotedblright,dagger,daggerdbl,trademark,caron.alt"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="74" />
    <hkern g1="quotedbl,quotesingle,asterisk,asciicircum,grave,dieresis,registered,overscore,degree,acute,circumflex,caron,uni02C9,breve,dotaccent,ring,tilde,hungarumlaut,quoteleft,quoteright,quotedblleft,quotedblright,dagger,daggerdbl,trademark,caron.alt"
	g2="J,j,J.ss01,j.ss01"
	k="73" />
    <hkern g1="quotedbl,quotesingle,asterisk,asciicircum,grave,dieresis,registered,overscore,degree,acute,circumflex,caron,uni02C9,breve,dotaccent,ring,tilde,hungarumlaut,quoteleft,quoteright,quotedblleft,quotedblright,dagger,daggerdbl,trademark,caron.alt"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="149" />
    <hkern g1="semicolon,exclamdown"
	g2="ampersand,zero,six,eight,at,C,G,O,Q,c,g,o,q,copyright,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Gbreve,gbreve,uni0122,uni0123,Omacron,omacron,Ohungarumlaut,ohungarumlaut,OE,oe,Ohorn,ohorn,uni1ECC,uni1ECD,uni1ECE,uni1ECF,uni1ED0,uni1ED1,uni1ED2,uni1ED3,uni1ED4,uni1ED5,uni1ED6,uni1ED7,uni1ED8,uni1ED9,uni1EDA,uni1EDB,uni1EDC,uni1EDD,uni1EDE,uni1EDF,uni1EE0,uni1EE1,uni1EE2,uni1EE3,Euro"
	k="-19" />
    <hkern g1="semicolon,exclamdown"
	g2="T,t,uni0162,uni0163,Tcaron,tcaron,uni021A,uni021B"
	k="37" />
    <hkern g1="semicolon,exclamdown"
	g2="V,W,v,w"
	k="37" />
    <hkern g1="semicolon,exclamdown"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="65" />
    <hkern g1="zero,three,eight,nine,at,B,C,D,O,Q,b,c,d,o,q,copyright,Ccedilla,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,germandbls,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Dcaron,dcaron,Dcroat,dslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut,uni1E9E,B.ss01,D.ss01,Eth.ss01,Dcaron.ss01,Dcroat.ss01,b.ss01,d.ss01,eth.ss01,dcaron.ss01,dcroat.ss01"
	g2="A,a,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring,agrave,aacute,acircumflex,atilde,adieresis,aring,Amacron,amacron,Abreve,abreve,Aogonek,aogonek,uni1EA0,uni1EA1,uni1EA2,uni1EA3,uni1EA4,uni1EA5,uni1EA6,uni1EA7,uni1EA8,uni1EA9,uni1EAA,uni1EAB,uni1EAC,uni1EAD,uni1EAE,uni1EAF,uni1EB0,uni1EB1,uni1EB2,uni1EB3,uni1EB4,uni1EB5,uni1EB6,uni1EB7,A.ss01,Aacute.ss01,Abreve.ss01,Acircumflex.ss01,Adieresis.ss01,Agrave.ss01,Amacron.ss01,Aogonek.ss01,Aring.ss01,Atilde.ss01,a.ss01,aacute.ss01,abreve.ss01,acircumflex.ss01,adieresis.ss01,agrave.ss01,amacron.ss01,aogonek.ss01,aring.ss01,atilde.ss01"
	k="28" />
    <hkern g1="zero,three,eight,nine,at,B,C,D,O,Q,b,c,d,o,q,copyright,Ccedilla,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,germandbls,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Dcaron,dcaron,Dcroat,dslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut,uni1E9E,B.ss01,D.ss01,Eth.ss01,Dcaron.ss01,Dcroat.ss01,b.ss01,d.ss01,eth.ss01,dcaron.ss01,dcroat.ss01"
	g2="B,D,E,F,H,I,K,M,N,P,R,b,d,e,f,h,i,k,m,n,p,r,Egrave,Eacute,Ecircumflex,Edieresis,Igrave,Iacute,Icircumflex,Idieresis,Eth,Ntilde,egrave,eacute,ecircumflex,edieresis,igrave,iacute,icircumflex,idieresis,eth,ntilde,Dcaron,dcaron,Dcroat,dslash,Emacron,emacron,Edotaccent,edotaccent,Eogonek,eogonek,Ecaron,ecaron,Itilde,itilde,Imacron,imacron,Iogonek,iogonek,Idot,uni0136,uni0137,Nacute,nacute,uni0145,uni0146,Ncaron,ncaron,Racute,racute,uni0156,uni0157,Rcaron,rcaron,uni1EB8,uni1EB9,uni1EBA,uni1EBB,uni1EBC,uni1EBD,uni1EBE,uni1EBF,uni1EC0,uni1EC1,uni1EC2,uni1EC3,uni1EC4,uni1EC5,uni1EC6,uni1EC7,uni1EC8,uni1EC9,uni1ECA,uni1ECB,fi,i.loclTRK"
	k="9" />
    <hkern g1="zero,three,eight,nine,at,B,C,D,O,Q,b,c,d,o,q,copyright,Ccedilla,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,germandbls,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Dcaron,dcaron,Dcroat,dslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut,uni1E9E,B.ss01,D.ss01,Eth.ss01,Dcaron.ss01,Dcroat.ss01,b.ss01,d.ss01,eth.ss01,dcaron.ss01,dcroat.ss01"
	g2="U,u,Ugrave,Uacute,Ucircumflex,Udieresis,ugrave,uacute,ucircumflex,udieresis,Utilde,utilde,Umacron,umacron,Uring,uring,Uhungarumlaut,uhungarumlaut,Uogonek,uogonek,Uhorn,uhorn,uni1EE4,uni1EE5,uni1EE6,uni1EE7,uni1EE8,uni1EE9,uni1EEA,uni1EEB,uni1EEC,uni1EED,uni1EEE,uni1EEF,uni1EF0,uni1EF1"
	k="9" />
    <hkern g1="zero,three,eight,nine,at,B,C,D,O,Q,b,c,d,o,q,copyright,Ccedilla,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,germandbls,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Dcaron,dcaron,Dcroat,dslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut,uni1E9E,B.ss01,D.ss01,Eth.ss01,Dcaron.ss01,Dcroat.ss01,b.ss01,d.ss01,eth.ss01,dcaron.ss01,dcroat.ss01"
	g2="V,W,v,w"
	k="28" />
    <hkern g1="zero,three,eight,nine,at,B,C,D,O,Q,b,c,d,o,q,copyright,Ccedilla,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,germandbls,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Dcaron,dcaron,Dcroat,dslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut,uni1E9E,B.ss01,D.ss01,Eth.ss01,Dcaron.ss01,Dcroat.ss01,b.ss01,d.ss01,eth.ss01,dcaron.ss01,dcroat.ss01"
	g2="X,x"
	k="19" />
    <hkern g1="zero,three,eight,nine,at,B,C,D,O,Q,b,c,d,o,q,copyright,Ccedilla,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,germandbls,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Dcaron,dcaron,Dcroat,dslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut,uni1E9E,B.ss01,D.ss01,Eth.ss01,Dcaron.ss01,Dcroat.ss01,b.ss01,d.ss01,eth.ss01,dcaron.ss01,dcroat.ss01"
	g2="Y,y,yen,Yacute,yacute,ydieresis,Ydieresis,Ygrave,ygrave,uni1EF4,uni1EF5,uni1EF6,uni1EF7,uni1EF8,uni1EF9"
	k="37" />
    <hkern g1="zero,three,eight,nine,at,B,C,D,O,Q,b,c,d,o,q,copyright,Ccedilla,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,germandbls,ccedilla,ograve,oacute,ocircumflex,otilde,odieresis,oslash,Cacute,cacute,Ccaron,ccaron,Dcaron,dcaron,Dcroat,dslash,Omacron,omacron,Ohungarumlaut,ohungarumlaut,uni1E9E,B.ss01,D.ss01,Eth.ss01,Dcaron.ss01,Dcroat.ss01,b.ss01,d.ss01,eth.ss01,dcaron.ss01,dcroat.ss01"
	g2="AE,ae,AE.ss01,ae.ss01"
	k="19" />
  </font>
</defs></svg>
