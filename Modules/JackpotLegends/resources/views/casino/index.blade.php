@php
    $endpoint = config('jackpotlegends.endpoints.casino.ranking');
    $eventCasinoTerms = config('jackpotlegends.casino.terms');
    $banner = Module::asset('jackpotlegends:images/huyen-thoai-om-hu-song-bai.webp');
    $bannerMb = Module::asset('jackpotlegends:images/huyen-thoai-om-hu-song-bai-mb.webp');
    $rankingBackground = Module::asset('jackpotlegends:images/song-bai-ranking-bg.jpg');
    $termBackground = Module::asset('jackpotlegends:images/song-bai-terms-bg.jpg');

    $largeBox = array_map(function ($item) {
        return $item->amount;
    }, array_slice($weeklyUserData, 0, 10));

    $smallBox = array_map(function ($item) {
        return $item->amount;
    }, array_slice($weeklyUserData, 10, 50));

    $largeBoxTooltip = [
        'Tiền thưởng = Gi<PERSON> trị <PERSON> * (Tổng doanh thu cược sòng bài hợp lệ của người chơi/ Tổng doanh thu sòng bài hợp lệ của top 1-10 * 100)(%)',
        'Hũ lớn: Bắt đầu với giá trị 100,000,000 VND, tích lũy thêm 0.2% doanh thu từ mỗi vé cược hợp lệ.'
    ];

    $smallBoxTooltip = [
        'Tiền thưởng = Giá trị Hũ Nhỏ * (Tổng doanh thu cược sòng bài hợp lệ của người chơi/ Tổng doanh thu sòng bài hợp lệ của top 11-50 * 100)(%)',
        'Hũ nhỏ: Bắt đầu với giá trị 50,000,000 VND, tích lũy thêm 0.1% doanh thu từ mỗi vé cược hợp lệ.'
    ];
@endphp
@vite(['Modules/JackpotLegends/resources/assets/js/app.js', 'Modules/JackpotLegends/resources/assets/sass/app.scss'])
<x-layout>
    <script>
        window.eventEndpoint = @json($endpoint);
    </script>
    <div class="page-event-casino max-xl:mb-5 xl:mt-5 flex flex-col gap-[18px] xl:gap-10 pb-[var(--menu-mobile-bottom)] xl:pb-[80px]">
        <div class="max-xl:-mx-4">
            <div class="xl:rounded-lg overflow-hidden">
                <x-jackpotlegends::banner :image="$banner" :imageMb="$bannerMb" />
            </div>
        </div>
        <x-jackpotlegends::jackpot :largeBox="$largeBox" :smallBox="$smallBox" :largeBoxTooltip="$largeBoxTooltip"
            :smallBoxTooltip="$smallBoxTooltip" />
        <x-jackpotlegends::ranking :background="$rankingBackground" title="Top bảng xếp hạng sòng bài"
            :dateList="$dateList" :weeklyUserData="$weeklyUserData" :currentUserPosition="$currentUserPosition" />
        <x-jackpotlegends::live-stream :casinoGames="$casinoGames" />
        <x-jackpotlegends::rules-terms :background="$termBackground" :eventTerms="$eventCasinoTerms" />
    </div>
</x-layout>
