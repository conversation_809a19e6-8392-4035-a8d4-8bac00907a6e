@props([
    'player',
    'index',
    'currentUserPosition',
    'backgrounds'
])
@php
    $user = Auth::user();
    $isTop1 = $index + 1 === 1;
    $isTop2 = $index + 1 === 2;
    $isTop3 = $index + 1 === 3;
    $isCurrentUser = isset($user) && isset($currentUserPosition) && $index + 1 === $currentUserPosition->index;
    $isTopAndNotCurrent = ($isTop1 || $isTop2 || $isTop3) && !$isCurrentUser;
    $isCurrentUserAndNotTop = !$isTop1 && !$isTop2 && !$isTop3 && $isCurrentUser;
@endphp

<div @class([
    'user-item text-white xl:text-neutral-200 relative flex items-center shrink-0 text-[10px] xl:text-sm xl:py-[2px] pl-[5px] xl:pl-[0px] pr-[13px] xl:pr-[0px] rounded-[4px] h-[38px] xl:h-[42px]',
    'user-item-first' => $isTop1 && !$isCurrentUser,
    'user-item-second' => $isTop2 && !$isCurrentUser,
    'user-item-third' => $isTop3 && !$isCurrentUser,
    'user-item-current !user-item bg-[#054331] text-white' => $isCurrentUser,
    'text-white' => $isCurrentUserAndNotTop,
])>
    <div class="flex w-[52px] items-center gap-2 xl:w-[14%] xl:pl-[25px]">
        <div class="user-item__number relative flex size-[32px] items-center justify-center xl:size-[38px]">
            <span
                class="rank-no-text z-[1] -mt-[2px] text-sm">{{ str_pad($index + 1, 2, '0', STR_PAD_LEFT) }}</span>
        </div>
    </div>
    <div class="flex w-[72px] items-center justify-start xl:pl-[36px] gap-2 xl:w-[30%]">
        <div @class([
            'line-clamp-1 text-left flex-1 font-normal xl:font-medium text-white',
            'font-bold leading-[20px]' => $isCurrentUserAndNotTop,
        ]) title="{{ $player->username ?? '******' }}">
            {{ $player->username ?? '******' }}
        </div>
        <div class="border-item h-[24px] max-xl:h-[20px]"></div>
    </div>
    <div
        class="flex w-[107px] items-center justify-end gap-[12px] font-medium xl:w-[30%] xl:gap-[36px]">
        <div class="line-clamp-1 text-right flex-1 tabular-nums" title="{{ number_format($player->turnover ?? 000, 0, ',', ',') }}">
            {{ number_format($player->turnover ?? 000, 0, ',', ',') }}
        </div>
        <div class="border-item h-[24px] max-xl:h-[20px]"></div>
    </div>
    <div class="flex flex-1 items-center justify-end font-medium xl:w-[26%] xl:pr-[20px]">
        <div class="line-clamp-1 tabular-nums" title="{{ number_format($player->amount ?? 000, 0, ',', ',') }}">
            {{ number_format($player->amount ?? 000, 0, ',', ',') }}
        </div>
    </div>
</div>
