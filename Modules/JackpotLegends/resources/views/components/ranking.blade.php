@props(['background', 'title', 'dateList', 'weeklyUserData', 'currentUserPosition'])

@php
    use App\Helpers\DetectDeviceHelper;
    use Illuminate\Support\Facades\Auth;

    $user = Auth::user();
    $isLoggedIn = Auth::check();
    $isMobile = DetectDeviceHelper::isMobile();
    $activeTab = 0;
    $currentData = $weeklyUserData ?? [];
    $backgrounds = [
        'top' => [
            0 => $isMobile ? 'pre-bg-first-mb.webp' : 'pre-bg-first.webp',
            1 => $isMobile ? 'pre-bg-second-mb.webp' : 'pre-bg-second.webp',
            2 => $isMobile ? 'pre-bg-third-mb.webp' : 'pre-bg-third.webp',
        ],
        'user' => $isMobile ? 'pre-bg-user-mb.webp' : 'pre-bg-user.webp',
        'normal' => $isMobile ? 'pre-bg-normal-mb.webp' : 'pre-bg-normal.png',
    ];
@endphp
<div
    class="event-ranking relative min-h-[390px] bg-cover bg-center max-xl:-mx-4 max-xl:flex max-xl:justify-center max-xl:bg-[#083022] xl:min-h-[614px]">
    <img src="{{ asset($background) }}" alt="ranking-background"
        class="absolute left-0 top-0 h-full w-full rounded-[20px] object-left-top object-cover max-xl:hidden">
    <div class="relative w-fit max-xl:w-full xl:absolute xl:right-[32px] xl:top-[32px] xl:w-[62.5%] xl:max-w-[760px] max-xl:bg-[#033A3A]">
        @if (isset($weeklyUserData) && count($weeklyUserData) > 0)
            <div
                class="player-count absolute left-0 top-[14px] flex h-[24px] min-w-[89px] pr-[11px] items-center bg-[url('/public/modules/jackpotlegends/images/user-counter-mb.png')] bg-contain bg-no-repeat pl-1 text-[10px] xl:hidden">
                <span
                    class="pr-1 text-[10px] font-medium text-neutral">{{ number_format(random_int(100, 400), 0, ',', ',') }}</span>
                người chơi
            </div>
        @endif
        <img src="{{ Module::asset('jackpotlegends:images/money-mb.avif') }}" alt="ranking-bg"
            class="absolute right-[4px] top-0 h-[60px] w-[90px] xl:hidden">
        <div
            class="ranking-title font-svn-vt-redzone-classic font-redzone mb-[25px] text-center text-[24px] uppercase leading-[29px] text-neutral max-xl:mb-[20px] max-xl:mt-[19px] max-xl:text-[16px] max-xl:leading-[15px]">
            {{ $title }}
        </div>

        <div class="ranking-data-wrapper rounded-lg bg-[#033A3A]">
            <div class="ranking-data xl:p-4 xl:pb-0 xl:pr-[5px]">
                <!-- Tab dates -->
                <div
                    class="tab-dates mb-2 flex items-center gap-2 max-xl:mb-1 max-xl:rounded-[4px] max-xl:px-4 max-xl:pt-1 xl:pr-3">
                    <div
                        class="custom-scrollbar flex gap-[2px] overflow-x-auto font-medium max-xl:gap-1 max-xl:rounded-[4px] flex-1">
                        @foreach ($dateList as $index => $date)
                            <div data-tab="{{ $index }}" data-start-date="{{ $date['startDate'] }}"
                                data-end-date="{{ $date['endDate'] }}"
                                onclick="eventSwitchTab({{ $index }}, '{{ $date['startDate'] }}', '{{ $date['endDate'] }}')"
                                class="tab-item {{ $activeTab == $index ? 'pb-[4px] active' : 'pb-[6px] default' }} flex h-[30px] min-w-fit cursor-pointer items-center justify-center px-4 pt-[6px] text-xs leading-4 xl:text-[14px] xl:leading-5">
                                {{ Carbon\Carbon::parse($date['startDate'])->format('d/m') }}-{{ Carbon\Carbon::parse($date['endDate'])->format('d/m') }}
                            </div>
                        @endforeach
                    </div>

                    @if (isset($weeklyUserData) && count($weeklyUserData) > 0)
                        <div
                            class="player-count ml-auto flex h-[30px] min-w-fit items-center gap-1 rounded-lg bg-[#011D1D] px-4 py-1.5 text-[14px] leading-[18px] max-xl:hidden">
                            <span
                                class="font-bold text-neutral tracking-[0.02em]">{{ number_format(random_int(100, 400), 0, ',', ',') }}</span>
                            người chơi hiện tại
                        </div>
                    @endif
                </div>

                <!-- Ranking tables -->
                <div class="ranking-content max-xl:px-4">
                    <div class="ranking-content-wrapper w-full">
                        <!-- Header -->
                        <div
                            class="{{ count($currentData) > 0 ? '' : 'max-xl:hidden' }}  ranking-table-header mb-[2px] flex h-[32px] xl:h-[36px] items-center rounded-t-[8px] p-2 pr-[13px] text-[10px] font-semibold text-white xl:mr-[11px] xl:rounded-[4px] xl:pl-[26px] xl:pr-[20px] xl:text-xs xl:font-bold">
                            <div class="ranking-table-header-th max-xl:pl-[8px] !text-left w-[56px] xl:w-[14%] xl:pl-[25px]">RANK</div>
                            <div class="ranking-table-header-th w-[72px] xl:w-[30%] xl:pl-[35px] !text-left">NGƯỜI CHƠI</div>
                            <div class="ranking-table-header-th w-[107px] xl:w-[30%] pr-[12px] xl:pr-[34px] !text-right">TIỀN CƯỢC (K)</div>
                            <div class="ranking-table-header-th flex flex-1 items-center justify-end xl:w-[26%] pr-[13px] xl:pr-[20px]"><span
                                    class="mr-1 max-xl:hidden">TIỀN</span>THƯỞNG (VND)</div>
                        </div>

                        <!-- Ranking rows -->
                        <div class="ranking-loading hidden h-[270px] w-full items-center justify-center xl:h-[354px]">
                            <img class="" src="/asset/images/spinner.svg" class="size-6" />
                        </div>

                        <div
                            class="{{ count($currentData) > 0 ? '' : 'ranking-content-list-empty' }} ranking-content-list custom-scrollbar flex xl:h-[354px] flex-col gap-[2px] overflow-y-auto h-[270px]">
                            @if (count($currentData) > 0)
                                @foreach ($currentData as $index => $player)
                                    <x-jackpotlegends::ranking-item :player="$player" :index="$index"
                                        :currentUserPosition="$currentUserPosition" :backgrounds="$backgrounds" />
                                @endforeach
                            @else
                                <x-jackpotlegends::empty-ranking />
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @if (count($currentData) > 0)
                @if (!$user)
                    <div
                        class="table-footer-auth flex h-[50px] xl:h-[56px] items-center justify-between rounded-b-lg bg-[#054331] px-[16px] max-xl:gap-[24px] xl:pl-[37px] xl:pr-[32px]">
                        <span class="text-[12px] leading-[18px] italic text-white xl:text-sm">Tham gia ngay sự kiện ôm hũ để thu về bạc tỷ
                            trong tay</span>
                        @if (isset($weeklyUserData) && count($weeklyUserData) > 0)
                            <x-kit.button
                                class="shrink-0 h-[24px] xl:h-[36px] w-[154px] text-xs xl:text-[14px] font-semibold xl:leading-[18px] max-xl:w-[126px] max-xl:rounded"
                                onclick="login()">
                                ĐĂNG NHẬP NGAY
                            </x-kit.button>
                        @endif
                    </div>
                @else
                    <div
                        class="current-user-wrapper flex h-[46px] items-center justify-between bg-[#054331] xl:h-[56px] xl:rounded-b-lg">
                        @if ($isLoggedIn)
                            <div class="pl-[16px] pr-[16px] w-full">
                                <div
                                    class="current-user z-[1] relative flex h-[46px] w-full items-center py-[2px] text-sm text-[#FFD176] xl:h-[56px]">
                                    <img src="{{ Module::asset('jackpotlegends:images/pre-bg-your-bot.webp') }}"
                                        alt="current-user-bg"
                                        class="absolute z-0 left-0 hidden h-[56px] w-[132px] xl:block xl:rounded-bl-[4px] -ml-4" />
                                    <img src="{{ Module::asset('jackpotlegends:images/pre-bg-your-bot-mb.webp') }}"
                                        alt="current-user-bg"
                                        class="absolute z-0 left-0 h-[46px] w-[74px] xl:hidden xl:rounded-bl-[4px] -ml-4" />
                                    <div class="flex w-[56px] justify-start items-center gap-2 xl:max-w-[114px] xl:w-[14%] pl-1 xl:pl-[25px] relative z-[1]">
                                        <div
                                            class="relative flex size-[32px] xl:size-[38px] items-center justify-center">
                                            @if (isset($currentUserPosition->index) && $currentUserPosition->index < 4)
                                                <span class="user-top user-top-{{ $currentUserPosition->index }}"></span>
                                            @else
                                                <span
                                                    class="current-user-rank rank-no-text z-[1] -mr-[2px] -mt-[2px] text-[18px] leading-[22px] xl:text-[19px]">
                                                    {{
                                                        isset($currentUserPosition->index) && $currentUserPosition->index >= 1 && $currentUserPosition->index <= 50
                                                            ? $currentUserPosition->index
                                                            : '50+'
                                                    }}
                                                </span>
                                            @endif
                                        </div>
                                    </div>
                                    <div
                                        class="flex w-[72px] items-center justify-between gap-2 text-[10px] font-bold leading-4 xl:w-[30%] xl:text-[16px] xl:leading-6 xl:pl-[36px] pr-2 relative z-[1]">
                                        <span class="truncate">{{ $currentUserPosition->username ?? $user->username }}</span>
                                    </div>
                                    <div
                                        class="current-user-turnover flex w-[107px] items-center justify-end pr-[12px] text-[10px] font-bold leading-4 xl:w-[30%] xl:pr-[36px] xl:text-[14px] xl:leading-5">
                                        {{ isset($currentUserPosition->turnover) && $currentUserPosition->turnover > 0 ? number_format($currentUserPosition->turnover ?? 000, 0, ',', ',') : '-' }}
                                    </div>
                                    <div
                                        class="current-user-amount flex flex-1 items-center justify-end text-[10px] font-bold leading-4 max-xl:pr-[20px] xl:text-[14px] xl:leading-5 xl:w-[26%] xl:pr-[20px]">
                                        {{ isset($currentUserPosition->amount) && $currentUserPosition->amount > 0 ? number_format($currentUserPosition->amount ?? 000, 0, ',', ',') : '-' }}
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                @endif
            @endif
        </div>
    </div>
</div>
