@props(['image', 'imageMb', 'startDate', 'isStarted', 'uniqueId'])

<div class="w-full relative xl:aspect-[1216/300] aspect-[390/150]">
    <picture>
        <source
            media="(max-width: 1200px)"
            srcset="{{ asset('modules/jackpotlegends/images/banner/banner-bg-mb.webp') }}"
        >
        <img
            class="w-full h-auto"
            src="{{ asset('modules/jackpotlegends/images/banner/banner-bg.webp') }}"
            alt="thumbnail"
        >
    </picture>
    {{-- addon & countdown--}}
    <div class="absolute xl:left-[calc((85/1216)*100%)] left-[calc((16/390)*100%)] top-0 flex flex-col justify-center gap-1.5 h-full">
        <div class="xl:h-[calc((217/300)*100%)] h-[calc((79/150)*100%)]">
            <picture>
                <source
                    media="(max-width: 1200px)"
                    srcset="{{ asset('modules/jackpotlegends/images/banner/banner-addon-mb.avif') }}"
                >
                <img
                    class="w-auto h-full"
                    src="{{ asset('modules/jackpotlegends/images/banner/banner-addon.avif') }}"
                    alt="thumbnail"
                >
            </picture>
        </div>
        <!-- <div class="xl:h-[32px] h-[18px] rounded overflow-hidden flex items-center max-[400px]:scale-[1] max-sm:scale-[1.2] max-xl:scale-[2] max-md:scale-[1.7] [transform-origin:top_left]">
            <div class="h-full xl:pl-[15px] pl-1 xl:pr-5 pr-2 flex items-center bg-[linear-gradient(90deg,_#092D19_0%,_#1C9351_100%)] uppercase border border-[#10630A] rounded-[4px_0_0_4px] overflow-hidden">
                <span class="xl:text-sm text-[8px] leading-[12px] font-medium text-[#F1EFB4] tracking-tighter">KẾT THÚC SAU</span>
            </div>
            <div class="h-full xl:w-[189px] w-[97px] rounded bg-[linear-gradient(279.31deg,_#FFE88E_0.23%,#FFFFD6_58.12%,_#FFE88E_99.77%)] xl:-ml-1 -ml-0.5 flex items-center justify-center">
                <div class="flex items-center xl:gap-2.5 gap-1 text-[#2D2E33] xl:text-sm text-[8px] leading-[12px] font-medium">
                    <span class="jackpotlegends-js-day">-- NGÀY</span>
                    <div class="w-[1px] xl:h-[15px] h-[11px] bg-[#EBD05F]"></div>
                    <span class="whitespace-nowrap w-[43px] xl:w-[77px] jackpotlegends-js-time text-[#3D3F4D]">-- : -- : --</span>
                </div>
            </div>
        </div> -->
    </div>
</div>
@vite(['Modules/JackpotLegends/resources/assets/js/banner-countdown.js'])
