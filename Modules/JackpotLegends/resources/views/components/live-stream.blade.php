@php
    use App\Enums\UrlPathEnum;
    $swiperRequiredClass = 'events-casino-swiper';
    $swiperCasinoConfig = [
        'slidesPerView' => 1.167,
        'spaceBetween' => 12,
        'loop' => false,
        'navigation' => [
            'prevEl' => '.events-casino-swiper-prev',
            'nextEl' => '.events-casino-swiper-next',
        ],
        'breakpoints' => [
            '1200' => [
                'slidesPerView' => 3,
                'spaceBetween' => 20,
            ],
        ],
    ];
@endphp

<div class="events-sports">
    <div class="sports-title mb-3 flex items-center justify-between xl:mb-6">
        <div class="font-redzone text-[16px] uppercase leading-[28px] text-neutral xl:text-[24px] xl:leading-[29px]">
            Game áp dụng
        </div>
        <div class="flex items-center gap-2">
            <a href="{{ UrlPathEnum::CASINO->value }}"
                class="rounded-lg bg-[#FFFFFF33] px-2 py-1.5 text-xs font-medium capitalize text-[#6CFE00] xl:hover:bg-[#FFFFFF22]">
                Xem thêm
                <i class="icon-right-chevron text-[14px] text-white group-hover:text-primary-300"></i>
            </a>
            <div class="section-header__nav flex items-center gap-1 max-xl:hidden">

                <div @class([
                    'size-[28px] cursor-pointer bg-neutral/20 text-neutral rounded-lg flex items-center justify-center xl:hover:bg-[#FFFFFF22]',
                    $swiperRequiredClass.'-prev',
                    '[&.swiper-button-disabled]:pointer-events-none [&.swiper-button-disabled]:opacity-50',
                ])>
                    <i
                        class="icon-chevron-left text-[20px]"></i>
                </div>
                <div @class([
                    'size-[28px] cursor-pointer bg-neutral/20 text-neutral rounded-lg flex items-center justify-center xl:hover:bg-[#FFFFFF22]',
                    $swiperRequiredClass.'-next',
                    '[&.swiper-button-disabled]:pointer-events-none [&.swiper-button-disabled]:opacity-50',
                ])>
                    <i
                        class="icon-chevron-right text-[20px]"></i>
                </div>
            </div>


        </div>
    </div>

    <div class="casino-list-wrapper max-xl:-mx-4">
        <x-ui.common.swiper :autoplay="true" :swiperConfig="$swiperCasinoConfig" :swiperRequiredClass="$swiperRequiredClass"
            swiperWrapperClass="events-casino-swiper-game max-w-[305px] xl:max-w-[calc(100%/3-14px)]">
            @foreach ($casinoGames as $item)
                <div class="swiper-slide">
                    <x-ui.game.card-horizontal name="{{ $item->name ?? 'title' }}" image="{{ $item->image ?? '' }}"
                        partner="{{ $item->partner ?? '' }}" partner_provider="{{ $item->partner_provider ?? '' }}"
                        partner_txt="{{ $item->partner_txt ?? '' }}" is_favorite="{{ $item->is_favorite ?? false }}"
                        data-api="{{ $item->api_url ?? '' }}" id="{{ $item->partner_game_id ?? '' }}"
                        tags="{{ $item->tags ?? '' }}" lobbyType="casino" deny_info="{{ $item->deny_info ?? false }}"
                        jackpot="{{ $item->jackpot ?? 0 }}">
                    </x-ui.game.card-horizontal>
                </div>
            @endforeach
        </x-ui.common.swiper>
    </div>
</div>
