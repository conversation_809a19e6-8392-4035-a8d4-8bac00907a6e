@props(['background', 'eventTerms'])

@php
    $itemClass =
        "rules-item relative bg-[url('/public/modules/jackpotlegends/images/bg-rule-item.jpg')] bg-center bg-cover bg-no-repeat w-full min-h-[128px] flex flex-col text-left justify-center items-start text-[14px] leading-[20px] font-normal rounded-[8px]";

    $content = [
        [
            [
                'hasDot' => true,
                'content' => '<strong>Thời gian áp dụng:</strong> Dài hạn.',
            ],
            [
                'hasDot' => true,
                'content' => 'Sự kiện tích luỹ và nổ hũ diễn ra hằng tuần từ 00:00 thứ hai tới 23:59 chủ nhật hằng tuần.',
            ],
        ],
        [
            [
                'hasDot' => true,
                'content' => '<strong>Hũ lớn:</strong> Bắt đầu với giá trị 100,000,000 VND, tích lũy thêm 0.2% doanh thu từ mỗi vé cược hợp lệ.',
            ],
            [
                'hasDot' => true,
                'content' => '<strong>Hũ nhỏ:</strong> Bắt đầu với giá trị 50,000,000 VND, tích lũy thêm 0.1% doanh thu từ mỗi vé cược hợp lệ.',
            ],
            [
                'hasDot' => true,
                'content' => 'Hũ bắt đầu tăng sau khi tích lũy bù đủ số tiền khởi tạo.',
            ],
        ],
        [
            [
                'hasDot' => true,
                'content' => 'Khi số tiền tích lũy đã đạt đủ mức khởi tạo ban đầu, giá trị của hũ sẽ bắt đầu tăng lên, tạo cơ hội cho người chơi tiếp tục săn đón và gia tăng phần thưởng tích lũy.',
            ],
            [
                'hasDot' => true,
                'content' => '<strong>Thời gian trả thưởng:</strong> Trong vòng 24h sau khi kết thúc mỗi vòng.',
            ],
        ],
    ];
@endphp

<div class="events-rules max-xl:hidden">
    <div class="ranking-title font-redzone mb-[24px] text-center text-[24px] uppercase leading-[1.2] text-neutral">
        Thể lệ chương trình
    </div>

    <div class="rules-terms-content grid grid-cols-3 gap-4 text-left text-[14px] leading-[20px]">
        @foreach ($content as $items)
            <div class="{{ $itemClass }} px-5 py-[26px]">
                @foreach ($items as $item)
                    <p class="{{ $item['hasDot'] ? 'rules-terms-item' : '' }}">{!! $item['content'] !!}</p>
                @endforeach
            </div>
        @endforeach
    </div>
</div>

<div class="events-terms max-xl:hidden">
    <div class="ranking-title font-redzone mb-[24px] text-center text-[24px] uppercase leading-[1.2] text-neutral">
        Điều khoản và điều kiện
    </div>

    <div class="terms-content relative h-[360px] rounded-lg pl-[40px] pt-[49px] text-sm">
        <img src="{{ asset($background) }}" alt="terms-background"
            class="absolute left-0 top-0 h-full w-full rounded-lg object-cover max-xl:hidden">
        <div class="relative z-[2] flex max-w-[670px] lg:w-[62.5%] flex-col gap-5 leading-[18px]">
            @foreach ($eventTerms as $term)
                <div class="flex gap-2">
                    <img src="{{ Module::asset('jackpotlegends:images/term-item.svg') }}" alt="term-item"
                        class="h-[16px] w-[13px]">
                    <p>{{ $term }}</p>
                </div>
            @endforeach
        </div>
    </div>
</div>

@php
    $ruleItemClass =
        "rules-item relative flex flex-col p-[10px] text-[10px] leading-[16px] rounded-lg bg-cover bg-center w-full bg-[url('/public/modules/jackpotlegends/images/bg-rule-item-mb.jpg')]";
@endphp
<div class="events-rules-terms-mobile xl:pt-2 xl:hidden">
    <div class="tabs mb-4 flex gap-2">
        <button
            class="tab-btn active"
            data-tab="rules">
            Thể lệ
        </button>
        <button
            class="tab-btn"
            data-tab="terms">
            Điều khoản
        </button>
    </div>


    <div class="tab-content">
        <div id="rules-content" class="tab-pane active">
            <div class="grid grid-cols-1 gap-[6px]">
                @foreach ($content as $items)
                    <div class="{{ $ruleItemClass }}">
                        @foreach ($items as $item)
                            <p class="{{ $item['hasDot'] ? '__dot' : '' }}">{!! $item['content'] !!}</p>
                        @endforeach
                    </div>
                @endforeach
            </div>
        </div>

        <div id="terms-content" class="tab-pane">
            <div class="relative rounded-lg bg-cover bg-center px-[6px] py-[10px] text-sm terms-content-mobile">
                <div class="flex flex-col gap-[10px]">
                    @foreach ($eventTerms as $term)
                        <div class="flex gap-2">
                            <img src="{{ Module::asset('jackpotlegends:images/term-item.svg') }}" alt="term-item"
                                class="h-[16px] w-[13px]">
                            <span class="text-[10px] leading-[15px]">{{ $term }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabPanes = document.querySelectorAll('.tab-pane');
            const activeBg = "bg-[url('/public/asset/images/jackpotlegends/bg/btn-tab-active.avif')]";
            const inactiveBg = "bg-[url('/public/asset/images/jackpotlegends/bg/btn-tab.avif')]";

            function switchTab(tabName) {
                // Hide all panes
                tabPanes.forEach(pane => {
                    pane.style.display = 'none';
                    pane.classList.remove('active');
                });

                // Show selected pane
                const selectedPane = document.getElementById(`${tabName}-content`);
                if (selectedPane) {
                    selectedPane.style.display = 'block';
                    selectedPane.classList.add('active');
                }
            }

            function updateButtonStyles(activeButton) {
                tabButtons.forEach(btn => {
                    btn.classList.remove('active', activeBg);
                    btn.classList.add(inactiveBg);
                });

                activeButton.classList.add('active', activeBg);
                activeButton.classList.remove(inactiveBg);
            }

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    updateButtonStyles(this);
                    switchTab(this.getAttribute('data-tab'));
                });
            });

            // Initialize first tab
            switchTab('rules');
        });
    </script>
@endpush
