@php
    use App\Enums\UrlPathEnum;

    $listSports = config('jackpotlegends.sport.games');
    $swiperRequiredClass = 'events-sports-swiper';
    $swiperSportsConfig = [
        'slidesPerView' => 2.383,
        'spaceBetween' => 12,
        'loop' => false,
        'navigation' => [
            'prevEl' => '.events-sports-swiper-prev',
            'nextEl' => '.events-sports-swiper-next',
        ],
        'breakpoints' => [
            '768' => [
                'slidesPerView' => 4.35,
                'spaceBetween' => 8,
            ],
            '1200' => [
                'slidesPerView' => 5.469,
                'spaceBetween' => 24,
            ],
        ],
    ];
@endphp

<div class="events-sports max-xl:pl-4">
    <div class="sports-title mb-3 xl:mb-6 flex items-center justify-between">
        <div class="uppercase text-neutral text-[16px] xl:text-[24px] leading-[28px] xl:leading-[29px] font-redzone">
            <span class="hidden xl:block">Thể thao áp dụng</span>
            <span class="xl:hidden">Game áp dụng</span>
        </div>
        <div class="flex items-center gap-4 pr-[16px] xl:pr-0">
            <div class="section-header__nav flex items-center gap-1 max-xl:hidden">
                <div
                    @class([
                        "w-[24px] h-[24px] bg-neutral-600 rounded-lg flex items-center justify-center group xl:hover:brightness-150",
                        "events-sports-swiper-prev",
                        "[&.swiper-button-disabled]:pointer-events-none",
                    ])
                >
                    <i class="icon-left-chevron text-neutral text-[14px] group-[&.swiper-button-disabled]:text-neutral-500">
                    </i>
                </div>
                <div
                    @class([
                        "w-[24px] h-[24px] bg-neutral-600 rounded-lg flex items-center justify-center group xl:hover:brightness-150",
                        "events-sports-swiper-next",
                        "[&.swiper-button-disabled]:pointer-events-none",
                    ])
                >
                    <i class="icon-right-chevron text-neutral text-[14px] group-[&.swiper-button-disabled]:text-neutral-500">
                    </i>
                </div>
            </div>
            <a href="{{ UrlPathEnum::SPORTS->value }}" class="ml-auto flex items-center gap-0 capitalize group">
                Xem thêm
                <i class="icon-right-chevron text-white text-[19px] leading-[20px] size-[20px]"></i>
            </a>
        </div>
    </div>

    <div class="sports-list-wrapper">
        <x-ui.common.swiper :autoplay="true" :swiperConfig="$swiperSportsConfig" :swiperRequiredClass="$swiperRequiredClass">
            @foreach ($listSports as $sport)
                <div class="swiper-slide">
                    <div 
                        class="group relative cursor-pointer mirror-effect 1backdrop-blur-[20px] max-xl:block xl:rounded-lg overflow-hidden 
                         aspect-[150/227] xl:aspect-[240/412] xl:max-w-[240px] xl:max-h-[412px]"
                        onclick="openSport({
                            link: '{{ $sport['url'] }}',
                            apiUrl: '{{ $sport['apiUrl'] ?? '' }}',
                            loginRequired: '{{ $sport['loginRequired'] ?? false }}',
                        })"
                    >
                        <picture class="block xl:hidden">
                            <source srcset="{{ asset($sport['imgMobile']) }}" media="(max-width: 1199px)">
                            <img class="w-full h-full object-cover" 
                                 src="{{ asset($sport['imgMobile']) }}" 
                                 alt="{{ $sport['title'] }}" />
                        </picture>
            
                        <div class="hidden xl:block relative">
                            <img class="w-full h-auto aspect-[240/412]" 
                                 src="{{ asset($sport['img']) }}" 
                                 alt="{{ $sport['title'] }}" 
                            />

                            <img
                                class="absolute top-[53.18px] right-[10.5px] size-[42px] object-cover"
                                src="{{ asset($sport['providerLogo']) }}"
                                alt="{{ $sport['title'] }}"
                            />
                            
                            <div class="flex flex-col items-start justify-center absolute bottom-0 w-full h-[99px] z-[2]">
                                <div class="flex flex-col gap-[2.8px] text-white pl-[26px] w-full">
                                    <p class="text-[20px] leading-[26px] font-bold">
                                        {{ $sport['title'] }}
                                    </p>
                                    <p class="text-[12px] leading-[16px] font-normal">
                                        {{ $sport['desc'] }}
                                    </p>

                                    <x-kit.button
                                        size="medium"
                                        rightIcon='icon-effect-arrow'
                                        type="gradient-secondary"
                                        class="w-[136px] h-0 py-0 bg-sur-brand-primary rounded-[12px] text-black flex items-center transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:h-[36px] group-hover:py-[8px]"
                                    >
                                        <span class="text-[14px] leading-[20px] font-semibold mr-[8px] whitespace-nowrap">Cược Ngay</span>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m10 17l5-5m0 0l-5-5"/></svg>
                                    </x-kit.button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </x-ui.common.swiper>
    </div>
</div>