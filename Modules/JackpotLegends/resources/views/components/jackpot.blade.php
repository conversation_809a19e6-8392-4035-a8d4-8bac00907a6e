@props(['largeBox', 'smallBox', 'largeBoxTooltip', 'smallBoxTooltip'])

@php
    $swiperConfig = [
        'slidesPerView' => 'auto',
        'spaceBetween' => 0,
        'freeMode' => true,
        'breakpoints' => [
            '1200' => [
                'slidesPerView' => 'auto',
                'spaceBetween' => 0,
            ],
        ],
    ];
@endphp

<div class="section-jackpot grid grid-cols-2 place-items-center items-center justify-center gap-2 xl:gap-5 max-sm:mt-[6px]">
    <x-jackpotlegends::jackpot-box type="large" :swiperConfig="$swiperConfig" :value="$largeBox[0] ?? 0" :items="array_slice($largeBox, 1)" :tooltips="$largeBoxTooltip"
        topLabelBg="#FFD6A3"
        topLabelColor="#7A470A" valueBg="linear-gradient(90deg, #B76007 0%, #D27400 100%)" />

    <x-jackpotlegends::jackpot-box type="small" :swiperConfig="$swiperConfig" :value="$smallBox[0] ?? 0" :items="array_slice($smallBox, 1)" :tooltips="$smallBoxTooltip"
        topLabelBg="#A8AFBB"
        topLabelColor="#3D3F4D" valueBg="#475368" />
</div>

<script>
    window.addEventListener('load', function() {

        const animateJackpot = () => {
            const $jackpotValues = $(`.js-number-animation`);
            $jackpotValues.each((index, element) => {
                const $jackpotValue = $(element);
                if ($jackpotValue.length > 0) {
                    const oldValue =
                        parseInt($jackpotValue.text().replace(/,/g, "")) || 0;
                    const newValue = parseInt($jackpotValue.data('target')) || 0;

                    $jackpotValue.toggleClass("hidden", newValue <= 0).toggleClass("flex", newValue >
                    0);

                    if (oldValue !== newValue) {
                        animateCounter(
                            $jackpotValue,
                            oldValue,
                            newValue,
                            5000,
                            $jackpotValue.data('target'),
                            ""
                        );
                    }
                }
            });
        }
        animateJackpot();
    });
</script>
