@props([
    'type' => 'large', // 'large' hoặc 'small'
    'swiperConfig' => [],
    'value' => 0,
    'items' => [],
    'tooltips' => [],
    'topLabelBg' => '#FFD6A3',
    'topLabelColor' => '#7A470A',
    'valueBg' => 'linear-gradient(90deg, #B76007 0%, #D27400 100%)',
])

@php
    $boxClass = $type . '-box-jackpot';
    $boxClassMb = $type . '-box-jackpot-mb';
    $itemClass = $type . '-jackpot-item-';
    $countUpClass = 'count-up-value font-roboto text-nowrap whitespace-nowrap';
@endphp

<div class="jackpot-box-{{ $type }} jackpot-box-card relative col-span-1 w-full h-[127px] sm:max-w-[699px] sm:h-[232px] aspect-[175/127] sm:aspect-[699/232] bg-cover bg-center sm:rounded-xl rounded sm:!bg-[length:100%_100%]">
    <div class="event-jackpot-tooltip absolute max-sm:bottom-[9px] left-[10px] sm:left-auto sm:top-[10px] sm:right-4">
        <div class="tooltip-container relative">
            <img
                class="w-4 h-4 sm:w-6 sm:h-6 cursor-pointer"
                src="{{ Module::asset('jackpotlegends:images/note-icon.avif') }}"
                alt="note-icon"
            >
            <div class="tooltip hidden absolute bg-[#686B74] text-white text-sm rounded-lg px-[10px] py-[8px] break-words w-[266px] sm:w-[343px] bottom-full ml-[114px] sm:-ml-[128px] mb-[6px]">
                <ul class="space-y-1">
                    @foreach ($tooltips as $tooltipItem)
                        <li class="flex items-start text-[10px] sm:text-xs leading-4">
                            <span class="mr-2">•</span>
                            <span>{{ $tooltipItem }}</span>
                        </li>
                    @endforeach
                </ul>
            </div>
            <div class="tooltip-arrow hidden absolute w-0 h-0 border-l-[6px] border-l-transparent border-r-[6px] border-r-transparent border-t-[6px] border-t-[#686B74] bottom-full left-1/2 -translate-x-1/2"></div>
        </div>
    </div>

    <div class="sm:hidden absolute left-[10px] top-[18px] pr-[10px]">
        <div class="{{ $boxClassMb }} text-neutral text-[16px] font-[900] leading-[16px] line-clamp-1 uppercase font-roboto" data-value="{{ $value }}">
            @if ($value > 0)
                <div class="js-jackpot-main-value {{ $countUpClass }}">
                    <span class="js-number-animation !inline tabular-nums" data-target="{{ $value }}">{{ formatAmount(roundDownToNearest($value)) }}</span> VND
                </div>
            @else
                -
            @endif
        </div>
    </div>
    <div class="jackpot-box-content max-[1700px]:min-[1500px]:z-[1]">
        <div class="jackpot-box-content-title">
            <img
                class=""
                src="{{ Module::asset('jackpotlegends:images/' . $type . '-jackpot-text.svg') }}"
                alt="big-icon"
            >
        </div>
        <div class="jackpot-box-top">
            <div class="jackpot-box-top-label">Top {{ $type == 'large' ? '1' : '11' }}</div>
            <div class="jackpot-box-top-value">
                <div class="" data-value="{{ $value }}">
                @if ($value > 0)
                    <div class="js-jackpot-main-value {{ $countUpClass }}">
                        <span class="js-number-animation !inline tabular-nums" data-target="{{ $value }}">{{ formatAmount(roundDownToNearest($value)) }}</span> VND
                    </div>
                @else
                    -
                @endif
            </div>
            </div>
        </div>
    </div>
    <div class="jackpot-box-img">
        <img
            src="{{ Module::asset('jackpotlegends:images/' . $type . '-jackpot-img.avif') }}"
            alt="icon"
        >
    </div>


    <div class="jackpot-box-swiper hidden sm:block absolute bottom-3 left-3 max-w-full pr-3 [&_.swiper-container]:sm:overflow-hidden">
        <x-ui.common.swiper :autoplay="false" :swiperConfig="$swiperConfig" swiperRequiredClass="{{ $type }}-box-swiper" swiperWrapperClass="w-full relative cursor-grab">
            @foreach ($items as $index => $item)
                <div class="swiper-slide pr-2 !w-max {{ $type === 'small' ? 'min-w-[173px] last:pr-7' : 'min-w-[168px] last:pr-2' }}">
                    <div class="flex h-[37px]">
                        <div class="flex items-center justify-center text-[11px] leading-2 font-bold {{ $type === 'small' ? 'w-[52px]' : 'w-[46px]' }}" style="background-color: {{ $topLabelBg }}; color: {{ $topLabelColor }}; min-width: fit-content;border-radius: 6px 0 0 6px;">
                            Top {{ $type == 'large' ? $index + 2 : $index + 12 }}
                        </div>
                        <div
                            class="{{ $itemClass }}{{ $index + 2 }} flex items-center px-2 text-neutral w-fit min-w-[121px] font-roboto h-full text-[12px] font-[900] leading-[16px] line-clamp-1 uppercase rounded-[0_6px_6px_0]"
                            style="background: {{ $valueBg }};"
                            title="{{ formatAmount($item) }} VND"
                        >
                            <div class="{{ $countUpClass }}">
                                <span class="js-number-animation !inline tabular-nums" data-target="{{ $item }}">{{ formatAmount(roundDownToNearest($item)) }}</span> VND
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </x-ui.common.swiper>
    </div>
</div>
