<?php

namespace Modules\JackpotLegends\Services;

use Illuminate\Support\Facades\App;
use App\Services\GatewayApi;
use App\Services\FakerApi;

class JackpotLegendsService
{
    private GatewayApi $gatewayApi;
    private FakerApi $fakerApi;

    public function __construct(GatewayApi $gatewayApi, FakerApi $fakerApi)
    {
        $this->gatewayApi = $gatewayApi;
        $this->fakerApi = $fakerApi;
    }

    public function getJackpotData()
    {
        try {
            $endpoint = 'lixi/list';
            if (App::environment('local')) {
                return $this->fakerApi->getPromotion($endpoint);
            }
            return $this->gatewayApi->getPromotion($endpoint);
        } catch (\Exception $e) {
            return ['list' => []];
        }
    }
}
