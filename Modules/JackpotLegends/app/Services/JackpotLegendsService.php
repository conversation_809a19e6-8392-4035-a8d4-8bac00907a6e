<?php

namespace Modules\JackpotLegends\Services;

use App\Services\GatewayApi;
use App\Services\FakerApi;
use Symfony\Component\HttpFoundation\Response;
use App\Services\AccountService;
use Illuminate\Support\Facades\App;
use Exception;
use Carbon\Carbon;
class JackpotLegendsService
{
    private GatewayApi $gatewayApi;
    private FakerApi $fakerApi;

    public function __construct(GatewayApi $gatewayApi, FakerApi $fakerApi)
    {
        $this->gatewayApi = $gatewayApi;
        $this->fakerApi = $fakerApi;
    }

    public function getRankingList($request, $params = [], $endpoint)
    {
        $response = $this->gatewayApi->getPromotion($endpoint, $params);
        if (isset($response) && isset($response->status) && $response->status === 'OK') {
            return $response->data;
        }
        return [];
    }
}