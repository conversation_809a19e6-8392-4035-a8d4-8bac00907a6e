<?php

namespace Modules\JackpotLegends\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Modules\JackpotLegends\Services\JackpotLegendsService;
use Carbon\Carbon;
use App\Services\GameService;

class JackpotLegendsController extends Controller
{
    private JackpotLegendsService $jackpotLegendsService;
    private GameService $gameService;
    protected $slug;
    public function __construct(JackpotLegendsService $jackpotLegendsService, GameService $gameService)
    {
        $this->jackpotLegendsService = $jackpotLegendsService;
        $this->gameService = $gameService;
        $this->slug = request()->path();
    }

    public function casino(Request $request)
    {
        $slug = $this->slug;
        // startDate is take 4 weeks from the previous date
        $startDate = config('app.jackpot_start_date');
        $isStarted = now()->greaterThanOrEqualTo($startDate);
        // Pick end date of current week
        $endDate = now()->endOfWeek()->format('Y-m-d');

        $start = Carbon::parse($startDate);
        $end = now()->copy();
        

        $now = now()->copy();
        
        if ($now->lt($start)) {
            $weekStartDate = $start->copy()->startOfWeek(Carbon::MONDAY);
            $weekEndDate = $weekStartDate->copy()->endOfWeek(Carbon::SUNDAY);
        } else {
            $weekStartDate = now()->copy()->startOfWeek(Carbon::MONDAY);
            $weekEndDate = $weekStartDate->copy()->endOfWeek(Carbon::SUNDAY);
        }
        
        $params = [
            'startTime' => $weekStartDate->format('Y-m-d'),
            'endTime' => $weekEndDate->format('Y-m-d')
        ];
        // Get date list
        $dateList = [];
        $listWeekStartDate = $start->copy()->startOfWeek(Carbon::MONDAY);
        
        $currentWeekStart = $now->copy()->startOfWeek(Carbon::MONDAY);
        $currentWeekEnd = $currentWeekStart->copy()->endOfWeek(Carbon::SUNDAY);
        $nextWeekStart = $currentWeekStart->copy()->addWeek();
        $nextWeekEnd = $nextWeekStart->copy()->endOfWeek(Carbon::SUNDAY);
        if ($now->lt($start)) {
            $dateList[] = [
                'startDate' => $nextWeekStart->format('Y-m-d'),
                'endDate' => $nextWeekEnd->format('Y-m-d')
            ];
        } else {
            while ($listWeekStartDate->lte($end)) {
                $listWeekEndDate = $listWeekStartDate->copy()->endOfWeek(Carbon::SUNDAY);

                $dateList[] = [
                    'startDate' => $listWeekStartDate->format('Y-m-d'),
                    'endDate' => $listWeekEndDate->format('Y-m-d')
                ];

                $listWeekStartDate->addWeek();
            }
        }
        
        
        $dateList = array_reverse($dateList);
        $endpoint = config('jackpotlegends.casino.endpoint');
        $weeklyUserData = [];
        $currentUserPosition = null;
        $streamGames = $this->gameService->getGameByParams($request, [
            'limit' => 6,
            'partner' => 'vingame,go,b52,rik',
            'sort' => 'partner_game_type',
            'type' => 'baccarat,baucua,sicbo'
        ], '/casino/search/');
        $casinoGames = $streamGames->items ?? [];
        $response = $this->jackpotLegendsService->getRankingList($request, $params, $endpoint);

        if ($response && isset($response->list)) {
            $weeklyUserData = $response->list;
        }

        if ($response && isset($response->position)) {
            $currentUserPosition = $response->position;
        }

        // Get jackpot
        $jackpots = $this->gameService->getJackpot($request);
        $streamGames->items = $this->gameService->mapJackpotToGames($streamGames->items, $jackpots);
        // Get live casino thumbs
        $liveCasinoThumbs = config('games.liveCasinoThumbs');
        $streamGames->items = array_map(function ($item) use ($liveCasinoThumbs) {
            $thumbId = mb_strtolower($item->partner_provider . '_' . $item->partner_game_id);
            if (in_array($thumbId, $liveCasinoThumbs)) {
                $item->image = asset('/asset/images/home/<USER>/' . $thumbId . '.webp');
            }
            return $item;
        }, $streamGames->items);

        return view(
            'jackpotlegends::casino.index',
            compact(
                'weeklyUserData',
                'dateList',
                'casinoGames',
                'isStarted',
                'currentUserPosition',
                'slug'
            )
        );
    }

    public function getRanking(Request $request)
    {
        $startTime = $request->input('startTime');
        $endTime = $request->input('endTime');
        $endpoint = $request->input('eventEndpoint');
        $params = [
            'startTime' => $startTime,
            'endTime' => $endTime
        ];
        $html = '';
        $response = $this->jackpotLegendsService->getRankingList($request, $params, $endpoint);
        if ($response && isset($response->list)) {
            $backgrounds = [
                'top' => [
                    0 => 'top-1-bg.png',
                    1 => 'top-2-bg.png',
                    2 => 'top-3-bg.png'
                ],
                'user' => 'user-bg.png',
                'normal' => 'normal-bg.png'
            ];
            if (count($response->list) > 0) {
                foreach ($response->list as $index => $player) {
                    $html .= view('jackpotlegends::components.ranking-item', [
                        'player' => $player,
                        'index' => $index,
                        'currentUserPosition' => $response->position ?? null,
                        'backgrounds' => $backgrounds
                    ])->render();
                }
            } else {
                $html = view('jackpotlegends::components.empty-ranking')->render();
            }
        }
        return response()->json([
            'data' => $response ?? [],
            'dataHtml' => $html,
            'status' => 'OK'
        ]);
    }
}
