<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class JackpotLegendsServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        $this->mergeConfigFrom(
            __DIR__.'/../config/api.php', 'jackpotlegends.api'
        );
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(Router $router)
    {
        //
    }
}
