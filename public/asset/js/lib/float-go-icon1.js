import "./go-jackpot2.js";

document.addEventListener("DOMContentLoaded", () => {
    const goJackpot = document.querySelector("go-jackpot");
    goJackpot.addEventListener("game-click", (event) => {
        const plainObject = JSON.parse(JSON.stringify(event.detail));
        const payload = plainObject[0];
        if (typeof window.openGame === "function") {
            window.openGame(payload.api_url);
        }
    });
});