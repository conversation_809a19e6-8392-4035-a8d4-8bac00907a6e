<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap"
        rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f5f5f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: white;
        }

        .container {
            padding: 2rem;
            max-width: 1140px;
            display: flex;
            gap: 32px;
        }

        .error-header {
            display: flex;
            align-items: center;
            gap: 24px;
        }

        .flag-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
        }

        .error-message {
            margin-bottom: 1rem;
        }

        .info-section {
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.9rem;
            display: flex;
            justify-content: space-evenly;
            gap: 12px;
        }

        .error-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            max-width: 420px;
        }

        .error-content {
            display: flex;
            flex-direction: column;
            gap: 24px;
            margin-top: 2.5rem;
        }

        .error-content-text {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .error-title {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #27272A;
            margin: 0;
            padding: 0;
        }

        .error-description {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #3D3F4D;
            margin: 0;
            padding: 0;
        }

        .info-item {
            color: #27272A;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;

        }

        .info-item-value {
            font-family: 'Roboto', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 0%;
            color: #27272A;
        }

        @media (max-width: 768px) {

            .info-section,
            .container {
                flex-direction: column;
            }

            .info-section {
                padding-top: 12px;
            }

            .info-item {
                margin: 0;
            }

            .error-content {
                margin-top: 0px;
            }

            .error-header {
                gap: 12px;
            }

            .error-description {
                font-size: 12px;
            }

            .flag-icon {
                width: 32px;
                height: 32px;
                border-radius: 50%;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <img src="/asset/images/errors/403.png" alt="403" class="error-image">
        <div class="error-content">
            <div class="error-header">
                 <img src="/asset/images/flags/uk.png" alt="UK Flag" class="flag-icon">
                <div class="error-content-text">
                    <h2 class="error-title">Access Restricted</h2>
                    <p class="error-description">Sorry, your region is not supported.</p>
                </div>
            </div>

            <div class="error-header">
                 <img src="/asset/images/flags/vn.png" alt="Vietnam Flag" class="flag-icon">
                <div class="error-content-text">
                    <h2 class="error-title">Truy Cập Bị Từ Chối</h2>
                    <p class="error-description">Xin lỗi, khu vực của bạn không nằm trong danh sách hỗ trợ.</p>
                </div>
            </div>

            <div class="info-section">
                <p class="info-item">IP: <span class="info-item-value"><!--# echo var="http_cf_connecting_ip" --></span>
                </p>
                <p class="info-item">RAY ID: <span class="info-item-value"><!--# echo var="http_cf_ray" --></p></span>
                </p>
            </div>
        </div>
    </div>
</body>

</html>