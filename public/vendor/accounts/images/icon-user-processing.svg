<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9888_386463)">
<foreignObject x="1.39557" y="-6.44818" width="21.0487" height="28.8964"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3.3px);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_iii_9888_386463)" data-figma-bg-blur-radius="6.60052">
<path d="M7.99609 13.3327C10.9416 13.3327 13.3294 10.9449 13.3294 7.99935C13.3294 5.05383 10.9416 2.66602 7.99609 2.66602" stroke="#B1B9CB" stroke-width="5.02897"/>
</g>
<foreignObject x="-6.45209" y="-6.45013" width="21.0487" height="28.8964"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3.3px);height:100%;width:100%"></div></foreignObject><g filter="url(#filter1_iii_9888_386463)" data-figma-bg-blur-radius="6.60052">
<path d="M7.99609 2.66537C5.05058 2.66537 2.66276 5.05318 2.66276 7.9987C2.66276 10.9442 5.05058 13.332 7.99609 13.332" stroke="#B1B9CB" stroke-width="5.02897"/>
</g>
</g>
<defs>
<filter id="filter0_iii_9888_386463" x="1.39557" y="-6.44818" width="21.0487" height="28.8964" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.4144"/>
<feGaussianBlur stdDeviation="0.707199"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_9888_386463"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.88586"/>
<feGaussianBlur stdDeviation="0.942932"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_9888_386463" result="effect2_innerShadow_9888_386463"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.471466"/>
<feGaussianBlur stdDeviation="0.235733"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_9888_386463" result="effect3_innerShadow_9888_386463"/>
</filter>
<filter id="filter1_iii_9888_386463" x="-6.45209" y="-6.45013" width="21.0487" height="28.8964" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.4144"/>
<feGaussianBlur stdDeviation="0.707199"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_9888_386463"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.88586"/>
<feGaussianBlur stdDeviation="0.942932"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_9888_386463" result="effect2_innerShadow_9888_386463"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.471466"/>
<feGaussianBlur stdDeviation="0.235733"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_9888_386463" result="effect3_innerShadow_9888_386463"/>
</filter>
<clipPath id="clip0_9888_386463">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
