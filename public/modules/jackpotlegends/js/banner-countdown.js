import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc'

dayjs.extend(utc);

const getCountdown = (endDate) => {
    const now = dayjs.utc().add(7, 'hours');
    const diff = endDate.diff(now, 'seconds');
    if (diff < 0) {
        return {
            day: '00',
            hour: '00',
            minute: '00',
            second: '00'
        };
    }
    const day = Math.floor(diff / (24 * 60 * 60)).toString().padStart(2, '0');
    const hour = Math.floor((diff % (24 * 60 * 60)) / (60 * 60)).toString().padStart(2, '0');
    const minute = Math.floor((diff % (60 * 60)) / 60).toString().padStart(2, '0');
    const second = (diff % 60).toString().padStart(2, '0');

    return {
        day,
        hour,
        minute,
        second
    };
}
let bannerCountdownInterval = null;
document.addEventListener('DOMContentLoaded', () => {
    const endDate = dayjs.utc().day(7).endOf('day');
    bannerCountdownInterval = setInterval(() => {
        const { day, hour, minute, second } = getCountdown(endDate);
        $('.jackpotlegends-js-day').text(`${day} NGÀY`);
        $('.jackpotlegends-js-time').text(`${hour} : ${minute} : ${second}`);
    }, 1000);
});
