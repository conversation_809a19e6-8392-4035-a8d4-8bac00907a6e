@font-face {
    font-family: 'SVN-VT Redzone Classic';
    src: url('../fonts/SVN-VTRedzone-Classic/SVN-VTRedzone-Classic.eot');
    src: url('../fonts/SVN-VTRedzone-Classic/SVN-VTRedzone-Classic.eot?#iefix') format('embedded-opentype'),
        url('../fonts/SVN-VTRedzone-Classic/SVN-VTRedzone-Classic.woff2') format('woff2'),
        url('../fonts/SVN-VTRedzone-Classic/SVN-VTRedzone-Classic.woff') format('woff'),
        url('../fonts/SVN-VTRedzone-Classic/SVN-VTRedzone-Classic.ttf') format('truetype'),
        url('../fonts/SVN-VTRedzone-Classic/SVN-VTRedzone-Classic.svg#SVN-VTRedzone-Classic') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'UTM HelvetIns';
    src: url('../fonts/UTM-HelvetIns/UTMHelvetIns.eot');
    src: url('../fonts/UTM-HelvetIns/UTMHelvetIns.eot?#iefix') format('embedded-opentype'),
        url('../fonts/UTM-HelvetIns/UTMHelvetIns.woff2') format('woff2'),
        url('../fonts/UTM-HelvetIns/UTMHelvetIns.woff') format('woff'),
        url('../fonts/UTM-HelvetIns/UTMHelvetIns.ttf') format('truetype'),
        url('../fonts/UTM-HelvetIns/UTMHelvetIns.svg#UTMHelvetIns') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

.font-utm-helvetins {
    @apply font-['UTM_HelvetIns'];
}

.font-redzone,
.font-svn-vt-redzone-classic {
    @apply font-['SVN-VT_Redzone_Classic'];
}
.tabular-nums {
    font-variant-numeric: tabular-nums;
}

.section-jackpot {
    .jackpot-box-large {
        @apply bg-[url('/public/modules/jackpotlegends/images/large-box-mb.png')] sm:bg-[url('/public/modules/jackpotlegends/images/large-box-bg.avif')];
    }
    .jackpot-box-small {
        @apply bg-[url('/public/modules/jackpotlegends/images/small-box-mb.png')] sm:bg-[url('/public/modules/jackpotlegends/images/small-box-bg.avif')];
    }
    .jackpot-box-card {
        @media screen and (max-width: 640px) {
            background-position: top left;
            background-size: 100% 127px;
            background-repeat: no-repeat;
        }
    }

    .event-jackpot-tooltip {
        .tooltip-container {
            @apply relative inline-block;

            .tooltip {
                @apply hidden absolute text-white text-sm leading-[1.5] rounded-lg px-[10px] py-[10px] bg-[#686B74] lg:ml-[-150px];
            }

            &:hover {
                .tooltip, .tooltip-arrow {
                    @apply block;
                }
            }
        }

        .tooltip {
            @apply bottom-full left-1/2 -translate-x-1/2 z-10;
            &.tooltip-bottom {
                @apply top-[calc(100%+6px)] !bottom-[unset] #{!important};
                + .tooltip-arrow {
                    @apply rotate-180 bottom-[-6px] #{!important};
                }
            }
        }
    }
    .jackpot-box-small {
        .event-jackpot-tooltip {
            .tooltip {
                @apply max-sm:right-0 max-sm:left-[-90px];
                .tooltip-arrow {
                    @apply max-sm:left-[111px];
                }
            }
        }
    }
}


.page-event-casino {
    .ranking-table-header {
        padding-left: 0;
        padding-right: 0;
        background: linear-gradient(261.37deg, #267943 0.18%, #075D2F 99.82%);
        // @apply grid grid-cols-[102px_217px_218px_189px];
        .ranking-table-header-th {
            text-align: center;
            white-space: nowrap;
        }
        @media screen and (max-width: 1199px) {
            background: linear-gradient(89.63deg, #155B3F 1.06%, #287150 99.86%);
        }
    }
    .ranking-content-list:not(.ranking-content-list-empty) {
        @media screen and (max-width: 1199px) {
            left: 0;
            right: 0;
        }
    }
    .ranking-content-list-empty {
        @apply h-[350px] xl:h-[448px] before:hidden;
    }
    .ranking-data-wrapper:has(.current-user-wrapper.hidden) {
        .ranking-content-list,
        .ranking-loading{
            @apply h-[350px] xl:h-[410px] #{!important};
        }
    }
    .ranking-data-wrapper {
        &:has(.table-footer-auth) {
            .ranking-content-list:has(#ranking-empty-data),
            .ranking-loading {
                @apply h-[304px] xl:h-[392px] #{!important};
            }
        }
    }
    .tab-item {
        @apply max-xl:tracking-tight max-xl:w-1/4 max-xl:px-0;

        &.default {
            @apply text-[#AFBBB7];
        }

        &.active {
            background: linear-gradient(180deg, rgba(9, 57, 58, 0.1) 0%, #0D4E0C 100%);
            @apply text-primary-400 border-b-[2px] border-[#057B1E];
        }
    }

    .rank-no-text {
        @apply font-['UTM_HelvetIns'];
        background: linear-gradient(149.37deg, #FFF4CE 28.58%, #FAAF0F 44.32%, #FFE89B 62.53%, #FAAF0F 78.87%);
        @apply bg-clip-text text-transparent;
    }

    .custom-scrollbar {
        @apply max-xl:w-full pr-0 xl:pr-[4px] relative z-[1] max-xl:before:hidden before:h-[1px] before:z-[-1] before:absolute before:bottom-0 before:left-0 before:w-full before:bg-[#2B303D];

        &::-webkit-scrollbar {
            @apply w-[7px] h-[7px] mt-[4px];
        }

        &::-webkit-scrollbar-track {
            @apply bg-neutral-700 rounded-full mb-[4px] xl:mt-[-38px];
        }

        &::-webkit-scrollbar-thumb {
            @apply bg-[#465F59] rounded-full border border-solid border-neutral-700 xl:mt-[-38px];
            background-clip: padding-box;
        }

        &::-webkit-scrollbar-button {
            @apply hidden;
        }
    }
    .user-item {
        @apply relative z-[1] bg-[#062C21];
        @media (min-width: 1200px) {
            &::before {
                @apply absolute inset-0 rounded p-[1px] z-[2];
                content: '';
                background-image: linear-gradient(90deg, rgba(5, 136, 141, 0.2) 0%, rgba(5, 136, 141, 0) 100%);
                -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
                mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
                -webkit-mask-composite: xor;
                mask-composite: exclude;
                user-select: none;
                pointer-events: none;
            }
        }
        &::after {
            @apply content-[''] z-[-1] absolute left-0 top-0 h-full w-[48px] rounded-l-[4px] xl:w-[102px] bg-[url('/public/modules/jackpotlegends/images/pre-bg-normal.png')] max-xl:bg-[url('/public/modules/jackpotlegends/images/pre-bg-normal-mb.avif')] bg-no-repeat bg-right bg-cover;
        }
        &:nth-child(-n+3) {
            .rank-no-text {
                @apply hidden;
            }
        }

        &.user-item-current{
            @apply bg-[#054331];
            &::after {
                @apply bg-[url('/public/modules/jackpotlegends/images/pre-bg-your.webp')] max-xl:bg-[url('/public/modules/jackpotlegends/images/pre-bg-your-mb.avif')];
            }
        }

        &:nth-child(1) {
            background: linear-gradient(90deg, #062C21 56.08%, #644A00 100%);

            &::before {
                background-image: linear-gradient(90deg, rgba(250, 167, 15, 0.2) 0%, rgba(250, 167, 15, 0) 100%);
            }
            &::after {
                @apply bg-[url('/public/modules/jackpotlegends/images/pre-bg-first.webp')] max-xl:bg-[url('/public/modules/jackpotlegends/images/pre-bg-first-mb.png')];
            }
            .user-item__number{
                @apply bg-[url('/public/modules/jackpotlegends/images/top-1.png')] bg-contain bg-center;
            }
            &.user-item-current {
                background: linear-gradient(90deg, #054331 56.08%, #644A00 100%);
            }
        }

        &:nth-child(2) {
            background: linear-gradient(90deg, #062C21 56.08%, #08688F 100%);

            &::before {
                background-image: linear-gradient(90deg, rgba(47, 103, 150, 0.2) 0%, rgba(47, 103, 150, 0) 100%);
            }
            &::after {
                @apply bg-[url('/public/modules/jackpotlegends/images/pre-bg-second.webp')] max-xl:bg-[url('/public/modules/jackpotlegends/images/pre-bg-second-mb.png')];
            }
            .user-item__number{
                @apply bg-[url('/public/modules/jackpotlegends/images/top-2.png')] bg-contain bg-center;
            }
            &.user-item-current {
                background: linear-gradient(90deg, #054331 56.08%, #08688F 100%);
            }
        }

        &:nth-child(3) {
            background: linear-gradient(90deg, #062C21 56.08%, #5C3B2D 100%);
            &::before {
                background-image: linear-gradient(90deg, rgba(151, 69, 35, 0.2) 0%, rgba(151, 69, 35, 0) 100%);
            }
            &::after {
                @apply bg-[url('/public/modules/jackpotlegends/images/pre-bg-third.webp')] max-xl:bg-[url('/public/modules/jackpotlegends/images/pre-bg-third-mb.png')];
            }
            .user-item__number{
                @apply bg-[url('/public/modules/jackpotlegends/images/top-3.png')] bg-contain bg-center;
            }

            &.user-item-current {
                background: linear-gradient(90deg, #054331 56.08%, #5C3B2D 100%);
            }
        }
    }

    .vip-privilege-page .vip-rank-note {
        &::before {
            @apply absolute inset-0 rounded p-[1px] z-[2];
            content: '';
            background-image: linear-gradient(90deg, rgba(250, 167, 15, 0.2) 0%, rgba(250, 167, 15, 0) 100%);
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
        }
    }

    .border-item {
        background: linear-gradient(180deg, rgba(31, 44, 39, 0) 0%, #245539 50%, rgba(31, 44, 39, 0) 100%);
        @apply w-px;
    }

    .events-rules-terms-mobile {
        .tab-pane {
            @apply hidden;

            &.active {
                @apply block;
            }
        }

        .tab-btn {
            @apply bg-[url('/public/modules/jackpotlegends/images/btn-tab.jpg')] bg-cover bg-center rounded h-[30px] w-1/2 py-2 text-center text-xs font-bold uppercase leading-[16px] text-neutral;
            &.active {
                @apply text-white bg-[url('/public/modules/jackpotlegends/images/btn-tab-active.jpg')];
            }

            &:not(.active) {
                @apply text-[#AFBBB7];
            }
        }
    }
    .rules-terms-item {
        @apply relative pl-[16px];
        &:before {
            @apply absolute left-1 top-0 content-['•'];
        }
    }

    .terms-content-mobile {
        @apply bg-[url('/public/modules/jackpotlegends/images/terms-content-mb.jpg')] bg-cover bg-center;
    }

    .current-user {
        .user-top{
            @apply size-[32px] xl:size-[38px];
        }
        .user-top-1{
            @apply bg-[url('/public/modules/jackpotlegends/images/top-1.png')] bg-contain bg-center;
        }
        .user-top-2{
            @apply bg-[url('/public/modules/jackpotlegends/images/top-2.png')] bg-contain bg-center;
        }
        .user-top-3{
            @apply bg-[url('/public/modules/jackpotlegends/images/top-3.png')] bg-contain bg-center;
        }
    }
}

.event-invest-legend-popup {

    .jackpot-amount-text-large,
    .vip-privilege-page {

        .vip-amount-text,
        .vip-level-note,
        .vip-popup-amount-text,
        .vip-privilege-level-text.level-0,
        .vip-privilege-level-text.level-1,
        .vip-privilege-level-text.level-2,
        .vip-privilege-level-text.level-3,
        .vip-privilege-level-text.level-4,
        .vip-privilege-level-text.level-5,
        .vip-privilege-level-text.level-6,
        .vip-privilege-level-text.level-7,
        .vip-privilege-level-text.level-8 {
            @apply bg-clip-text text-transparent;
        }

        .vip-progress-bg {
            background-image: linear-gradient(to right, #602f06, #ac6629);
        }

        .vip-ranking-tooltip {
            .tooltip-container:hover .tooltip {
                @apply block;
            }

            .tooltip {
                @apply bottom-full right-[-1300%] z-10 md:left-[-900%] md:bottom-[110%];
            }
        }

        .vip-item-reward-blur {
            @apply z-[2] opacity-60 border border-solid rounded;
            background-image: linear-gradient(to bottom, rgba(74, 52, 52, 0.74), #2b2525);
            border-image: linear-gradient(153.66deg, rgba(184, 106, 62, 0.3) 18.27%, rgba(184, 106, 62, 0.3) 86.14%) 1;
        }

        .vip-item-reward-blur-mb {
            @apply z-[2] overflow-hidden rounded-[8px] border-[1.5px] border-[#845858];
        }

        .vip-rank-note {
            @apply relative rounded-[8px];
            background-image: linear-gradient(271.4deg, #191919 -3.77%, #1d1d1d 93.47%),
                radial-gradient(19.6% 46.86% at -6.23% 37.9%, rgba(227, 162, 65, 0.4) 0%, rgba(227, 162, 65, 0) 100%);

            @media (max-width: 1199px) {
                @apply rounded-[4px];
            }
        }

        .vip-rank-note-bg {
            @apply relative rounded-[8px];
            background-image: radial-gradient(19.6% 46.86% at -6.23% 37.9%, rgba(227, 162, 65, 0.4) 0%, rgba(227, 162, 65, 0) 100%);

            @media (max-width: 1199px) {
                @apply rounded-[4px];
            }
        }
    }
}

.events-casino-swiper {
    @apply overflow-hidden max-xl:px-4;

    &.swiper-horizontal {
        .swiper-wrapper {
            @apply gap-0;
        }
    }
    .swiper-wrapper {
        @apply gap-2 xl:gap-5;
    }

    @media (max-width: 1199px) {
        @apply overflow-visible;
    }
}

.__dot {
    @apply relative pl-4 xl:pl-[20px];

    &:before {
        @apply absolute content-['•'] top-[-1px] left-[4px] xl:left-[8px];
    }
}


.jackpot-box-card {
    position: relative;
    .jackpot-box-content {
        @apply hidden sm:block;
        position: absolute;
        left: 32px;
        right: 32px;
        bottom: 86px;
        @media screen and (max-width: 1500px) {
            left: 24px;
            right: 24px;
        }
    }
    .jackpot-box-content-title {
        margin-bottom: 24px;
        img {
            height: 40px;
        }
    }
    .jackpot-box-img {
        position: absolute;
        right: 0px;
        top: 0;
        z-index: 0;
        width: 53%;
        max-width: 248px;
        pointer-events: none;
        @apply hidden sm:block;
        @media screen and (max-width: 1500px) {
            max-width: 200px;
            width: 44%;
            top: 25%;
            right: 0;
        }
    }
    .jackpot-box-swiper {
        padding: 12px;
        background: linear-gradient(90deg, #F7AA4D 0%, #DD9037 100%);
        left: 0;
        bottom: 0;
        width: 100%;
        border-radius: 0 0 11px 11px;
    }
    .jackpot-box-top {
        @apply flex items-center;
        background: linear-gradient(90deg, #93450A 0%, #A64403 50%, rgba(166, 68, 3, 0.7) 100%);
        border-radius: 8px;
        overflow: hidden;
        .jackpot-box-top-label {
            font-family: Inter;
            font-weight: 600;
            font-size: 16px;
            line-height: 20px;
            letter-spacing: 0%;
            text-align: center;
            text-transform: capitalize;
            padding: 13px 16px;
            white-space: nowrap;
            @media screen and (max-width: 1500px) {
                font-size: 14px;
                padding: 13px 12px;
            }
        }
        .jackpot-box-top-value {
            font-family: Inter;
            font-weight: 900;
            font-size: 26px;
            line-height: 26px;
            letter-spacing: 0%;
            vertical-align: middle;
            text-transform: uppercase;
            padding: 10px 16px;
            position: relative;
            z-index: 2;
            @media screen and (max-width: 1500px) {
                font-size: 20px;
                padding: 10px 12px;
            }
        }
    }
}

.jackpot-box-large {
    .jackpot-box-top {
        background: linear-gradient(90deg, #93450A 0%, #A64403 50%, rgba(166, 68, 3, 0.7) 100%);
        .jackpot-box-top-label {
            background: linear-gradient(114.9deg, #FF9A41 -17.58%, #C65C00 106.89%);
        }
    }
}

.jackpot-box-small  {
    .jackpot-box-top {
        background: linear-gradient(90deg, #29313D 0%, #4D5460 100%);
        .jackpot-box-top-label {
            background: linear-gradient(113.24deg, #898F9C 6.5%, #636A74 110.45%);
        }
    }
    .jackpot-box-swiper {
        background: linear-gradient(90deg, #6C7A8F 0%, #737D8F 100%);
    }
}
