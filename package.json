{"name": "fe", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"autoprefixer": "^10.4.20", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "vite": "^5.0", "vite-plugin-chunk-split": "0.5.0", "vite-plugin-compression": "0.5.1", "vite-plugin-minify": "2.1.0"}, "dependencies": {"@alpinejs/anchor": "^3.14.1", "@alpinejs/collapse": "^3.14.1", "alpinejs": "^3.14.3", "dayjs": "^1.11.13", "jquery": "^3.7.1", "jquery-validation": "^1.21.0", "sass": "^1.81.0", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "sweetalert2": "^11.14.5", "swiper": "^11.1.15", "vite-plugin-compress": "^2.1.1"}, "lint-staged": {"*.php": ["./vendor/bin/pint", "git add"]}}