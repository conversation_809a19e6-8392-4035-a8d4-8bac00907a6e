# Stage 1: Build Phase with Node.js 20 for compiling assets
FROM public.ecr.aws/docker/library/node:20 AS build

WORKDIR /var/www/app

COPY package*.json yarn.lock ./
RUN yarn install

COPY . .
RUN yarn build

# Stage 2: PHP 8.3 FPM with Lara<PERSON> setup
FROM public.ecr.aws/docker/library/php:8.3-fpm

# Add wait-for-it script
COPY wait-for-it.sh /usr/bin/wait-for-it
RUN chmod +x /usr/bin/wait-for-it

# Install system dependencies and PHP extensions
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libzip-dev \
    libonig-dev \
    unzip \
    git \
    curl \
    libssl-dev \
    pkg-config \
    zlib1g-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install \
        pdo_mysql \
        mbstring \
        bcmath \
        zip \
        gd \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Redis extension
RUN pecl install redis \
    && docker-php-ext-enable redis
# Set working directory
WORKDIR /var/www/app

# Copy custom PHP configs
COPY php.ini /usr/local/etc/php/conf.d/
COPY www.conf /usr/local/etc/php-fpm.d/www.conf

# Copy Laravel app from build stage
COPY --from=build /var/www/app /var/www/app
COPY .env /var/www/app/

# Install Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install Elastic APM PHP agent
ENV APM_VERSION=1.15.1
RUN curl -LO https://github.com/elastic/apm-agent-php/releases/download/v${APM_VERSION}/apm-agent-php_${APM_VERSION}_amd64.deb \
    && dpkg -i apm-agent-php_${APM_VERSION}_amd64.deb \
    && rm apm-agent-php_${APM_VERSION}_amd64.deb

# Composer install with optimization
ENV COMPOSER_ALLOW_SUPERUSER=1
ENV COMPOSER_HOME=/composer
RUN composer install --no-interaction --prefer-dist --optimize-autoloader

# Set permissions
RUN chown -R www-data:www-data /var/www/app \
    && chmod -R 775 /var/www/app/storage \
    && chmod -R 775 /var/www/app/public

# Add custom PHP error log path
RUN echo "error_log = /proc/self/fd/2" > /usr/local/etc/php/conf.d/docker-php-ext-error-log.ini

# Copy entrypoint script to handle Artisan at runtime
COPY docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# RUN php artisan app:generate-robots

USER www-data

# Use entrypoint for Laravel tasks (like migrate)
ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["php-fpm"]