<?php
return [
    'sportList' => [
        [
            'id' => 2,
            'title' => 'K - SPORTS',
            'tag' => 'k',
            'imgSrc' => '/asset/images/sports/k-sports.png',
            'imgBackground' => '/asset/images/sports/k-sport-bg.png',
            'imgModel' => '/asset/images/sports/k-sport-model.png',
            'providerLogo' => '/asset/images/sports/provider-k-sport.png',
            'description'=> 'Chinh phục mọi giới hạn',
            'imgSrcMobile' => '/asset/images/sports/k-sports-mb.png',
            'to' => '/ca-cuoc-the-thao/ksports',
            'apiUrl' => '/tp/ksportUrl?loginPath=ksports&login=true',
            'loginRequired' => false,
            'isSport' => true,
        ],
        [
            'id' => 4,
            'title'=> 'BTI - SPORTS',
            'tag' => 's',
            'imgSrc' => '/asset/images/sports/bti-sports.png',
            'imgBackground' => '/asset/images/sports/bti-sport-bg.png',
            'imgModel' => '/asset/images/sports/bti-sport-model.png',
            'providerLogo' => '/asset/images/sports/provider-bti-sport.png',
            'description'=> 'Tỉ lệ cược cực cao',
            'imgSrcMobile' => '/asset/images/sports/bti-sports-mb.png',
            'to' => '/ca-cuoc-the-thao/bti-sports',
            'apiUrl' => '/tp/ssportUrl?loginPath=ssports?login=true',
            'loginRequired' => false,
            'isSport' => true,
        ],
        [
            'id' => 1,
            'title'=> 'SABA - SPORTS',
            'tag' => 'a',
            'imgSrc' => '/asset/images/sports/saba-sports.png',
            'imgBackground' => '/asset/images/sports/saba-sport-bg.png',
            'imgModel' => '/asset/images/sports/saba-sport-model.png',
            'providerLogo' => '/asset/images/sports/provider-saba-sport.png',
            'description'=> 'Thể thao cuồng nhiệt',
            'imgSrcMobile' => '/asset/images/sports/saba-sports-mb.png',
            'to' => '/ca-cuoc-the-thao/saba-sports',
            'apiUrl' => '/athena/sportUrl?loginPath=asports?login=true',
            'loginRequired' => false,
            'isSport' => true,
        ],
        [
            'id' => 5,
            'title'=> 'IM - Sports',
            'tag' => 'saba',
            'imgSrc' => '/asset/images/sports/im-sports.png',
            'imgBackground' => '/asset/images/sports/im-sport-bg.png',
            'imgModel' => '/asset/images/sports/im-sport-model.png',
            'providerLogo' => '/asset/images/sports/provider-im-sport.png',
            'description'=> 'Sống trọn từng giây',
            'imgSrcMobile' => '/asset/images/sports/im-sports-mb.png',
            'to' => '/ca-cuoc-the-thao/im-sports',
            'apiUrl' => '/gameUrl?p=im&gId=sport',
            'loginRequired' => false,
            'isSport' => true,
        ],
    ],

    'virtualSportList' => [
        [
            'id' => 5,
            'title'=> 'K - SPORTS',
            'tag' => 'virtual k-sports',
            'imgSrc' => '/asset/images/sports/virtual-k-sports.png',
            'imgBackground' => '/asset/images/sports/k-sport-bg.png',
            'imgModel' => '/asset/images/sports/virtual-k-sports-model.png',
            'providerLogo' => '/asset/images/sports/provider-k-sport.png',
            'description'=> 'Cược thông minh, rinh quà bự',
            'imgSrcMobile' => '/asset/images/sports/virtual-k-sports-mb.png',
            'to' => '/ca-cuoc-the-thao/virtual-k-sports',
            'apiUrl' => '/tp/ksportUrl?loginPath=ksports&login=true&leagueId&matchId=&sportType=1_8',
            'loginRequired' => false,
            'isSport' => true,
        ],
        [
            'id' => 4,
            'title'=> 'SABA - SPORTS',
            'tag' => 'virtual betradar sports',
            'imgSrc' => '/asset/images/sports/virtual-saba-sports.png',
            'imgBackground' => '/asset/images/sports/saba-sport-bg.png',
            'imgModel' => '/asset/images/sports/virtual-saba-sports-model.png',
            'providerLogo' => '/asset/images/sports/provider-saba-sport.png',
            'description'=> 'Vui cùng thể thao ảo',
            'imgSrcMobile' => '/asset/images/sports/virtual-saba-sports-mb.png',
            'to' => '/ca-cuoc-the-thao/virtual-saba-sports',
            'apiUrl' => '/athena/virtualSportUrl?loginPath=saba-sports?login=true',
            'loginRequired' => false,
            'isSport' => true,
        ],
        [
            'id' => 1,
            'title'=> 'PP - SPORTS',
            'tag' => 'virtual pp sports',
            'imgSrc' => '/asset/images/sports/virtual-pp-sports.png',
            'imgBackground' => '/asset/images/sports/pp-sport-bg.png',
            'imgModel' => '/asset/images/sports/virtual-pp-sports-model.png',
            'providerLogo' => '/asset/images/sports/provider-pp-sport.png',
            'description'=> 'Bứt phá bản thân',
            'imgSrcMobile' => '/asset/images/sports/virtual-pp-sports-mb.png',
            'to' => '/ca-cuoc-the-thao/virtual-pp-sports',
            'apiUrl' => '/gameUrl?p=pragmatic&gId=vpfh3',
            'loginRequired' => false,
            'isSport' => true,
        ],
        [
            'id' => 2,
            'title'=> 'IM - Play',
            'tag' => 'virtual im-sport',
            'imgSrc' => '/asset/images/sports/virtual-im-sports.png',
            'imgBackground' => '/asset/images/sports/im-sport-bg.png',
            'imgModel' => '/asset/images/sports/virtual-im-sports-model.png',
            'providerLogo' => '/asset/images/sports/provider-im-sport.png',
            'description'=> 'Tận hưởng từng khoảnh khắc',
            'imgSrcMobile' => '/asset/images/sports/virtual-im-sports-mb.png',
            'to' => '/ca-cuoc-the-thao/virtual-im-sports',
            'apiUrl' => '/gameUrl?p=im&gId=virtualsport',
            'loginRequired' => false,
            'isSport' => true,
        ],
    ],
];
