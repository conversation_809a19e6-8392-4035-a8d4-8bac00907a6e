<?php

return [
    'headerMenu' => [
        [
            'name' => 'sports',
            'title' => 'Thể thao',
            'url' => '/ca-cuoc-the-thao',
            'urlRegex' =>
                '/^\/ca-cuoc-the-thao(\/(ksports|bti-sports|saba-sports|im-sports|virtual-im-sports|virtual-k-sports|virtual-saba-sports|virtual-pp-sports))?$/',
            'icon' => 'sports'
        ],
        // [
        //     'name' => 'virtual-sports',
        //     'title' => 'Thể thao ảo',
        //     'url' => '/esports',
        //     'urlRegex' =>
        //         '/^\/esports?$/',
        //     'icon' => 'virtual-sports'
        // ],
        [
            'title' => 'Sòng Bài',
            'url' => '/live-casino',
            'urlRegex' => '/^\/live-casino.*$/',
            'label' => 'live',
            'icon' => 'casino'
        ]
    ],
    'siteMenu' => [
        // [
        //     'title' => 'E-Sports',
        //     'url' => '',
        //     'urlRegex' => '/^\/esports.*/',
        //     'icon' => 'esports',
        //     'isButton' => true,
        //     'action' => 'handleClick("/esports")',
        // ],
        [
            'items' => [
                [
                    'title' => 'Nổ Hũ',
                    'url' => '/cong-game/no-hu',
                    'label' => '',
                    'urlRegex' => '/^\/*cong-game\/no-hu.*/',
                    'icon' => 'nohu'
                ],
                [
                    'title' => 'Quay Số',
                    'url' => '',
                    'urlRegex' => '/^\/*quayso.*/',
                    'label' => 'hot',
                    'isButton' => true,
                    'icon' => 'lottery',
                    'submenuId' => 'submenu-lottery',
                    'submenu' => [
                        [
                            'title' => 'Atom',
                            'action' => 'handleClick("/quayso/atom")',
                            'urlRegex' => '/^\/*quayso\/atom.*/',
                            'icon' => 'atom',
                        ],
                        [
                            'title' => 'Quay Số 1',
                            'action' => 'handleClick("/quayso/quayso1")',
                            'urlRegex' => '/^\/*quayso\/quayso1.*/',
                            'icon' => 'lottery-1',
                        ],
                        [
                            'title' => 'Quay Số 2',
                            'action' => 'handleClick("/quayso/quayso2")',
                            'urlRegex' => '/^\/*quayso\/quayso2.*/',
                            'icon' => 'lottery-2',
                        ],
                        [
                            'title' => 'Quay Số 5',
                            'action' => 'handleClick("/quayso/quayso5")',
                            'urlRegex' => '/^\/*quayso\/quayso5.*/',
                            'icon' => 'lottery-5',
                        ],
                        [
                            'title' => 'Numbers Game 1',
                            'action' => 'handleClick("/quayso/numbergame1")',
                            'urlRegex' => '/^\/*quayso\/numbergame1.*/',
                            'icon' => 'lottery',
                        ],
                        [
                            'title' => 'Numbers Game 2',
                            'action' => 'handleClick("/quayso/numbergame2")',
                            'urlRegex' => '/^\/*quayso\/numbergame2.*/',
                            'icon' => 'number-game-2',
                        ],
                    ],
                ],
                [
                    'title' => 'Game Bài',
                    'url' => '/cong-game/game-bai',
                    'urlRegex' => '/^\/*cong-game\/game-bai.*/',
                    'icon' => 'gambling',
                    'label' => 'hot',
                ],
                [
                    'title' => 'Lô đề',
                    'url' => '',
                    'urlRegex' => '/^\/*(lode|lo-de|mega645|power655|sieu-toc-md5).*/',
                    'isButton' => true,
                    'icon' => 'lode',
                    'submenuId' => 'submenu-lode',
                    'submenu' => [
                        [
                            'title' => 'Lô đề 3 miền',
                            'action' => 'handleClick("/lode-3-mien")',
                            'urlRegex' => '/^\/*lode-3-mien.*/',
                            'icon' => 'lode3mien',
                        ],
                        [
                            'title' => 'Lô đề siêu tốc',
                            'action' => 'handleClick("/lo-de-sieu-toc")',
                            'urlRegex' => '/^\/?lo-de-sieu-toc*/',
                            'icon' => 'lodesieutoc',
                        ],
                        [
                            'title' => 'Mega645',
                            'action' => 'handleClick("/mega645")',
                            'urlRegex' => '/^\/*mega645.*/',
                            'icon' => 'mega645',
                        ],
                        [
                            'title' => 'Power655',
                            'action' => 'handleClick("/power655")',
                            'urlRegex' => '/^\/*power655.*/',
                            'icon' => 'power655',
                        ],
                        [
                            'title' => 'Siêu tốc MD5',
                            'action' => 'handleClick("/sieu-toc-md5")',
                            'urlRegex' => '/^\/*sieu-toc-md5*/',
                            'icon' => 'lodesieutoc-md5',
                        ],
                    ],
                ],
                [
                    'title' => 'Keno',
                    'url' => '/cong-game/keno',
                    'urlRegex' => '/^\/*cong-game\/keno.*/',
                    'icon' => 'keno',
                ],
                [
                    'title' => 'Đá Gà',
                    'url' => '',
                    'urlRegex' => '/^\/*cockfight.*/',
                    'isButton' => true,
                    'icon' => 'cockfight',
                    'submenuId' => 'submenu-cockfight',
                    'submenu' => [
                        [
                            'title' => 'Đá Gà GA28',
                            'url' => '',
                            'urlRegex' => '/^\/*cockfight\/ga28.*/',
                            'icon' => 'ga28',
                            'isRequiredLogin' => true,
                            'action' => 'handleClick("/cockfight/ga28")',
                        ],
                        [
                            'title' => 'Đá Gà WS168',
                            'url' => '',
                            'urlRegex' => '/^\/*cockfight\/ws168.*/',
                            'icon' => 'ws168',
                            'isRequiredLogin' => true,
                            'action' => 'handleClick("/cockfight/ws168")',
                        ],
                    ],
                ],
                [
                    'title' => 'Bắn Cá',
                    'url' => '/cong-game/ban-ca',
                    'urlRegex' => '/^\/*cong-game\/ban-ca.*/',
                    'icon' => 'fishing',
                ],
                [
                    'title' => 'Cổng Game',
                    'url' => '/cong-game',
                    'urlRegex' => '/^\/*cong-game(?!\/keno|\/ban-ca|\/cockfight|\/no-hu|\/game-bai)[\/{1}a-zA-Z-]*$/',
                    'icon' => 'lobby-game'
                ],
            ]
        ],
        [
            'title' => 'Trợ Giúp',
            'url' => '/ve-chung-toi',
            'urlRegex' => '/^\/tro-giup.*/',
            'isButton' => true,
            'label' => '',
            'icon' => 'support-1',
        ]
    ],
    'siteMenuMobile' => [
        [
            'items' => [
                [
                    'title' => 'Thể thao',
                    'url' => '/ca-cuoc-the-thao',
                    'urlRegex' => '/^\/ca-cuoc-the-thao.*/',
                    'isButton' => true,
                    'icon' => 'sports',
                    'submenu' => [
                        [
                            'title' => 'K - Sports',
                            'url' => '',
                            'urlRegex' => '',
                            'icon' => 'ksports',
                            'action' => 'onClickSport({apiUrl: "/tp/ksportUrl?loginPath=ksports&login=true", loginRequired: false, isSport: true} )'
                        ],
                        [
                            'title' => 'Saba - Sports',
                            'url' => '',
                            'urlRegex' => '',
                            'icon' => 'saba',
                            'action' => 'onClickSport({apiUrl: "/athena/sportUrl?loginPath=asports?login=true", loginRequired: false, isSport: true})'
                        ],
                        [
                            'title' => 'BTI - Sports',
                            'url' => '',
                            'urlRegex' => '',
                            'icon' => 'bti',
                            'action' => 'onClickSport({apiUrl: "/tp/ssportUrl?loginPath=ssports?login=true", loginRequired: false, isSport: true})'
                        ],
                        [
                            'title' => 'IM - Sports',
                            'url' => '',
                            'urlRegex' => '',
                            'icon' => 'im',
                            'action' => 'onClickSport({apiUrl: "/gameUrl?p=im&gId=sport", loginRequired: true, isSport: true})'
                        ],
                    ],
                ],
            ]
        ],
        [
            'items' => [
                [
                    'title' => 'Sòng bài',
                    'url' => '/live-casino',
                    'urlRegex' => '/^\/*live-casino.*/',
                    'icon' => 'casino',
                    'label' => 'live',
                ],
                [
                    'title' => 'Nổ Hũ',
                    'url' => '/cong-game/no-hu',
                    'label' => '',
                    'urlRegex' => '/^\/*cong-game\/no-hu.*/',
                    'icon' => 'nohu'
                ],
                [
                    'title' => 'Quay Số',
                    'url' => '/quayso',
                    'urlRegex' => '/^\/*quayso.*/',
                    'label' => 'hot',
                    'isButton' => true,
                    'icon' => 'lottery',
                    'submenuId' => 'submenu-lottery',
                    'submenu' => [
                        [
                            'title' => 'Atom',
                            'url' => '',
                            'urlRegex' => '/^\/*quayso\/atom.*/',
                            'icon' => 'atom',
                            'action' => 'onClickSport({apiUrl: "/gameUrl?p=vingame&gId=atom", needCheckName: true})'
                        ],
                        [
                            'title' => 'Quay Số 1',
                            'url' => '',
                            'urlRegex' => '/^\/*quayso\/quayso1.*/',
                            'icon' => 'lottery-1',
                            'action' => 'onClickSport({apiUrl: "/gameUrl?p=vingame&gId=quayso", needCheckName: true})'
                        ],
                        [
                            'title' => 'Quay Số 2',
                            'url' => '',
                            'urlRegex' => '/^\/*quayso\/quayso2.*/',
                            'icon' => 'lottery-2',
                            'action' => 'onClickSport({apiUrl: "/gameUrl?p=vingame&gId=quayso2", needCheckName: true})'
                        ],
                        [
                            'title' => 'Quay Số 5',
                            'url' => '',
                            'urlRegex' => '/^\/*quayso\/quayso5.*/',
                            'icon' => 'lottery-5',
                            'action' => 'onClickSport({apiUrl: "/gameUrl?p=vingame&gId=quayso5", needCheckName: true})'
                        ],
                        [
                            'title' => 'Numbers Game 1',
                            'url' => '',
                            'urlRegex' => '/^\/*quayso\/numbergame1.*/',
                            'icon' => 'lottery',
                            'action' => 'onClickSport({apiUrl: "/tp/numberGameUrl", needCheckName: true})'
                        ],
                        [
                            'title' => 'Numbers Game 2',
                            'url' => '',
                            'urlRegex' => '/^\/*quayso\/numbergame2.*/',
                            'icon' => 'number-game-2',
                            'action' => 'onClickSport({apiUrl: "/tp/numberGame2Url", needCheckName: true})'
                        ],
                    ],
                ],
                [
                    'title' => 'Game Bài',
                    'url' => '/cong-game/game-bai',
                    'urlRegex' => '/^\/*cong-game\/game-bai.*/',
                    'icon' => 'gambling',
                    'label' => 'hot',
                ],
                [
                    'title' => 'Lô đề',
                    'url' => '/lode',
                    'urlRegex' => '/^\/*(lode|lo-de|mega645|power655|sieu-toc-md5).*/',
                    'isButton' => true,
                    'icon' => 'lode',
                    'submenuId' => 'submenu-lode',
                    'submenu' => [
                        [
                            'title' => 'Lô đề 3 miền',
                            'url' => '',
                            'urlRegex' => '/^\/*lode-3-mien.*/',
                            'icon' => 'lode3mien',
                            'action' => 'onClickSport({apiUrl: "/gameUrl?p=vingame&gId=lode2", needCheckName: true})'
                        ],
                        [
                            'title' => 'Lô đề siêu tốc',
                            'url' => '',
                            'urlRegex' => '/^\/*lo-de-sieu-toc.*/',
                            'icon' => 'lodesieutoc',
                            'action' => 'onClickSport({apiUrl: "/lodeVirtualUrl", needCheckName: true})'
                        ],
                        [
                            'title' => 'Mega645',
                            'action' => 'handleClick("/mega645")',
                            'urlRegex' => '/^\/*mega645.*/',
                            'icon' => 'mega645',
                        ],
                        [
                            'title' => 'Power655',
                            'action' => 'handleClick("/power655")',
                            'urlRegex' => '/^\/*power655.*/',
                            'icon' => 'power655',
                        ],
                        [
                            'title' => 'Siêu tốc MD5',
                            'action' => 'handleClick("/sieu-toc-md5")',
                            'urlRegex' => '/^\/*sieu-toc-md5*/',
                            'icon' => 'lodesieutoc-md5',
                        ],
                    ],
                ],
                [
                    'title' => 'Keno',
                    'url' => '/cong-game/keno',
                    'urlRegex' => '/^\/*cong-game\/keno.*/',
                    'icon' => 'keno',
                ],
                [
                    'title' => 'Bắn Cá',
                    'url' => '/cong-game/ban-ca',
                    'urlRegex' => '/^\/*cong-game\/ban-ca.*/',
                    'icon' => 'fishing',
                ],
                [
                    'title' => 'Đá Gà',
                    'url' => '/cockfight',
                    'urlRegex' => '/^\/*cockfight.*/',
                    'isButton' => true,
                    'icon' => 'cockfight',
                    'submenuId' => 'submenu-cockfight',
                    'submenu' => [
                        [
                            'title' => 'Đá Gà GA28',
                            'url' => '',
                            'urlRegex' => '/^\/*cockfight\/ga28.*/',
                            'icon' => 'ga28',
                            'action' => 'onClickSport({apiUrl: "/gameUrl?p=ga28&gId=lobby", needCheckName: true})'
                        ],
                        [
                            'title' => 'Đá Gà WS168',
                            'url' => '',
                            'urlRegex' => '/^\/*cockfight\/ws168.*/',
                            'icon' => 'ws168',
                            'action' => 'onClickSport({apiUrl: "/gameUrl?p=WS168&gId=lobby", needCheckName: true})'
                        ],
                    ],
                ],

                [
                    'title' => 'Cổng Game',
                    'url' => '/cong-game',
                    'urlRegex' => '/^\/*cong-game(?!\/keno|\/ban-ca|\/cockfight|\/no-hu|\/game-bai)[\/{1}a-zA-Z-]*$/',
                    'icon' => 'lobby-game'
                ],
            ]
        ],
        [
            'items' => [
                [
                    'title' => 'Khuyến mãi',
                    'url' => '/khuyen-mai?tab=promotions',
                    'urlRegex' => '/^\/khuyen-mai.*/',
                    'isButton' => true,
                    'label' => '',
                    'icon' => 'promotion',
                ],
                [
                    'title' => 'Tin tức',
                    'url' => '/tin-tuc',
                    'urlRegex' => '/^\/tin-tuc.*/',
                    'isButton' => true,
                    'label' => '',
                    'icon' => 'news',
                ],
                [
                    'title' => 'Trợ giúp',
                    'url' => '/tro-giup',
                    'urlRegex' => '/^\/tro-giup.*/',
                    'isButton' => true,
                    'label' => '',
                    'icon' => 'support-1',
                ],
            ]
        ],
    ],
];
